## spring security 配置
security:
  oauth2:
    client:
      ignore-urls:
        - /webjars/**
        - /v3/api-docs/**
        - /doc.html
        - /swagger-resources
        - /token/check_token
        - /error
        - /druid/**
        - /actuator/**
        - /code/**

# 前端密码登录解密密钥
gateway:
  encodeKey: pigxpigxpigxpigx

# 数据源
spring:
  redis:
    host: 127.0.0.1
    database: 9  #避免和微服务 PIGX 冲突
    port: 6379
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      #password: Kj9mPx7nQw2L
      url: jdbc:mysql://*************:3306/paleoecology_db?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true
      #url: *************************************************************************************************************************************************************************************************************************************************************
      stat-view-servlet:
        enabled: true
        allow: ""
        url-pattern: /druid/*
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 10000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  mail:
    default-encoding: UTF-8
    host: smtp.qq.com
    username: <EMAIL>
    password: ppwbmettzfhqbgjf
    port: 465
    protocol: smtp
    properties:
      mail:
        smtp:
          ssl:
            enable: true
            trust: "*"
            protocols: TLSv1.2
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          auth: true
          timeout: 60000

# 本地文件系统
file:
  local:
    enable: true
    basePath: /home/<USER>/data/file

# Logger Config
logging:
  level:
    com.tdkj.tdcloud.admin.mapper: debug

# 租户表维护
tdcloud:
  tenant:
    column: tenant_id
    tables:
      - sys_user
      - sys_role
      - sys_menu
      - sys_dept
      - sys_log
      - sys_social_details
      - sys_dict
      - sys_dict_item
      - sys_public_param
      - sys_log
      - sys_file
      - sys_oauth_client_details
      - sys_post

