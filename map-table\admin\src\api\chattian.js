import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/chattian/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/chattian',
    method: 'post',
    data: obj
  })
}

export function getObj(cId) {
  return request({
    url: '/admin/chattian/getChattianByCId' ,
    method: 'get',
    params: {cId}
  })
}

export function delObj(id) {
  return request({
    url: '/admin/chattian/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/chattian',
    method: 'put',
    data: obj
  })
}
