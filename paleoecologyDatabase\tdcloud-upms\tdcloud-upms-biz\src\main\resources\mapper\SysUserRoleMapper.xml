<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdkj.tdcloud.admin.mapper.SysUserRoleMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.tdkj.tdcloud.admin.api.entity.SysUserRole">
		<id column="user_id" property="userId"/>
		<result column="role_id" property="roleId"/>
	</resultMap>

	<!--根据用户Id删除该用户的角色关系-->
	<delete id="deleteByUserId">
		DELETE FROM sys_user_role WHERE user_id = #{userId}
	</delete>

	<insert id="insertUserId" parameterType="com.tdkj.tdcloud.admin.api.entity.SysUserRole">
		insert into sys_user_role
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="userId != null">user_id,</if>
			<if test="roleId != null">role_id,</if>

		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userId != null">#{userId},</if>
			<if test="roleId != null">#{roleId},</if>
		</trim>
	</insert>


	<select id="selectRoleId" parameterType="String" resultType="Long">
		SELECT role_id roleId FROM sys_role WHERE role_code = #{roleCode}
	</select>
</mapper>
