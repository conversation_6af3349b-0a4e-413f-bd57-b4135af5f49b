<template>
  <div class="home-page">
    <!--
    <section class="project">
      <div class="project-intro">
        <div class="project-title">{{ $t('nav.paleoecologyNav') }}</div>
        <div  class="project-subtitle">{{ $t('home.homeSubTit') }}</div>
      </div>

      <el-carousel height="700px" :interval="5000" arrow="never">
        <el-carousel-item v-for="img in bannerList" :key="img.id">
          <img class="banner" :src="img.url" alt="">
        </el-carousel-item>
      </el-carousel>
    </section>
    -->
    <div class="home">

      <section class="home-database">
        <ul class="home-database-ul">
          <li class="database-item">
            <img @click="goMap" class="icon" src="../../assets/home/<USER>"/>
            <div class="name">{{$t('nav.paleoecologyNav')}}</div>
          </li>
          <li class="database-item">
            <img @click="goLeafCuticl" class="icon" src="../../assets/home/<USER>"/>
            <div class="name">{{$t('nav.leafCuticlNav')}}</div>
          </li>
        </ul>
      </section>
      <section class="home-statistics">
        <ul class="statistics-ul">
          <li class="statistics-item" v-for="statisticItem in statisticsList" :key="statisticItem.id">
            <div class="statistics-item-pic"><img class="pic-icon" :src="statisticItem.icon" alt=""></div>
            <div class="statistics-item-name">{{ $t(statisticItem.name) }}</div>
            <div class="statistics-item-number">{{ statisticItem.number }}</div>
          </li>
        </ul>
      </section>
      <section class="home-chart">
        <ul class="chart-ul">
          <li class="chart-item"><div id="category" style="width: 100%;height:100%;"></div></li>
          <li class="chart-item"><div id="pie" style="width: 100%;height:100%;"></div></li>
        </ul>
      </section>
    </div>
  </div>

</template>
<script>

import * as echarts from 'echarts';
import {getSpecimenCount, getCarouselImage} from "@/api/home";
export default {
  name: 'HomeIndex',
  components: {

  },
  data() {
    return {
      statisticsList: [
        {id: 1, name: 'home.specimenNum', number: '', icon: require('../../assets/home/<USER>')},
        {id: 2, name: 'home.samplingNum', number: '', icon: require('../../assets/home/<USER>')},
        {id: 3, name: 'home.distributionNum', number: '', icon: require('../../assets/home/<USER>')},
        {id: 4, name: 'home.publicationNum', number: '', icon: require('../../assets/home/<USER>')},
      ],
      rupelianEpochTotal: [],
      rupelianFossilTotal: [],
      bannerList: []
    };
  },
  mounted() {
    this.getInitData()

  },
  methods: {
    async getInitData() {
      let res = await getSpecimenCount()
      await this.getCarouselImage()
      if(res.data.code === 0) {
        this.statisticsList[0].number = res.data.data.specimenNum.toLocaleString()
        this.statisticsList[1].number = res.data.data.collectionPlaceTotal.toLocaleString()
        this.statisticsList[2].number = res.data.data.distributionTotal.toLocaleString()
        this.statisticsList[3].number = res.data.data.publicationTotal.toLocaleString()
        this.rupelianEpochTotal = res.data.data.rupelianEpochTotal
        this.rupelianFossilTotal = res.data.data.rupelianFossilTotal
      }
      await  this.getCategory()
      await  this.getPie()
    },
    //获取轮播图数据
    async getCarouselImage() {
      let result = await getCarouselImage()
      if(result.data.code === 0) {
        this.bannerList = result.data.data
      }else {
        this.$message.error(result.data.msg)
      }
    },
    //柱状图
    getCategory() {
      let chartDom = document.getElementById('category');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: 'Cenozoic distribution statistics',
          left: 50,
          top: 10,
          textStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#2C4A52'
          }
        },
        xAxis: {
          type: 'category',
          data: ['Paleocene', 'Eocene', 'Oligocene', 'Miocene', 'Pliocene', 'Pleistocene', 'Holocene'],
          axisLabel: {
            textStyle: {
              color: '#2C4A52', // 这里可以设置你想要的颜色，例如 '#333' 是深灰色
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#C8CACA'],
              type: 'solid'
            }
          }

        },
        yAxis: {
          type: 'value',
          axisTick: {
            length: 5,
            lineStyle: {
              color: '#C8CACA'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#2C4A52', // 这里可以设置你想要的颜色，例如 '#333' 是深灰色
              fontSize: 16
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#C8CACA'],
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: [this.rupelianEpochTotal.paleoceneCount, this.rupelianEpochTotal.eoceneCount,
              this.rupelianEpochTotal.oligoceneCount, this.rupelianEpochTotal.mioceneCount,
              this.rupelianEpochTotal.plioceneCount, this.rupelianEpochTotal.pleistoceneCount,
              this.rupelianEpochTotal.holoceneCount,],
            type: 'bar',
            itemStyle: {
              fontSize: 20,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#BB915D' }, // 起始颜色（深棕色）
                { offset: 1, color: '#324543' } // 结束颜色（浅棕色）
              ])
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    //饼图
    getPie() {
      let chartDom = document.getElementById('pie');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: 'Distribution of fossil types',
          left: 50,
          top: 10,
          textStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#2C4A52'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical', // 设置为垂直方向
          left: '5%',
          top: '20%',
          right: '20%',
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '75%'],
            center: ['70%', '50%'], // 通过调整 center 的值来改变饼图位置，从而拉开与图例的距离
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
              itemStyle: { color: '#2C4A52' }

            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold',
              }
            },
            labelLine: {
              show: false
            },
            data: this.rupelianFossilTotal,
            itemStyle: {
              color: function (params) {
                const colors = ['#924E4E','#45656E', '#528961', '#98725A' ];
                return colors[params.dataIndex % colors.length];
              }
            }
            // data: [
            //   { value: 400, name: 'Plant fossils',itemStyle: { color: '#45656E' }  },
            //   { value: 420, name: 'Pollen and spore fossils',itemStyle: { color: '#528961' } },
            //   { value: 50, name: 'Vertebrates' ,itemStyle: { color: '#98725A' }},
            //   { value: 484, name: 'Others',itemStyle: { color: '#924E4E' } },
            // ],

          }
        ]
      };

      option && myChart.setOption(option);
    },
    //跳转到古生态数据库
    goMap() {
      this.$router.push('/map')
    },
    //跳转到叶角质层数据库
    goLeafCuticl() {
      this.$router.push('/leafCuticle')
    }
  }
}
</script>

<style scoped lang="scss">
.home-page {
  margin-top: -180px;
  min-height: calc(100vh - 272px - 100px);
  background-color: var(--about-background-color);
  .project {
    //margin-bottom: 60px;
    //padding-top: 40px;
    height: 700px;
    position: relative;
    .banner {
      width: 100% ;
      height: 100%;
      object-fit: cover;
    }
    .project-intro {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 999;
      font-family: Source Han Sans, Source Han Sans;
      .project-title {
        font-weight: 700;
        font-size: 58px;
        color: var(--light-text-color);
      }
      .project-subtitle {
        font-size: 24px;
        color: var(--light-text-color);
      }
    }

  }
}
 ::v-deep .el-carousel__button {
  width: 40px;
  height: 10px;
  border-radius: 35px;
}
.home {
  //margin-top: 50px;
  padding: 50px 80px 0;

  .home-database {
    .home-database-ul{
      width:100%;
      display: flex;
      .database-item {
        width: calc(100% / 2);
        height: 335px;
        margin-right: 40px;
        background-image: url('../../assets/home/<USER>');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center ;
        position: relative;
        border-radius: 6px;
        &:last-child{
          margin-right: 0;
          background-image: url('../../assets/home/<USER>');
        }
        .icon {
          float: right;
          display: block;
          margin-right: 30px;
          cursor: pointer;
          margin-top: 20px;
        }
        .name {
          font-size: 32px;
          color: #ffffff;
          position: absolute;
          bottom: 30px;
          left: 30px;
        }
      }
    }
  }
  .home-statistics {
    margin-top: 50px;
    .statistics-ul {
      display: flex;
      align-items: center;
      .statistics-item {
        width: calc((100% - 117px) / 4);
        height: 447px;
        background: rgba(255,255,255,0.5);
        border-radius: 8px;
        margin-right: 39px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &:nth-child(4) {
          margin-right: 0;
        }
        .statistics-item-pic {
          width: 200px;
          height: 200px;
          .pic-icon {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
          }
        }
        .statistics-item-name {
          font-size: 24px;
          color: var(--home-text-color);
          margin: 20px 0;
        }
        .statistics-item-number {
          font-size: 36px;
          color: var(--home-text-color);

        }
      }
    }
  }
  .home-chart {
    margin-top: 50px;
    .chart-ul {
      display: flex;
      align-items: center;
      .chart-item {
        width: calc((100% - 39px) / 2);
        height: 457px;
        margin-right: 39px;
        border-radius: 8px;
        background: rgba(255,255,255,0.5);

        &:nth-child(2) {
          margin-right: 0;
        }
      }
    }
  }

}
</style>