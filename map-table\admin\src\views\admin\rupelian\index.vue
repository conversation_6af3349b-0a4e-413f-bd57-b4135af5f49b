<template>
  <div class="mod-config">
    <basic-container>
      <el-form :model="searchForm"  ref="searchForm" size="small"  class="demo-ruleForm" :inline="true" label-width="100px">
        <el-form-item label="Country" prop="country">
          <el-input  v-model="searchForm.country" clearable></el-input>
        </el-form-item>
        <el-form-item label="DatingMethod" prop="datingmethod">
          <el-input  v-model="searchForm.datingmethod" clearable></el-input>
        </el-form-item>
        <el-form-item label="DatingQuality" prop="datingquality">
          <el-input  v-model="searchForm.datingquality" clearable></el-input>
        </el-form-item>
        <el-form-item label="Epoch" prop="epoch">
          <el-select v-model="searchForm.epoch" style="width: 100%;" clearable placeholder="Epoch" @change="changeItem">
            <el-option v-for="item in EpochOp" :key="item.epoch_value" :label="item.epoch_value" :value="item.epoch_value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Stage" prop="stage">
          <el-select v-model="searchForm.stage" style="width: 100%;" clearable placeholder="Stage" @change="getDataList">
            <el-option v-for="item in StageOp" :key="item.stage_value" :label="item.stage_value" :value="item.stage_value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="EarlyInterval" prop="earlyInterval">
          <el-input  v-model="searchForm.earlyInterval" clearable></el-input>
        </el-form-item>
        <el-form-item label="LateInterval" prop="lateInterval">
          <el-input  v-model="searchForm.lateInterval" clearable></el-input>
        </el-form-item>
        <el-form-item label="AgeMax" prop="agemax">
          <el-input  v-model="searchForm.agemax" clearable></el-input>
        </el-form-item>
        <el-form-item label="AgeMin" prop="agemin">
          <el-input  v-model="searchForm.agemin" clearable></el-input>
        </el-form-item>
        <el-form-item label="Pubyr" prop="pubyr">
          <el-input  v-model="searchForm.pubyr" clearable></el-input>
        </el-form-item>
        <el-form-item label="TimeBin" prop="timebin">
          <el-input  v-model="searchForm.timebin" clearable></el-input>
        </el-form-item>
        <el-form-item label="FossilType" prop="fossiltype">
          <el-input  v-model="searchForm.fossiltype" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
          <el-button  icon="el-icon-upload" type="primary" plain @click="importExcel('parent')">导入</el-button>
          <el-button  icon="el-icon-upload" type="primary" plain @click="importChildExcel('child')">导入子表数据</el-button>
        </el-form-item>
      </el-form>

      <div class="avue-crud">
        <el-table :data="dataList" border v-loading="dataListLoading">
<!--          <el-table-column-->
<!--            prop="pid"-->
<!--            header-align="center"-->
<!--            align="center"-->
<!--            label="pid">-->
<!--          </el-table-column>-->
          <el-table-column fixed
            prop="id"
            header-align="center"
            align="center"
            label="ID">
          </el-table-column>
          <el-table-column
            prop="siteno"
            header-align="center"
            align="center"
            label="SiteNo">
          </el-table-column>
          <el-table-column width="160"
            prop="sitename"
            header-align="center"
            align="center"
            label="SiteName">
          </el-table-column>
<!--          <el-table-column-->
<!--            prop="source"-->
<!--            header-align="center"-->
<!--            align="center"-->
<!--            label="source">-->
<!--          </el-table-column>-->
<!--          <el-table-column-->
<!--            prop="sourceid"-->
<!--            header-align="center"-->
<!--            align="center"-->
<!--            label="sourceid">-->
<!--          </el-table-column>-->
<!--          <el-table-column width="180" show-overflow-tooltip-->
<!--            prop="collectionme"-->
<!--            header-align="center"-->
<!--            align="center"-->
<!--            label="collectionme">-->
<!--          </el-table-column>-->
          <el-table-column
            prop="country"
            header-align="center"
            align="center"
            label="Country">
          </el-table-column>
          <el-table-column width="180"
            prop="datingmethod"
            header-align="center"
            align="center"
            label="DatingMethod">
          </el-table-column>
          <el-table-column width="180"
            prop="datingquality"
            header-align="center"
            align="center"
            label="DatingQuality">
          </el-table-column>
          <el-table-column
            prop="epoch"
            header-align="center"
            align="center"
            label="Epoch">
          </el-table-column>
          <el-table-column
            prop="stage"
            header-align="center"
            align="center"
            label="Stage">
          </el-table-column>
          <el-table-column
            prop="earlyinterval" width="160"
            header-align="center"
            align="center"
            label="EarlyInterval">
          </el-table-column>
          <el-table-column
            prop="lateinterval" width="160"
            header-align="center"
            align="center"
            label="LateInterval">
          </el-table-column>
          <el-table-column
            prop="agemax"
            header-align="center"
            align="center"
            label="AgeMax">
          </el-table-column>
          <el-table-column
            prop="agemin"
            header-align="center"
            align="center"
            label="AgeMin">
          </el-table-column>
          <el-table-column
            prop="agemiddle" width="160"
            header-align="center"
            align="center"
            label="AgeMiddle">
          </el-table-column>
          <el-table-column
            prop="author" width="180" show-overflow-tooltip
            header-align="center"
            align="center"
            label="Author">
          </el-table-column>
          <el-table-column
            prop="pubyr"
            header-align="center"
            align="center"
            label="Pubyr">
          </el-table-column>
          <el-table-column
            prop="longitude"
            header-align="center"
            align="center" width="180"
            label="Longitude">
          </el-table-column>
          <el-table-column width="180"
            prop="latitude"
            header-align="center"
            align="center"
            label="Latitude">
          </el-table-column>
          <el-table-column
            prop="timebin"
            header-align="center"
            align="center"
            label="TimeBin">
          </el-table-column>
          <el-table-column
            prop="fossiltype"
            header-align="center"
            align="center"
            label="FossilType">
          </el-table-column>
          <el-table-column width="160"
            prop="pollendiagram"
            header-align="center"
            align="center"
            label="PollenDiagram">
          </el-table-column>
          <el-table-column show-overflow-tooltip
            prop="reference1" width="180"
            header-align="center"
            align="center"
            label="Reference1">
          </el-table-column>
          <el-table-column show-overflow-tooltip
            prop="reference2" width="180"
            header-align="center"
            align="center"
            label="Reference2">
          </el-table-column>
          <el-table-column show-overflow-tooltip
            prop="reference3" width="180"
            header-align="center"
            align="center"
            label="Reference3">
          </el-table-column>
          <el-table-column width="180"
            prop="otherreferences"
            header-align="center"
            align="center"
            label="other Reference">
          </el-table-column>
          <el-table-column width="180" fixed="right"
            header-align="center"
            align="center"
            label="操作">
            <template slot-scope="scope">
              <el-button  type="text" size="small" icon="el-icon-view" @click="viewHandle(scope.row.id)">查看子表信息</el-button>
              <el-button v-if="permissions.admin_rupelian_edit" type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.pid)">修改</el-button>
              <el-button v-if="permissions.admin_rupelian_del" type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.pid)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
      <el-dialog
        :title="type === 'parent' ? '导入化石组合数据信息表' : '导入化石分类群数据信息表'" center
        :visible.sync="isImportData"
        width="50%" @close="getDataList"
        :close-on-click-modal="false"
        append-to-body >
        <ImportData v-if="type === 'parent'" ref="importData" :importType="'rupelian'" :searchForm="searchForm" :export-file-name="'采集地数据信息'"
                    @exportSuccess="exportSuccess"  @errorSuccess="isImportData = false" v-on="$listeners"/>
        <ImportData v-else  ref="importData" :importType="'chattian'" :searchForm="searchForm" :export-file-name="'采集地子表数据信息'"
                    @exportSuccess="exportSuccess"  @errorSuccess="isImportData = false" v-on="$listeners"/>
      </el-dialog>
      <ChildTable v-if="childVisible" ref="child" :parentId="parentId"></ChildTable>
    </basic-container>
  </div>
</template>

<script>
  import {fetchList, delObj} from '@/api/rupelian'
  import TableForm from './rupelian-form'
  import ImportData from '@/components/importData/index'
  import ChildTable from './child-table'
  import {mapGetters} from 'vuex'

  export default {
    data() {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false,
        isImportData: false,
        type: 'parent',
        parentId: null,
        childVisible: false,
        EpochOp:[
          {id: 1, epoch_value: 'Paleocene',epoch_name: '古新统'},
          {id: 2, epoch_value: 'Eocene',epoch_name: '始新统'},
          {id: 3, epoch_value: 'Oligocene',epoch_name: '渐新统'},
          {id: 4, epoch_value: 'Miocene',epoch_name: '中新统'},
          {id: 5, epoch_value: 'Pliocene',epoch_name: '上新统'},
          {id: 6, epoch_value: 'Pleistocene',epoch_name: '更新统'},
          {id: 7, epoch_value: 'Holocene',epoch_name: '全新统'},
        ],
        StageOpAll:[
          {id: 11, stage_name: '丹麦阶',stage_value: 'Danian',parentId:'Paleocene'},
          {id: 12, stage_name: '塞兰特阶',stage_value: 'Selandian',parentId:'Paleocene'},
          {id: 13, stage_name: '坦尼特阶',stage_value: 'Thanetian',parentId:'Paleocene'},
          {id: 14, stage_name: '伊普里斯阶',stage_value: 'Ypresian',parentId:'Eocene'},
          {id: 15, stage_name: '卢泰特阶',stage_value: 'Lutetian',parentId:'Eocene'},
          {id: 16, stage_name: '巴顿阶',stage_value: 'Bartonian',parentId:'Eocene'},
          {id: 17, stage_name: '普利亚本阶',stage_value: 'Priabonian',parentId:'Eocene'},
          {id: 18, stage_name: '吕珀尔阶',stage_value: 'Rupelian',parentId:'Oligocene'},
          {id: 19, stage_name: '夏特阶',stage_value: 'Chattian',parentId:'Oligocene'},
          {id: 20, stage_name: '阿基坦阶',stage_value: 'Aquitanian',parentId:'Miocene'},
          {id: 21, stage_name: '波尔多阶',stage_value: 'Burdigalian',parentId:'Miocene'},
          {id: 22, stage_name: '兰盖阶',stage_value: 'Langhian',parentId:'Miocene'},
          {id: 23, stage_name: '塞拉瓦莱阶',stage_value: 'Serravallian',parentId:'Miocene'},
          {id: 24, stage_name: '托尔托纳阶',stage_value: 'Tortonian',parentId:'Miocene'},
          {id: 25, stage_name: '墨西拿阶',stage_value: 'Messinian',parentId:'Miocene'},
          {id: 26, stage_name: '赞克勒阶',stage_value: 'Zanclean',parentId:'Pliocene'},
          {id: 27, stage_name: '皮亚琴察阶',stage_value: 'Piacenzian',parentId:'Pliocene'},
          {id: 28, stage_name: '杰拉阶',stage_value: 'Gelasian',parentId:'Pleistocene'},
          {id: 29, stage_name: '卡拉布里雅阶',stage_value: 'Calabrian',parentId:'Pleistocene'},
          {id: 30, stage_name: '千叶阶',stage_value: 'Chibanian',parentId:'Pleistocene'},
          {id: 31, stage_name: '上阶',stage_value: 'Upper',parentId:'Pleistocene'},
          {id: 32, stage_name: '格陵兰阶',stage_value: 'Greenlandian',parentId:'Holocene'},
          {id: 33, stage_name: '诺斯格瑞比阶',stage_value: 'Northgrippian',parentId:'Holocene'},
          {id: 34, stage_name: '梅加拉亚阶',stage_value: 'Meghalayan',parentId:'Holocene'},
        ],
        StageOp:[]
      }
    },
    components: {
      TableForm,
      ImportData,
      ChildTable
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {
      this.getDataList()
    },
    methods: {
      changeItem(val) {
        this.StageOp = this.StageOpAll.filter(item => item.parentId === val);
        this.pageIndex = 1
        this.getDataList()
      },
      submitForm() {
        this.pageIndex = 1
        this.getDataList()
      },
      resetForm() {
        this.searchForm = {}

        this.getDataList()
      },
      viewHandle(id) {
        this.parentId = id
        this.childVisible = true
        this.$nextTick(() => {
          this.$refs.child.getChildTableList(id)
        })
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        },this.searchForm)).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
      this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        this.$confirm('是否确认删除ID为' + id, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.$message.success('删除成功')
          this.getDataList()
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/admin/rupelian/export', this.searchForm, 'rupelian.xlsx')
      },
      //导入生物分类层级表
      importExcel(val) {
        this.isImportData = true
        this.type = val
      },
      importChildExcel(val) {
        this.isImportData = true
        this.type = val
      },
      //导入成功
      exportSuccess() {
        this.isImportData = false
        this.getDataList()
      },
    }
  }
</script>
