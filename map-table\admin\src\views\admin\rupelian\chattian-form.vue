<template>
  <el-dialog width="70%" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" append-to-body :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-position="top" :inline="true">
    <el-form-item label="OriginalName" prop="originalname">
      <el-input v-model="dataForm.originalname" placeholder="originalname"></el-input>
    </el-form-item>
    <el-form-item label="ScientificName1" prop="scientificname1">
      <el-input v-model="dataForm.scientificname1" placeholder="scientificname1"></el-input>
    </el-form-item>
    <el-form-item label="ScientificName2" prop="scientificname2">
      <el-input v-model="dataForm.scientificname2" placeholder="scientificname2"></el-input>
    </el-form-item>
    <el-form-item label="Scientificname3" prop="scientificname3">
      <el-input v-model="dataForm.scientificname3" placeholder="scientificname3"></el-input>
    </el-form-item>
    <el-form-item label="AcceptedRank" prop="acceptedrank">
      <el-input v-model="dataForm.acceptedrank" placeholder="acceptedrank"></el-input>
    </el-form-item>
    <el-form-item label="Phylum" prop="phylum">
      <el-input v-model="dataForm.phylum" placeholder="phylum"></el-input>
    </el-form-item>
    <el-form-item label="Class" prop="classNew">
      <el-input v-model="dataForm.class" placeholder="class"></el-input>
    </el-form-item>
    <el-form-item label="Order" prop="orderNew">
      <el-input v-model="dataForm.order" placeholder="order"></el-input>
    </el-form-item>
    <el-form-item label="Family" prop="family">
      <el-input v-model="dataForm.family" placeholder="family"></el-input>
    </el-form-item>
    <el-form-item label="Genus" prop="genus">
      <el-input v-model="dataForm.genus" placeholder="genus"></el-input>
    </el-form-item>
    <el-form-item label="Species1" prop="species1">
      <el-input v-model="dataForm.species1" placeholder="species1"></el-input>
    </el-form-item>
    <el-form-item label="Species2" prop="species2">
      <el-input v-model="dataForm.species2" placeholder="species2"></el-input>
    </el-form-item>
    <el-form-item label="Species3" prop="species3">
      <el-input v-model="dataForm.species3" placeholder="species3"></el-input>
    </el-form-item>
    <el-form-item label="PlantOrgan1" prop="plantorgan1">
      <el-input v-model="dataForm.plantorgan1" placeholder="plantorgan1"></el-input>
    </el-form-item>
    <el-form-item label="PlantOrgan2" prop="plantorgan2">
      <el-input v-model="dataForm.plantorgan2" placeholder="plantorgan2"></el-input>
    </el-form-item>
    <el-form-item label="AbundValue" prop="abundvalue">
      <el-input v-model="dataForm.abundvalue" placeholder="abundvalue"></el-input>
    </el-form-item>
    <el-form-item label="AbundUnit" prop="abundunit">
      <el-input v-model="dataForm.abundunit" placeholder="abundunit"></el-input>
    </el-form-item>
    <el-form-item label="FossilType" prop="fossiltype">
      <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
    </el-form-item>
      <el-form-item label="PollenDiagram" prop="pollendiagram">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
      <el-form-item label="SiteNo" prop="siteno">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
      <el-form-item label="SiteName" prop="sitename">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
      <el-form-item label="Extinct" prop="extinct">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
      <el-form-item label="TimeContext" prop="timecontext">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/chattian'

  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
                id: '',
                originalname: '',
                scientificname1: '',
                scientificname2: '',
                scientificname3: '',
                acceptedrank: '',
                phylum: '',
                classNew: '',
                order: '',
                family: '',
                genus: '',
                species1: '',
                species2: '',
                species3: '',
                plantorgan1: '',
                plantorgan2: '',
                abundvalue: '',
                abundunit: '',
                fossiltype: '',
        },
        dataRule: {
                id: [
                {required: false, message: 'id不能为空', trigger: 'blur'}
              ],
                originalname: [
                {required: false, message: 'originalname不能为空', trigger: 'blur'}
              ],
                scientificname1: [
                {required: false, message: 'scientificname1不能为空', trigger: 'blur'}
              ],
                scientificname2: [
                {required: false, message: 'scientificname2不能为空', trigger: 'blur'}
              ],
                scientificname3: [
                {required: false, message: 'scientificname3不能为空', trigger: 'blur'}
              ],
                acceptedrank: [
                {required: false, message: 'acceptedrank不能为空', trigger: 'blur'}
              ],
                phylum: [
                {required: false, message: 'phylum不能为空', trigger: 'blur'}
              ],
          classNew: [
                {required: false, message: 'class不能为空', trigger: 'blur'}
              ],
                order: [
                {required: false, message: 'order不能为空', trigger: 'blur'}
              ],
                family: [
                {required: false, message: 'family不能为空', trigger: 'blur'}
              ],
                genus: [
                {required: false, message: 'genus不能为空', trigger: 'blur'}
              ],
                species1: [
                {required: false, message: 'species1不能为空', trigger: 'blur'}
              ],
                species2: [
                {required: false, message: 'species2不能为空', trigger: 'blur'}
              ],
                species3: [
                {required: false, message: 'species3不能为空', trigger: 'blur'}
              ],
                plantorgan1: [
                {required: false, message: 'plantorgan1不能为空', trigger: 'blur'}
              ],
                plantorgan2: [
                {required: false, message: 'plantorgan2不能为空', trigger: 'blur'}
              ],
                abundvalue: [
                {required: false, message: 'abundvalue不能为空', trigger: 'blur'}
              ],
                abundunit: [
                {required: false, message: 'abundunit不能为空', trigger: 'blur'}
              ],
                fossiltype: [
                {required: false, message: 'fossiltype不能为空', trigger: 'blur'}
              ],
        }
      }
    },
    methods: {
      init(id) {
        console.log(id)
        this.dataForm.id = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
