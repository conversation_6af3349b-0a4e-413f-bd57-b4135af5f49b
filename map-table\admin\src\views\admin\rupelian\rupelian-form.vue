<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-position="top">
      <el-form-item label="SiteNo" prop="siteno">
        <el-input v-model="dataForm.siteno" placeholder="siteno"></el-input>
      </el-form-item>
      <el-form-item label="SiteName" prop="sitename">
        <el-input v-model="dataForm.sitename" placeholder="sitename"></el-input>
      </el-form-item>
      <el-form-item label="Country" prop="country">
        <el-input v-model="dataForm.country" placeholder="country"></el-input>
      </el-form-item>
      <el-form-item label="DatingMethod" prop="datingmethod">
        <el-input v-model="dataForm.datingmethod" placeholder="datingmethod"></el-input>
      </el-form-item>
      <el-form-item label="DatingQuality" prop="datingquality">
        <el-input v-model="dataForm.datingquality" placeholder="datingquality"></el-input>
      </el-form-item>
      <el-form-item label="Epoch" prop="epoch">
        <el-select v-model="dataForm.epoch" style="width: 100%;" clearable placeholder="Epoch" @change="changeItem">
          <el-option v-for="item in EpochOp" :key="item.epoch_value" :label="item.epoch_value" :value="item.epoch_value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Stage" prop="stage">
        <el-select v-model="dataForm.stage" style="width: 100%;" clearable placeholder="Stage" >
          <el-option v-for="item in StageOp" :key="item.stage_value" :label="item.stage_value" :value="item.stage_value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="EarlyInterval" prop="earlyinterval">
        <el-input v-model="dataForm.earlyinterval" placeholder="earlyinterval"></el-input>
      </el-form-item>
      <el-form-item label="LateInterval" prop="lateinterval">
        <el-input v-model="dataForm.lateinterval" placeholder="lateinterval"></el-input>
      </el-form-item>
      <el-form-item label="AgeMax" prop="agemax">
        <el-input v-model="dataForm.agemax" placeholder="agemax"></el-input>
      </el-form-item>
      <el-form-item label="AgeMin" prop="agemin">
        <el-input v-model="dataForm.agemin" placeholder="agemin"></el-input>
      </el-form-item>
      <el-form-item label="AgeMiddle" prop="agemiddle">
        <el-input v-model="dataForm.agemiddle" placeholder="agemiddle"></el-input>
      </el-form-item>
      <el-form-item label="Author" prop="author">
        <el-input v-model="dataForm.author" placeholder="author"></el-input>
      </el-form-item>
      <el-form-item label="Pubyr" prop="pubyr">
        <el-input v-model="dataForm.pubyr" placeholder="pubyr"></el-input>
      </el-form-item>
      <el-form-item label="Longitude" prop="longitude">
        <el-input v-model="dataForm.longitude" placeholder="longitude"></el-input>
      </el-form-item>
      <el-form-item label="Latitude" prop="latitude">
        <el-input v-model="dataForm.latitude" placeholder="latitude"></el-input>
      </el-form-item>
      <el-form-item label="TimeBin" prop="timebin">
        <el-input v-model="dataForm.timebin" placeholder="timebin"></el-input>
      </el-form-item>
      <el-form-item label="FossilType" prop="fossiltype">
        <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
      </el-form-item>
      <el-form-item label="PollenDiagram" prop="pollendiagram">
        <el-input v-model="dataForm.pollendiagram" placeholder="pollendiagram"></el-input>
      </el-form-item>

      <el-form-item label="Reference1" prop="reference1">
        <el-input v-model="dataForm.reference1" placeholder="reference1"></el-input>
      </el-form-item>
      <el-form-item label="Reference2" prop="reference2">
        <el-input v-model="dataForm.reference2" placeholder="reference2"></el-input>
      </el-form-item>
      <el-form-item label="Reference3" prop="reference3">
        <el-input v-model="dataForm.reference3" placeholder="reference3"></el-input>
      </el-form-item>
      <el-form-item label="other Reference" prop="otherreferences">
        <el-input v-model="dataForm.otherreferences" placeholder="otherreferences"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/rupelian'

  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          pid: 0,
          id: '',
          siteno: '',
          sitename: '',
          source: '',
          sourceid: '',
          collectionme: '',
          country: '',
          datingmethod: '',
          datingquality: '',
          epoch: '',
          stage: '',
          earlyinterval: '',
          lateinterval: '',
          agemax: '',
          agemin: '',
          agemiddle: '',
          author: '',
          pubyr: '',
          longitude: '',
          latitude: '',
          timebin: '',
          fossiltype: '',
          reference1: '',
          reference2: '',
          reference3: '',
          otherreferences: '',
        },
        dataRule: {
          id: [
            {required: false, message: 'id不能为空', trigger: 'blur'}
          ],
          siteno: [
            {required: false, message: 'siteno不能为空', trigger: 'blur'}
          ],
          sitename: [
            {required: false, message: 'sitename不能为空', trigger: 'blur'}
          ],
          country: [
            {required: false, message: 'country不能为空', trigger: 'blur'}
          ],
          datingmethod: [
            {required: false, message: 'datingmethod不能为空', trigger: 'blur'}
          ],
          datingquality: [
            {required: false, message: 'datingquality不能为空', trigger: 'blur'}
          ],
          epoch: [
            {required: false, message: 'epoch不能为空', trigger: 'blur'}
          ],
          stage: [
            {required: false, message: 'stage不能为空', trigger: 'blur'}
          ],
          earlyinterval: [
            {required: false, message: 'earlyinterval不能为空', trigger: 'blur'}
          ],
          lateinterval: [
            {required: false, message: 'lateinterval不能为空', trigger: 'blur'}
          ],
          agemax: [
            {required: false, message: 'agemax不能为空', trigger: 'blur'}
          ],
          agemin: [
            {required: false, message: 'agemin不能为空', trigger: 'blur'}
          ],
          agemiddle: [
            {required: false, message: 'agemiddle不能为空', trigger: 'blur'}
          ],
          author: [
            {required: false, message: 'author不能为空', trigger: 'blur'}
          ],
          pubyr: [
            {required: false, message: 'pubyr不能为空', trigger: 'blur'}
          ],
          longitude: [
            {required: false, message: 'longitude不能为空', trigger: 'blur'}
          ],
          latitude: [
            {required: false, message: 'latitude不能为空', trigger: 'blur'}
          ],
          timebin: [
            {required: false, message: 'timebin不能为空', trigger: 'blur'}
          ],
          fossiltype: [
            {required: false, message: 'fossiltype不能为空', trigger: 'blur'}
          ],
          reference1: [
            {required: false, message: 'reference1不能为空', trigger: 'blur'}
          ],
          reference2: [
            {required: false, message: 'reference2不能为空', trigger: 'blur'}
          ],
          reference3: [
            {required: false, message: 'reference3不能为空', trigger: 'blur'}
          ],
          otherreferences: [
            {required: false, message: 'otherreferences不能为空', trigger: 'blur'}
          ],
        },
        EpochOp:[
          {id: 1, epoch_value: 'Paleocene',epoch_name: '古新统'},
          {id: 2, epoch_value: 'Eocene',epoch_name: '始新统'},
          {id: 3, epoch_value: 'Oligocene',epoch_name: '渐新统'},
          {id: 4, epoch_value: 'Miocene',epoch_name: '中新统'},
          {id: 5, epoch_value: 'Pliocene',epoch_name: '上新统'},
          {id: 6, epoch_value: 'Pleistocene',epoch_name: '更新统'},
          {id: 7, epoch_value: 'Holocene',epoch_name: '全新统'},
        ],
        StageOpAll:[
          {id: 11, stage_name: '丹麦阶',stage_value: 'Danian',parentId:'Paleocene'},
          {id: 12, stage_name: '塞兰特阶',stage_value: 'Selandian',parentId:'Paleocene'},
          {id: 13, stage_name: '坦尼特阶',stage_value: 'Thanetian',parentId:'Paleocene'},
          {id: 14, stage_name: '伊普里斯阶',stage_value: 'Ypresian',parentId:'Eocene'},
          {id: 15, stage_name: '卢泰特阶',stage_value: 'Lutetian',parentId:'Eocene'},
          {id: 16, stage_name: '巴顿阶',stage_value: 'Bartonian',parentId:'Eocene'},
          {id: 17, stage_name: '普利亚本阶',stage_value: 'Priabonian',parentId:'Eocene'},
          {id: 18, stage_name: '吕珀尔阶',stage_value: 'Rupelian',parentId:'Oligocene'},
          {id: 19, stage_name: '夏特阶',stage_value: 'Chattian',parentId:'Oligocene'},
          {id: 20, stage_name: '阿基坦阶',stage_value: 'Aquitanian',parentId:'Miocene'},
          {id: 21, stage_name: '波尔多阶',stage_value: 'Burdigalian',parentId:'Miocene'},
          {id: 22, stage_name: '兰盖阶',stage_value: 'Langhian',parentId:'Miocene'},
          {id: 23, stage_name: '塞拉瓦莱阶',stage_value: 'Serravallian',parentId:'Miocene'},
          {id: 24, stage_name: '托尔托纳阶',stage_value: 'Tortonian',parentId:'Miocene'},
          {id: 25, stage_name: '墨西拿阶',stage_value: 'Messinian',parentId:'Miocene'},
          {id: 26, stage_name: '赞克勒阶',stage_value: 'Zanclean',parentId:'Pliocene'},
          {id: 27, stage_name: '皮亚琴察阶',stage_value: 'Piacenzian',parentId:'Pliocene'},
          {id: 28, stage_name: '杰拉阶',stage_value: 'Gelasian',parentId:'Pleistocene'},
          {id: 29, stage_name: '卡拉布里雅阶',stage_value: 'Calabrian',parentId:'Pleistocene'},
          {id: 30, stage_name: '千叶阶',stage_value: 'Chibanian',parentId:'Pleistocene'},
          {id: 31, stage_name: '上阶',stage_value: 'Upper',parentId:'Pleistocene'},
          {id: 32, stage_name: '格陵兰阶',stage_value: 'Greenlandian',parentId:'Holocene'},
          {id: 33, stage_name: '诺斯格瑞比阶',stage_value: 'Northgrippian',parentId:'Holocene'},
          {id: 34, stage_name: '梅加拉亚阶',stage_value: 'Meghalayan',parentId:'Holocene'},
        ],
        StageOp:[]
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      changeItem(val) {
        this.StageOp = this.StageOpAll.filter(item => item.parentId === val);
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
