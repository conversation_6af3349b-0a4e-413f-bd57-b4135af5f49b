<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f13de875-0583-4752-9601-49baf473c9cd" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\Users\<USER>\Documents\apache-maven-3.8.4\repository" />
        <option name="mavenHome" value="$USER_HOME$/Documents/apache-maven-3.8.4" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="C:\Users\<USER>\Documents\apache-maven-3.8.4\conf\settings.xml" />
        <option name="workOffline" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2r4EBRU6WGJ9Nb81reloYgfMndy" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../../../../动物所/Fw_pigxv4.1fortdcloud/tdcloud" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="project.structure.last.edited" value="Modules" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.0" />
    <property name="settings.editor.selected.configurable" value="reference.projectsettings.compiler.javacompiler" />
  </component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\java\project\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz\src\main\java\com\tdkj\tdcloud\admin\mapper" />
      <recent name="D:\java\project\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz\src\main\java\com\tdkj\tdcloud\admin\service\impl" />
      <recent name="D:\java\project\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz\src\main\java\com\tdkj\tdcloud\admin\service" />
      <recent name="D:\java\project\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-api\src\main\java\com\tdkj\tdcloud\admin\api\entity" />
      <recent name="D:\java\project\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz\src\main\java\com\tdkj\tdcloud\admin\controller" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.tdkj.tdcloud.admin.api.dto" />
      <recent name="com.tdkj.tdcloud.admin.service.impl" />
      <recent name="com.tdkj.tdcloud.admin.service" />
      <recent name="com.tdkj.tdcloud.admin.api.entity" />
      <recent name="com.tdkj.tdcloud.admin.controller" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn clean" />
      <command value="mvn clean package -Dmaven.test.skip=true" />
      <command value="mvn clean install" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.TdcloudAdminApplication">
    <configuration name="SysUserServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.tdkj.tdcloud.admin.service.impl.SysUserServiceImpl" />
      <module name="tdcloud-upms-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.tdkj.tdcloud.admin.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TdcloudAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tdcloud-upms-biz" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tdkj.tdcloud.TdcloudAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TdcloudCodeGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="tdcloud-codegen" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.tdkj.tdcloud.codegen.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tdkj.tdcloud.codegen.TdcloudCodeGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.TdcloudCodeGenApplication" />
        <item itemvalue="Application.SysUserServiceImpl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f13de875-0583-4752-9601-49baf473c9cd" name="Default Changelist" comment="" />
      <created>1735809811927</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735809811927</updated>
      <workItem from="1735809815060" duration="24798000" />
      <workItem from="1736212208372" duration="56000" />
      <workItem from="1736212276764" duration="61731000" />
      <workItem from="1736989769393" duration="93000" />
      <workItem from="1739950940102" duration="2448000" />
      <workItem from="1741588440583" duration="10860000" />
      <workItem from="1741671067023" duration="17000" />
      <workItem from="1741671116159" duration="47310000" />
      <workItem from="1741914623358" duration="10947000" />
      <workItem from="1742180836274" duration="677000" />
      <workItem from="1742195154481" duration="597000" />
      <workItem from="1742455853128" duration="1634000" />
      <workItem from="1742524990394" duration="29330000" />
      <workItem from="1743056826114" duration="4391000" />
      <workItem from="1743124367340" duration="936000" />
      <workItem from="1743405542992" duration="632000" />
      <workItem from="1743993496796" duration="29100000" />
      <workItem from="1744092492828" duration="7991000" />
      <workItem from="1744939775412" duration="13735000" />
      <workItem from="1746778785113" duration="1675000" />
      <workItem from="1747033809819" duration="1504000" />
      <workItem from="1747099539343" duration="1251000" />
      <workItem from="1747201458873" duration="56000" />
      <workItem from="1747625291601" duration="5924000" />
      <workItem from="1747721744474" duration="4064000" />
      <workItem from="1747892217271" duration="3168000" />
      <workItem from="1748921902833" duration="799000" />
      <workItem from="1748924638918" duration="1908000" />
      <workItem from="1748937359663" duration="2476000" />
      <workItem from="1749017360752" duration="4446000" />
      <workItem from="1749623217966" duration="760000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>