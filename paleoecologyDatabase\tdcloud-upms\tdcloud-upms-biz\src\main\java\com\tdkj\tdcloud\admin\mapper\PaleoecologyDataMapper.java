/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.dto.PaleoecologyDataDTO;
import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.api.entity.Rupelian;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Paleoecology Data Mapper Interface
 * MyBatis mapper for complex paleoecology data queries
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Mapper
public interface PaleoecologyDataMapper {

    /**
     * Complex data query with dynamic filtering
     * Equivalent to Python Flask /data endpoint query
     *
     * @param queryParams Query parameters
     * @return List of filtered results
     */
    List<Map<String, Object>> selectPaleoecologyData(@Param("params") PaleoecologyDataDTO queryParams);

    /**
     * Get detail data from Chattian table by ID
     *
     * @param id Record ID
     * @return List of Chattian records
     */
    List<Chattian> selectChattianDetailById(@Param("id") String id);

    /**
     * Get unique dating method values from Rupelian table
     *
     * @return List of unique dating methods
     */
    List<String> selectUniqueDatingMethods();

    /**
     * Get unique dating quality values from Rupelian table
     *
     * @return List of unique dating qualities
     */
    List<String> selectUniqueDatingQualities();

    /**
     * Get unique family values from Chattian table
     *
     * @return List of unique families
     */
    List<String> selectUniqueFamilies();

    /**
     * Get unique genus values from Chattian table
     *
     * @return List of unique genera
     */
    List<String> selectUniqueGenera();

    /**
     * Get unique species values from Chattian table
     * Combines Species1, Species2, and Species3 columns
     *
     * @return List of unique species
     */
    List<String> selectUniqueSpecies();

    /**
     * Get unique scientific name values from Chattian table
     * Combines ScientificName1, ScientificName2, and ScientificName3 columns
     *
     * @return List of unique scientific names
     */
    List<String> selectUniqueScientificNames();

    /**
     * Get unique original name values from Chattian table
     *
     * @return List of unique original names
     */
    List<String> selectUniqueOriginalNames();

    /**
     * Get unique fossil type values from Rupelian table
     *
     * @return List of unique fossil types
     */
    List<String> selectUniqueFossilTypes();

    /**
     * Get unique plant organ values from Chattian table
     * Combines PlantOrgan1 and PlantOrgan2 columns
     *
     * @return List of unique plant organs
     */
    List<String> selectUniquePlantOrgans();
}
