<template>
  <div class="forgot-panle">
    <div class="forgot-panle-title">{{$t('nav.paleoecologyNav')}}</div>
    <el-form ref="forgotForm" class="forgotForm" :model="forgotForm" :rules="forgotRules">

      <div class="form_item email-group">
        <el-form-item prop="username" class="email-group-item">
          <el-input  @blur="validateEmail"  v-model="forgotForm.username" :placeholder="$t('login.emailPlaceholder')"></el-input>
        </el-form-item>
        <el-button class="email-group-btn" type="primary"  :disabled="isDisabled || !isEmailValid" @click="sendVerificationCode">
          <span v-if="countdown > 0">{{ countdown }} 秒</span>
          <span v-else>{{$t('login.codeText')}}</span>
        </el-button>
      </div>
      <div class="form_item">
        <el-form-item prop="code">
          <el-input v-model="forgotForm.code" :placeholder="$t('login.codePlaceholder')"></el-input>
        </el-form-item>
      </div>

      <div class="form_item">
        <el-form-item prop="password">
          <el-input
              auto-complete="off"
              v-model="forgotForm.password"
              :type="passwordType"
              :placeholder="$t('login.newPassPlaceholder')">
            <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
            <i slot="prefix" class="icon-mima icon"></i>
          </el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="rePassword">
          <el-input v-model="forgotForm.rePassword" type="password" :placeholder="$t('login.rePassPlaceholder')"></el-input>
        </el-form-item>
      </div>

      <div class="forgot-btns">
        <div class="btn cancel" @click="cancelHandle">{{$t('login.cancelBtn')}}</div>
        <div class="btn login" @click="handleForgot">{{$t('login.forgotNote')}}</div>
      </div>
    </el-form>
  </div>
</template>

<script>
import {rule} from "@/util/validateRules";
import {mapMutations} from "vuex";
import {getEmailCode, updateUserPassword} from '@/api/login'
import webiste from "@/util/website";
import {aesEncrypt} from "@/util/encryption";
export default {
  name: "ForgotPanle",
  data(){
    return {
      countdown: 0,      // 倒计时秒数
      isEmailValid: false, // 邮箱是否有效
      timer: null ,       // 计时器对象
      forgotForm: {
        username: '',
        code: '',
        password: '',
        rePassword: '',
      },
      forgotRules: {
        username: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {validator: rule.validatorEmail,trigger: 'blur'}
        ],
        code: [
          { required: true, message: '验证码不能为空', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'}
        ],
        // password: [
        //   { required: true, message: '请输入密码', trigger: 'blur' },
        //   {validator: rule.checkSpace,trigger: 'blur'},
        //   {validator: rule.validatorPassword,trigger: 'blur'}
        //
        // ],
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          // {validator: rule.checkSpace,trigger: 'blur'},
          {validator: rule.validatorPassword,trigger: 'blur'}

        ],
        rePassword: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value !== this.forgotForm.password) {
                callback(new Error('两次输入的密码不一致!'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'] // 在输入和字段变化时都触发验证
          }
        ],
      },
      passwordType: "password",
    }
  },
  computed: {
    isDisabled() {
      return this.countdown > 0
    }
  },
  methods: {
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    ...mapMutations(['SET_OPEN_FORGOT_BOX']),
    cancelHandle() {
      this.$refs.forgotForm.resetFields();
      this.SET_OPEN_FORGOT_BOX(false);
    },
    showPassword() {
      this.passwordType === ""
          ? (this.passwordType = "password")
          : (this.passwordType = "");
    },
    // 邮箱格式验证
    validateEmail() {
      const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      this.isEmailValid = pattern.test(this.forgotForm.username)
      return this.isEmailValid
    },
    // 发送验证码
    sendVerificationCode() {
      if (!this.validateEmail()) {
        this.$message.error('请输入有效的邮箱地址')
        return
      }
      this.startCountdown()
      // TODO 调用API发送验证码（示例）
      getEmailCode(this.forgotForm.username,'', 'forgetPassword').then(res => {
        if(res.data.code === 0) {
          this.$message.success(res.data.data)
        }else {
          this.$message.warning(res.data.msg)
        }
      })

    },
    // 开始倒计时
    startCountdown(seconds = 60) {
      // 清除已有计时器
      if (this.timer) clearInterval(this.timer)
      // 设置初始值
      this.countdown = seconds
      // 启动计时器
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    handleForgot() {
      this.$refs.forgotForm.validate(valid => {
        if (valid) {
          //TODO  1.密码加密 2.重置密码
          if (webiste.passwordEnc) {
            this.forgotForm.password = aesEncrypt(this.forgotForm.password, webiste.encPassword)
            this.forgotForm.repassword = aesEncrypt(this.forgotForm.repassword, webiste.encPassword)
          }
          updateUserPassword(this.forgotForm).then(res => {
            if(res.data.code === 0) {
              this.$message.success('密码重置成功！')
              //关闭忘记密码弹框
              this.SET_OPEN_FORGOT_BOX(false)
              // 打开登录弹框
              this.SET_OPEN_LOGIN_BOX(true)
            }else {
              this.$message.warning(res.data.msg)
            }
          })

        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.forgot-panle {
  position: fixed;
  top: 200px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  width: 504px;
  height: auto;
  background: #FFFFFF;
  border-radius: 6px ;
  .forgot-panle-title {
  margin-top: 40px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 700;
  font-size: 32px;
  color: var(--primary-text-color);
  margin-bottom: 40px;
}
  .forgotForm {
    padding: 0 34px 40px;
    .email-group {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 22px;
      .email-group-item {
        margin-bottom: 0;
        width: 68%;
        margin-right: 10px;
      }
      .email-group-btn {
        width: calc(100% - 68% - 10px);
        padding: 12px 8px !important;
      }

    }
    .forgot-btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .btn {
        width: calc((100% - 10px) / 2);
        height: 50px;
        line-height: 50px;
        text-align: center;
        background: #D5E2E5;
        border-radius: 4px;
        color: var(--primary-text-color);
        cursor: pointer;
      }
      .login {
        background-color: var(--primary-text-color);
        color: #ffffff;
      }
    }
  }
}
::v-deep .el-input__inner {
  padding: 0 15px !important;
}
</style>