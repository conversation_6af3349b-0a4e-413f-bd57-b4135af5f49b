<template>
  <div class="mod-config">
    <basic-container>
      <el-form :model="searchForm"  ref="searchForm" size="small"  label-width="120px"
               class="demo-ruleForm" :inline="true">
        <el-form-item label="Family" prop="familyEn">
          <el-input  v-model="searchForm.familyEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="科名" prop="familyCn">
          <el-input  v-model="searchForm.familyCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="Genus" prop="genusEn">
          <el-input  v-model="searchForm.genusEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="属名" prop="genusCn">
          <el-input  v-model="searchForm.genusCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="Species" prop="speciesEn">
          <el-input  v-model="searchForm.speciesEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="种名" prop="speciesCn">
          <el-input  v-model="searchForm.speciesCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="采集地点" prop="collectionPlace">
          <el-input  v-model="searchForm.collectionPlace" clearable></el-input>
        </el-form-item>
        <el-form-item label="PlaceCollection" prop="placeEn">
          <el-input  v-model="searchForm.placeEn" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-button v-if="permissions.admin_xspecimen_add" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button icon="el-icon-upload" type="primary" @click="importExcel()">导入</el-button>
          <el-button v-if="permissions.admin_xspecimen_export" icon="el-icon-download" type="primary" plain
                     @click="exportExcel()">导出
          </el-button>
        </el-form-item>
      </el-form>
      <div class="avue-crud">
        <el-table :data="dataList" border v-loading="dataListLoading">
            <el-table-column type="index" header-align="center" align="center" label="序号" fixed>
              <template slot-scope="scope">
                <span>{{ (pageIndex - 1) * pageSize + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed width="160"
                    prop="specimenNo"
                    header-align="center"
                    align="center"
                    label="标本号/Specimen No.">
            </el-table-column>
<!--            <el-table-column-->
<!--                    prop="chineseName"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="中文名称">-->
<!--            </el-table-column>-->
          <el-table-column width="140"
                           prop="speciesCn"
                           header-align="center"
                           align="center"
                           label="物种中文名">
          </el-table-column>
            <el-table-column width="140"
                    prop="speciesEn"
                    header-align="center"
                    align="center"
                    label="species">
            </el-table-column>

          <el-table-column
            prop="genusCn"
            header-align="center"
            align="center"
            label="属名">
          </el-table-column>
            <el-table-column
                    prop="genusEn"
                    header-align="center"
                    align="center"
                    label="Genus">
            </el-table-column>
          <el-table-column
            prop="familyCn"
            header-align="center"
            align="center"
            label="科名">
          </el-table-column>
            <el-table-column width="140"
                    prop="familyEn"
                    header-align="center"
                    align="center"
                    label="Family">
            </el-table-column>
          <el-table-column width="240"
                           prop="placeEn"
                           header-align="center"
                           align="center"
                           label="Place of Collection">
          </el-table-column>
            <el-table-column width="160"
                    prop="collectionPlace"
                    header-align="center"
                    align="center"
                    label="采集地点">
            </el-table-column>
            <el-table-column width="160"
                    prop="collectionTime"
                    header-align="center"
                    align="center"
                    label="采集日期/Collect Date">
            </el-table-column>
          <el-table-column width="140"
                           prop="collectorEn"
                           header-align="center"
                           align="center"
                           label="collector">
          </el-table-column>
            <el-table-column width="140"
                    prop="collector"
                    header-align="center"
                    align="center"
                    label="采集人">
            </el-table-column>

            <el-table-column width="160"
                    prop="importBatch"
                    header-align="center"
                    align="center"
                    label="数据库导入批次">
            </el-table-column>
            <el-table-column
                    prop="languageType"
                    header-align="center"
                    align="center"
                    label="语言类型">
              <template slot-scope="scope">
                <div v-if="scope.row.languageType === 'zh'">中文</div>
                <div v-if="scope.row.languageType === 'en'">英文</div>
              </template>
            </el-table-column>
          <el-table-column width="160"
                           prop="createTime"
                           header-align="center"
                           align="center"
                           label="创建时间">
          </el-table-column>
          <el-table-column fixed="right" width="180"
                  header-align="center"
                  align="center"
                  label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" icon="el-icon-view" @click="detailHandle(scope.row.id)">详情</el-button>
              <el-button v-if="permissions.admin_xspecimen_edit" type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
              <el-button v-if="permissions.admin_xspecimen_del" type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
      <el-dialog
        title="导入叶角质层数据信息表" center
        :visible.sync="isImportData"
        width="50%"
        :close-on-click-modal="false"
        append-to-body >
        <ImportData ref="importData" :importType="'xspecimen'" :searchForm="searchForm" :export-file-name="'叶角质层数据信息'"
                    @exportSuccess="exportSuccess"  @errorSuccess="isImportData = false" v-on="$listeners"/>
      </el-dialog>
      <detailInfo v-if="detailVisible" ref="detailCom" ></detailInfo>

    </basic-container>
  </div>
</template>

<script>
  import {fetchList, delObj} from '@/api/xspecimen/xspecimen'
  import TableForm from './xspecimen-form'
  import {mapGetters} from 'vuex'
  import ImportData from '@/components/importData/index'
  import detailInfo from './info'
  export default {
    data() {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false,
        isImportData: false,
        detailVisible: false,

      }
    },
    components: {
      TableForm,
      ImportData,
      detailInfo
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {
      this.getDataList()
    },
    methods: {
      submitForm() {
        this.pageIndex = 1
        this.getDataList()
      },
      resetForm() {
        this.searchForm = {}

        this.getDataList()
      },
      //详情
      detailHandle(id) {
        this.detailVisible = true
        this.$nextTick(() => {
          this.$refs.detailCom.getDetailInfo(id)
        })
      },
      //导入生物分类层级表
      importExcel() {
        this.isImportData = true
      },
      //导入成功
      exportSuccess() {
        this.isImportData = false
        this.getDataList()
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        },this.searchForm)).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        this.$confirm('是否确认删除此条数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.getDataList()
          this.$message.success('删除成功')
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/admin/xspecimen/export', this.searchForm, '叶角质层数据库信息表.xlsx')
      }
    }
  }
</script>
