from flask import Flask, jsonify, request
from flask_cors import CORS
import mysql.connector
from mysql.connector import Error
import math

app = Flask(__name__)
CORS(app)

# MySQL数据库连接配置
# db_config = {
#     'host': '127.0.0.1',
#     'database': 'paleoecology_db',
#     'user': 'root',
#     'password': 'Kj9mPx7nQw2L',
#     'port': '32768',
#     'ssl_disabled': True
# }

db_config = {
    'host': '*************',
    'database': 'paleoecology_db',
    'user': 'root',
    'password': 'root',
    'port': '3306'
}

def get_db_connection():
    try:
        connection = mysql.connector.connect(**db_config)
        return connection
    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None


# 判断点是否在多边形内部的函数（使用射线法和交叉数算法的组合）
def point_in_polygon(point, polygon):
    """
    使用射线法和交叉数算法的组合判断点是否在多边形内部
    point: [lng, lat]
    polygon: [[lng1, lat1], [lng2, lat2], ...]
    """
    # 多边形必须至少有3个点
    if len(polygon) < 3:
        print(f"[DEBUG] Polygon has less than 3 points: {len(polygon)}")
        return False

    # 首先检查点是否在多边形的边界框内
    lngs = [p[0] for p in polygon]
    lats = [p[1] for p in polygon]
    min_lng, max_lng = min(lngs), max(lngs)
    min_lat, max_lat = min(lats), max(lats)

    if point[0] < min_lng or point[0] > max_lng or point[1] < min_lat or point[1] > max_lat:
        print(f"[DEBUG] Point {point} is outside polygon bounding box [{min_lng}, {min_lat}, {max_lng}, {max_lat}]")
        return False

    # 使用射线法（Ray Casting Algorithm）
    inside = False
    x, y = point

    for i in range(len(polygon)):
        j = (i - 1) % len(polygon)
        xi, yi = polygon[i]
        xj, yj = polygon[j]

        # 检查点是否在多边形的边上
        if (yi == y and xi == x) or (yj == y and xj == x):
            print(f"[DEBUG] Point {point} is on polygon vertex")
            return True

        # 检查点是否在多边形的边上
        if (yi == yj) and (y == yi) and (min(xi, xj) <= x <= max(xi, xj)):
            print(f"[DEBUG] Point {point} is on polygon edge")
            return True

        # 检查射线是否与边相交
        intersect = ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi)
        if intersect:
            inside = not inside

    print(f"[DEBUG] Point {point} is {'inside' if inside else 'outside'} polygon with {len(polygon)} points")
    return inside


# 规范化多边形坐标
def normalize_polygon(polygon):
    """
    规范化多边形坐标，确保它是闭合的，并且顶点顺序正确
    polygon: [[lng1, lat1], [lng2, lat2], ...]
    """
    # 确保多边形至少有3个点
    if len(polygon) < 3:
        print(f"[WARNING] Polygon has less than 3 points: {len(polygon)}")
        return polygon

    # 确保多边形是闭合的（第一个点和最后一个点相同）
    if polygon[0] != polygon[-1]:
        polygon.append(polygon[0])
        print(f"[DEBUG] Closed polygon by adding first point to end")

    # 计算多边形的面积符号，确定顶点顺序
    area = 0.0
    for i in range(len(polygon) - 1):
        area += (polygon[i][0] * polygon[i + 1][1] - polygon[i + 1][0] * polygon[i][1])

    # 如果面积为负，则反转顶点顺序
    if area < 0:
        polygon.reverse()
        print(f"[DEBUG] Reversed polygon vertices to ensure correct orientation")

    return polygon


@app.route('/data')
def get_data():
    # 获取所有请求参数
    search_terms = request.args.getlist('search_term')  # 接受多个搜索词
    collection_name = request.args.get('collection_name', '').strip()
    polygons = request.args.get('polygons', '').strip()
    bboxes = request.args.get('bboxes', '').strip()

    # 改进圆形参数处理
    circle_centers = request.args.getlist('circle_centers')
    if len(circle_centers) == 1 and '|' in circle_centers[0]:
        # 处理以 | 分隔的多个圆心
        circle_centers = circle_centers[0].split('|')
    circle_centers = [center.strip() for center in circle_centers if center.strip()]

    # 处理半径参数
    circle_radii = request.args.getlist('circle_radii')
    if len(circle_radii) == 1 and '|' in circle_radii[0]:
        # 处理以 | 分隔的多个半径
        circle_radii = circle_radii[0].split('|')
    radii = [float(radius) for radius in circle_radii if radius and float(radius) > 0]


    # 获取前端搜索条件
    # Taxonomic Rank
    accepted_rank = request.args.get('acceptedRank', '').strip()

    # Taxa
    phylum = request.args.get('phylum', '').strip()
    class_name = request.args.get('class', '').strip()
    order = request.args.get('order', '').strip()
    family = request.args.get('family', '').strip()
    genus = request.args.get('genus', '').strip()
    species = request.args.get('species', '').strip()
    scientific_name = request.args.get('scientificName', '').strip()
    original_name = request.args.get('originalName', '').strip()

    # Time
    epoch = request.args.get('epoch', '').strip()
    stage = request.args.get('stage', '').strip()
    early_interval = request.args.get('earlyInterval', '').strip()
    late_interval = request.args.get('lateInterval', '').strip()
    time_bin = request.args.get('timeBin', '').strip()

    # Geological age
    age_min = request.args.get('ageMin', '').strip()
    age_max = request.args.get('ageMax', '').strip()

    # Location & Dating
    country = request.args.get('country', '').strip()
    south_latitude = request.args.get('southLatitude', '-90').strip()
    north_latitude = request.args.get('northLatitude', '90').strip()
    west_longitude = request.args.get('westLongitude', '-180').strip()
    east_longitude = request.args.get('eastLongitude', '180').strip()
    dating_method = request.args.get('datingMethod', '').strip()
    dating_quality = request.args.get('datingQuality', '').strip()

    # Additional Info
    author = request.args.get('author', '').strip()
    pubyr = request.args.get('pubyr', '').strip()
    fossil_type = request.args.get('fossilType', '').strip()
    plant_organ = request.args.get('plantOrgan', '').strip()
    # 构建SQL查询
    # 检查是否有子表相关的查询参数
    has_child_table_params = any([
        accepted_rank, phylum, class_name, order, family, genus,
        species, scientific_name, original_name, plant_organ
    ])

    # 基本查询 - 只查询主表
    sql_query = """
    SELECT DISTINCT
        r.ID, r.Source, r.SourceID, r.SiteNo,r.SiteName,r.Collectionme, r.Country, r.DatingMethod,
        r.DatingQuality, r.Epoch, r.Stage, r.EarlyInterval, r.LateInterval,
        r.AgeMax, r.AgeMin, r.AgeMiddle, r.Author, r.Pubyr, r.Longitude, r.Latitude,
        r.TimeBin,r.FossilType, r.Reference1, r.Reference2, r.Reference3, r.OtherReferences
    FROM Rupelian r
    """

    # 如果有子表相关的查询参数，则添加关联
    if has_child_table_params:
        sql_query = sql_query.rstrip() + "\nLEFT JOIN Chattian c ON r.ID = c.ID"

    where_conditions = []
    params = []

    # 处理前端搜索条件
    # Taxonomic Rank
    if accepted_rank:
        where_conditions.append("c.AcceptedRank LIKE %s")
        params.append(f"%{accepted_rank}%")

    # Taxa
    if phylum:
        where_conditions.append("c.Phylum LIKE %s")
        params.append(f"%{phylum}%")

    if class_name:
        where_conditions.append("c.Class LIKE %s")
        params.append(f"%{class_name}%")

    if order:
        where_conditions.append("c.Order LIKE %s")
        params.append(f"%{order}%")

    if family:
        where_conditions.append("c.Family LIKE %s")
        params.append(f"%{family}%")

    if genus:
        where_conditions.append("c.Genus LIKE %s")
        params.append(f"%{genus}%")

    if species:
        species_condition = "(c.Species1 LIKE %s OR c.Species2 LIKE %s OR c.Species3 LIKE %s)"
        where_conditions.append(species_condition)
        params.extend([f"%{species}%", f"%{species}%", f"%{species}%"])

    if scientific_name:
        scientific_name_condition = "(c.ScientificName1 LIKE %s OR c.ScientificName2 LIKE %s OR c.ScientificName3 LIKE %s)"
        where_conditions.append(scientific_name_condition)
        params.extend([f"%{scientific_name}%", f"%{scientific_name}%", f"%{scientific_name}%"])

    if original_name:
        where_conditions.append("c.OriginalName LIKE %s")
        params.append(f"%{original_name}%")

    # Time
    if epoch:
        where_conditions.append("r.Epoch LIKE %s")
        params.append(f"%{epoch}%")

    if stage:
        where_conditions.append("r.Stage LIKE %s")
        params.append(f"%{stage}%")

    if early_interval:
        where_conditions.append("r.EarlyInterval LIKE %s")
        params.append(f"%{early_interval}%")

    if late_interval:
        where_conditions.append("r.LateInterval LIKE %s")
        params.append(f"%{late_interval}%")

    if time_bin:
        where_conditions.append("r.TimeBin LIKE %s")
        params.append(f"%{time_bin}%")

    # Geological age
    if age_min and age_max:
        # For geological ages, we want to find records where:
        # 1. The record's AgeMin is >= the query's AgeMin (record's oldest age is at least as old as query's oldest age)
        # 2. AND the record's AgeMax is <= the query's AgeMax (record's youngest age is at most as old as query's youngest age)
        where_conditions.append("r.AgeMin >= %s AND r.AgeMax <= %s")
        params.extend([age_min, age_max])
    elif age_min:
        # Only min is provided - find records with min age >= specified min
        where_conditions.append("r.AgeMin >= %s")
        params.append(age_min)
    elif age_max:
        # Only max is provided - find records with max age <= specified max
        where_conditions.append("r.AgeMax <= %s")
        params.append(age_max)

    # Location & Dating
    if country:
        where_conditions.append("r.Country LIKE %s")
        params.append(f"%{country}%")

    # 处理经纬度范围
    try:
        south_lat = float(south_latitude)
        north_lat = float(north_latitude)
        west_lng = float(west_longitude)
        east_lng = float(east_longitude)

        # 只有当用户修改了默认值时才添加经纬度条件
        if south_lat > -90 or north_lat < 90 or west_lng > -180 or east_lng < 180:
            where_conditions.append(
                "(CAST(r.Latitude AS DECIMAL(10,6)) BETWEEN %s AND %s AND CAST(r.Longitude AS DECIMAL(10,6)) BETWEEN %s AND %s)")
            params.extend([south_lat, north_lat, west_lng, east_lng])
    except ValueError:
        print("[DEBUG] Error parsing latitude/longitude values")

    if dating_method:
        where_conditions.append("r.DatingMethod LIKE %s")
        params.append(f"%{dating_method}%")

    if dating_quality:
        where_conditions.append("r.DatingQuality LIKE %s")
        params.append(f"%{dating_quality}%")

    # Additional Info
    if author:
        where_conditions.append("r.Author LIKE %s")
        params.append(f"%{author}%")

    if pubyr:
        where_conditions.append("r.Pubyr LIKE %s")
        params.append(f"%{pubyr}%")

    if fossil_type:
        where_conditions.append("r.FossilType LIKE %s")
        params.append(f"%{fossil_type}%")

    if plant_organ:
        where_conditions.append("c.PlantOrgan1 LIKE %s OR c.PlantOrgan2 LIKE %s")
        params.extend([f"%{plant_organ}%", f"%{plant_organ}%"])

    # 处理地理空间查询
    geo_conditions = []

    # 存储多边形数据，用于后处理
    polygon_data = []

    # 处理多边形条件
    if polygons:
        # 处理可能包含多个多边形的情况
        polygon_list = polygons.split('|')
        for polygon_coords in polygon_list:
            if polygon_coords.strip():
                try:
                    coords = list(map(float, polygon_coords.split(',')))
                    if len(coords) >= 6 and len(coords) % 2 == 0:
                        # 首先使用边界框方法进行初步筛选
                        lngs = [coords[i] for i in range(0, len(coords), 2)]
                        lats = [coords[i + 1] for i in range(0, len(coords), 2)]
                        min_lng, max_lng = min(lngs), max(lngs)
                        min_lat, max_lat = min(lats), max(lats)

                        # 使用边界框查询
                        geo_conditions.append(
                            "(CAST(r.Longitude AS DECIMAL(10,6)) BETWEEN %s AND %s AND CAST(r.Latitude AS DECIMAL(10,6)) BETWEEN %s AND %s)")
                        params.extend([min_lng, max_lng, min_lat, max_lat])

                        # 保存多边形顶点，用于后处理
                        points = []
                        for i in range(0, len(coords), 2):
                            points.append([coords[i], coords[i + 1]])

                        # 规范化多边形坐标
                        points = normalize_polygon(points)

                        polygon_data.append(points)

                        print(f"[DEBUG] Using bounding box for polygon: [{min_lng}, {min_lat}, {max_lng}, {max_lat}]")
                        print(f"[DEBUG] Polygon points ({len(points)} vertices):")
                        for i, point in enumerate(points):
                            print(f"  Vertex {i}: [{point[0]}, {point[1]}]")
                except ValueError as e:
                    print(f"Error processing polygon coordinates: {e}")
                    continue

    # 处理矩形条件
    bbox_data = []
    if bboxes:
        bbox_list = bboxes.split('|')
        for bbox in bbox_list:
            try:
                min_lng, min_lat, max_lng, max_lat = map(float, bbox.split(','))
                geo_conditions.append(
                    "(CAST(r.Longitude AS DECIMAL(10,6)) BETWEEN %s AND %s AND CAST(r.Latitude AS DECIMAL(10,6)) BETWEEN %s AND %s)")
                params.extend([min_lng, max_lng, min_lat, max_lat])

                # 保存矩形数据，用于后处理
                bbox_data.append((min_lng, min_lat, max_lng, max_lat))
                print(
                    f"[DEBUG] Rectangle search: min_lng={min_lng}, min_lat={min_lat}, max_lng={max_lng}, max_lat={max_lat}")
            except ValueError as e:
                print(f"Error processing bounding box coordinates: {e}")
                continue

    # 处理圆形条件
    circle_data = []
    for i, center in enumerate(circle_centers):
        if i < len(radii):
            try:
                lng, lat = map(float, center.split(','))
                radius_deg = radii[i] / 111000  # 将米转换为大致的经纬度差（1度约等于111km）

                # 使用简单的经纬度范围查询代替复杂的Haversine计算
                # 这种方法在赤道附近效果较好，在高纬度地区会有一定误差
                geo_conditions.append("""
                    (CAST(r.Longitude AS DECIMAL(10,6)) BETWEEN %s AND %s AND
                     CAST(r.Latitude AS DECIMAL(10,6)) BETWEEN %s AND %s)
                """)
                params.extend([lng - radius_deg, lng + radius_deg, lat - radius_deg, lat + radius_deg])

                # 保存圆形数据，用于后处理
                circle_data.append((lng, lat, radii[i]))

                print(f"[DEBUG] Circle search: center=({lng},{lat}), radius={radii[i]}m, radius_deg={radius_deg}")
            except ValueError as e:
                print(f"Error processing circle coordinates: {e}")
                continue

    if geo_conditions:
        where_conditions.append("(" + " OR ".join(geo_conditions) + ")")

    # 添加WHERE子句（如果有条件）
    if where_conditions:
        sql_query += " WHERE " + " AND ".join(where_conditions)

    print(f"[DEBUG] Final SQL query: {sql_query}")
    print(f"[DEBUG] Query parameters: {params}")

    # 执行查询
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor(dictionary=True)
            try:
                cursor.execute(sql_query, params)
                results = cursor.fetchall()
                print(f"[DEBUG] Query executed successfully, fetched {len(results)} results")
            except Error as e:
                print(f"Error executing MySQL query: {e}")
                print(f"SQL Query: {sql_query}")
                print(f"Parameters: {params}")
                return jsonify({'error': f'Database query failed: {str(e)}'}), 500
            finally:
                cursor.close()
                connection.close()

            # 处理结果，合并多级数字标号字段
            processed_results = []
            original_count = len(results)
            filtered_count = 0

            for row in results:
                processed_row = row.copy()

                # 检查点是否在任何一个多边形或圆形内部
                # 如果没有多边形或圆形，则保留所有点
                if polygon_data or circle_data or bbox_data:
                    try:
                        lng = float(row['Longitude'])
                        lat = float(row['Latitude'])
                        point = [lng, lat]

                        # 默认不在任何形状内
                        in_any_shape = False

                        # 检查点是否在任何一个多边形内部
                        if polygon_data:
                            for polygon in polygon_data:
                                if point_in_polygon(point, polygon):
                                    in_any_shape = True
                                    print(f"[DEBUG] Point at ({lng}, {lat}) is inside a polygon")
                                    break

                        # 如果不在多边形内，检查是否在任何一个圆形内部
                        if not in_any_shape and circle_data:
                            for circle_center_lng, circle_center_lat, radius in circle_data:
                                distance = haversine_distance(lat, lng, circle_center_lat, circle_center_lng)
                                if distance <= radius:
                                    in_any_shape = True
                                    print(f"[DEBUG] Point at ({lng}, {lat}) is inside a circle")
                                    break

                        # 如果不在多边形或圆形内，检查是否在任何一个矩形内部
                        if not in_any_shape and bbox_data:
                            for min_lng, min_lat, max_lng, max_lat in bbox_data:
                                if min_lng <= lng <= max_lng and min_lat <= lat <= max_lat:
                                    in_any_shape = True
                                    print(f"[DEBUG] Point at ({lng}, {lat}) is inside a rectangle")
                                    break

                        # 如果不在任何形状内部，则跳过此点
                        if not in_any_shape:
                            filtered_count += 1
                            print(f"[DEBUG] Filtered out point at ({lng}, {lat}) - outside all shapes")
                            continue
                    except (ValueError, TypeError) as e:
                        print(f"Error checking point in shapes: {e}")
                        print(f"Problem row: {row}")

                # 合并多级数字标号字段
                for base_field in ['Species', 'ScientificName', 'PlantOrgan']:
                    values = [row.get(f'{base_field}{i}', '') for i in range(1, 4) if row.get(f'{base_field}{i}')]
                    processed_row[base_field] = ', '.join(values) if values else None

                processed_results.append(processed_row)

            print(
                f"[DEBUG] Original results: {original_count}, Filtered out: {filtered_count}, Final results: {len(processed_results)}")
            return jsonify({'table1': processed_results})
        except Error as e:
            print(f"Error with database connection: {e}")
            return jsonify({'error': f'Database operation failed: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Database connection failed'}), 500


# Add a new endpoint to get detail data from Chattian table based on ID
@app.route('/detail', methods=['GET'])
def get_detail_data():
    try:
        # Get the ID parameter from the request
        id_value = request.args.get('id', '').strip()

        if not id_value:
            return jsonify({'error': 'ID parameter is required'}), 400

        # Connect to the database
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Query to get detail data from Chattian table
        query = """
        SELECT * FROM Chattian WHERE ID = %s
        """

        cursor.execute(query, (id_value,))
        detail_data = cursor.fetchall()

        # Close the database connection
        cursor.close()
        conn.close()

        # Return the detail data as JSON
        return jsonify({'detail': detail_data})

    except Exception as e:
        print(f"Error in get_detail_data: {str(e)}")
        return jsonify({'error': str(e)}), 500


# 计算两点之间的距离（Haversine公式）
def haversine_distance(lat1, lon1, lat2, lon2):
    """
    计算两个经纬度点之间的距离（米）
    使用Haversine公式
    """
    # 将经纬度转换为弧度
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

    # Haversine公式
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    c = 2 * math.asin(math.sqrt(a))
    r = 6371000  # 地球半径（米）
    return c * r


# Add endpoint to get unique Dating Method values
@app.route('/dating-methods')
def get_dating_methods():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique Dating Method values
            query = "SELECT DISTINCT DatingMethod FROM Rupelian WHERE DatingMethod IS NOT NULL AND DatingMethod != ''"
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching dating methods: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Dating Quality values
@app.route('/dating-qualities')
def get_dating_qualities():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique Dating Quality values
            query = "SELECT DISTINCT DatingQuality FROM Rupelian WHERE DatingQuality IS NOT NULL AND DatingQuality != ''"
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching dating qualities: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Family values
@app.route('/taxa/families')
def get_families():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique Family values from Chattian table
            query = "SELECT DISTINCT Family FROM Chattian WHERE Family IS NOT NULL AND Family != ''"
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching families: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Genus values
@app.route('/taxa/genera')
def get_genera():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique Genus values from Chattian table
            query = "SELECT DISTINCT Genus FROM Chattian WHERE Genus IS NOT NULL AND Genus != ''"
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching genera: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Species values
@app.route('/taxa/species')
def get_species():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique Species values from Chattian table
            # Combine Species1, Species2, and Species3 columns
            query = """
            SELECT DISTINCT Species1 FROM Chattian WHERE Species1 IS NOT NULL AND Species1 != ''
            UNION
            SELECT DISTINCT Species2 FROM Chattian WHERE Species2 IS NOT NULL AND Species2 != ''
            UNION
            SELECT DISTINCT Species3 FROM Chattian WHERE Species3 IS NOT NULL AND Species3 != ''
            """
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching species: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique ScientificName values
@app.route('/taxa/scientific-names')
def get_scientific_names():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique ScientificName values from Chattian table
            # Combine ScientificName1, ScientificName2, and ScientificName3 columns
            query = """
            SELECT DISTINCT ScientificName1 FROM Chattian WHERE ScientificName1 IS NOT NULL AND ScientificName1 != ''
            UNION
            SELECT DISTINCT ScientificName2 FROM Chattian WHERE ScientificName2 IS NOT NULL AND ScientificName2 != ''
            UNION
            SELECT DISTINCT ScientificName3 FROM Chattian WHERE ScientificName3 IS NOT NULL AND ScientificName3 != ''
            """
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching scientific names: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Original Name values
@app.route('/taxa/original-names')
def get_original_names():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique OriginalName values from Chattian table
            query = """
            SELECT DISTINCT OriginalName FROM Chattian WHERE OriginalName IS NOT NULL AND OriginalName != ''
            """
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching original names: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Fossil Type values
@app.route('/fossil-types')
def get_fossil_types():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique FossilType values from Rupelian table
            query = """
            SELECT DISTINCT FossilType FROM Rupelian WHERE FossilType IS NOT NULL AND FossilType != ''
            """
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching fossil types: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

# Add endpoint to get unique Plant Organ values
@app.route('/plant-organs')
def get_plant_organs():
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            # Query to get all unique PlantOrgan values from Chattian table
            # Combine PlantOrgan1 and PlantOrgan2 columns
            query = """
            SELECT DISTINCT PlantOrgan1 FROM Chattian WHERE PlantOrgan1 IS NOT NULL AND PlantOrgan1 != ''
            UNION
            SELECT DISTINCT PlantOrgan2 FROM Chattian WHERE PlantOrgan2 IS NOT NULL AND PlantOrgan2 != ''
            """
            cursor.execute(query)
            results = [row[0] for row in cursor.fetchall()]
            cursor.close()
            connection.close()
            return jsonify(results)
        else:
            return jsonify({'error': 'Database connection failed'}), 500
    except Error as e:
        print(f"Error fetching plant organs: {e}")
        return jsonify({'error': f'Database operation failed: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)