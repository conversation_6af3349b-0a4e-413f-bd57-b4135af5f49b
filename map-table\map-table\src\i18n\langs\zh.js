export default  {
    nav: {
        // 首页，古生态数据库，叶角质层数据库，教育，团队，关于我们
        homeNav: '首页',
        paleoecologyNav: '古生态数据库',
        leafCuticlNav: '叶角质层数据库',
        leafCuticlDetailNav: '叶角质层数据库详情',
        educationNav: '教育',
        educationDetailNav: '教育详情',
        teamNav: '团队',
        teamDetailNav: '团队详情',
        aboutNav: '关于我们',
    },
    footer: {
        addressFooter: '地址',
        addressText: '中国云南省西双版纳傣族自治州勐腊县勐仑镇',
        telFooter: '电话',
        telText: '0691-8715071',
        emailFooter: '邮箱',
        emailText: '<EMAIL>；<EMAIL>',
        postalCode: '邮政编码',
        postalCodeText: '666303',
        copyrightNotice: '版权所有 ©2024，中国科学院西双版纳热带植物园古生态研究组，地址：中国云南省西双版纳傣族自治州勐腊县勐仑镇，邮编 666303 ',
        visitsFooter: '访问量',
    },
    about: {
        aboutText: '关于我们',
        overviewText: '概述',
        contactText: '联系我们',
        contactInformation: '联系方式'
    },
    team: {
        teamText: '团队',
        introductionText: '团队介绍',
        academicianText: '研究团队',
        researcherText: '人才培养',
        academicTitle:'职称',
        titleText: '岗位',
        email:'邮箱',
        mailingAddress:'通讯地址',
        introduction:'简介',
        resumeText: '简历',
        publicationsText: '研究成果'
    },
    education: {
        educationText: '教育',
        articlesText: '科普文章',
        videoText: '科普视频',
        publicationText:'研究出版物',
    },
    home: {
        barTit: '新生代的岩层分布统计',
        pieTit: '化石类型分布',
        specimenNum: '样本数量',
        samplingNum: '取样点',
        distributionNum: '分布地点',
        publicationNum: '出版物',
        homeSubTit: '探索地球上生命进化的奥秘'
    },
    login: {
        loginButton: '登录/注册',
        loginBoxBtn: '登录',
        cancelBtn: '取消',
        loginNote: '没有账号？',
        signIn: '立即注册',
        forgotText: '忘记密码？',
        createAccount: '创建账户',
        registerNote: '已有账号？',
        toLogin: '去登录',
        codeText: '获取验证码',
        forgotNote: '重置密码',
        namePlaceholder: '姓名',
        emailPlaceholder: '邮箱',
        codePlaceholder: '验证码',
        newPassPlaceholder: '新密码',
        passPlaceholder: '密码',
        rePassPlaceholder: '确认密码',
        oldPassPlaceholder: '原密码',
        saveBtn: '保存',
        utilPlaceholder: '单位',
        phonePlaceholder: '联系电话',
        userInfo:'个人中心',
        logout: '退出登录',
        info: '基本信息',
        updatePass: '修改密码',
        interface: '接口文档',
        emailMessage: '邮箱号不能为空',
        emailMessageValid: '请输入有效的邮箱地址（如：<EMAIL>）',
        nameMessage: '姓名不能为空',
        codeMessage: '验证码不能为空',
        utilMessage: '单位',
        phoneMessage: '手机号不能为空',
        confirmMessage: '确认密码不能为空',
        confirmMessageValid: '两次输入的密码不一致!',
        newPassMessage: '新密码不能为空',
        passMessage: '密码不能为空',
        passMessageValid: '密码必须为8-16位，包含字母、数字和特殊字符（如：zhangsan@123）',

    }

}