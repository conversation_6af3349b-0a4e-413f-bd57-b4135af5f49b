<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.codegen.mapper.GeneratorPgMapper">

	<select id="queryTable" resultType="map">
		SELECT
			relname AS "tableName",
			obj_description ( oid ) AS "tableComment"
		FROM
			pg_class pc
		WHERE
			relkind = 'r'
		AND relname NOT LIKE'pg_%'
		AND relname NOT LIKE'sql_%'
		AND relchecks = 0
		<if test="tableName != null and tableName.trim() != ''">
			and pc.relname = #{tableName}
		</if>
		ORDER BY relname
	</select>


	<sql id="queryColumn">
		SELECT
			'pg' AS "dbType",
			pa.attname AS "columnName",
			pg_type.typname AS "columnType",
			pg_type.typname AS "dataType",
			col_description ( pa.attrelid, pa.attnum ) AS "comments",
			(
			CASE
					pcon.contype
					WHEN 'p' THEN
					'PRI' ELSE''
				END
				) AS "columnKey"
			FROM
				pg_class AS pc,
				pg_attribute AS pa
				INNER JOIN pg_type ON pg_type.oid = pa.atttypid
				LEFT JOIN pg_constraint pcon ON pa.attnum = pcon.conkey[1]
				AND pa.attrelid = pcon.conrelid
			WHERE
				pc.relname = #{ tableName }
			AND pa.attrelid = pc.oid
			AND pa.attnum > 0
	</sql>

	<select id="selectTableColumn" resultType="com.tdkj.tdcloud.codegen.entity.ColumnEntity">
		<include refid="queryColumn"/>
	</select>

	<select id="selectMapTableColumn" resultType="map">
		<include refid="queryColumn"/>
	</select>
</mapper>
