<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.ChattianMapper">

	<resultMap id="chattianMap" type="com.tdkj.tdcloud.admin.api.entity.Chattian">
		<id property="cid" column="Cid"/>
		<result property="id" column="ID"/>
		<result property="originalname" column="OriginalName"/>
		<result property="scientificname1" column="ScientificName1"/>
		<result property="scientificname2" column="ScientificName2"/>
		<result property="scientificname3" column="ScientificName3"/>
		<result property="acceptedrank" column="AcceptedRank"/>
		<result property="phylum" column="Phylum"/>
		<result property="classNew" column="class_new"/>
		<result property="orderNew" column="order_new"/>
		<result property="family" column="Family"/>
		<result property="genus" column="Genus"/>
		<result property="species1" column="Species1"/>
		<result property="species2" column="Species2"/>
		<result property="species3" column="Species3"/>
		<result property="plantorgan1" column="PlantOrgan1"/>
		<result property="plantorgan2" column="PlantOrgan2"/>
		<result property="abundvalue" column="AbundValue"/>
		<result property="abundunit" column="AbundUnit"/>
		<result property="fossiltype" column="FossilType"/>
		<result property="pollendiagram" column="PollenDiagram"/>
		<result property="siteno" column="SiteNo"/>
		<result property="sitename" column="SiteName"/>
		<result property="extinct" column="Extinct"/>
		<result property="timecontext" column="TimeContext"/>
	</resultMap>

	<sql id="selectChattianVo">
		select ID, OriginalName, ScientificName1, ScientificName2, ScientificName3, AcceptedRank, Phylum, class_new, order_new, Family, Genus, Species1, Species2, Species3, PlantOrgan1, PlantOrgan2, AbundValue, AbundUnit, FossilType, PollenDiagram, SiteNo, SiteName, Extinct, TimeContext, Cid from chattian
	</sql>

	<select id="selectChattianList" parameterType="com.tdkj.tdcloud.admin.api.entity.Chattian" resultMap="chattianMap">
		<include refid="selectChattianVo"/>
		<where>
			<if test="ID != null  and ID != ''"> and ID = #{ID}</if>
			<if test="OriginalName != null  and OriginalName != ''"> and OriginalName like concat('%', #{OriginalName}, '%')</if>
			<if test="ScientificName1 != null  and ScientificName1 != ''"> and ScientificName1 = #{ScientificName1}</if>
			<if test="ScientificName2 != null  and ScientificName2 != ''"> and ScientificName2 = #{ScientificName2}</if>
			<if test="ScientificName3 != null  and ScientificName3 != ''"> and ScientificName3 = #{ScientificName3}</if>
			<if test="AcceptedRank != null  and AcceptedRank != ''"> and AcceptedRank = #{AcceptedRank}</if>
			<if test="Phylum != null  and Phylum != ''"> and Phylum = #{Phylum}</if>
			<if test="classNew != null  and classNew != ''"> and class_new = #{classNew}</if>
			<if test="orderNew != null  and orderNew != ''"> and order_new = #{orderNew}</if>
			<if test="Family != null  and Family != ''"> and Family = #{Family}</if>
			<if test="Genus != null  and Genus != ''"> and Genus = #{Genus}</if>
			<if test="Species1 != null  and Species1 != ''"> and Species1 = #{Species1}</if>
			<if test="Species2 != null  and Species2 != ''"> and Species2 = #{Species2}</if>
			<if test="Species3 != null  and Species3 != ''"> and Species3 = #{Species3}</if>
			<if test="PlantOrgan1 != null  and PlantOrgan1 != ''"> and PlantOrgan1 = #{PlantOrgan1}</if>
			<if test="PlantOrgan2 != null  and PlantOrgan2 != ''"> and PlantOrgan2 = #{PlantOrgan2}</if>
			<if test="AbundValue != null  and AbundValue != ''"> and AbundValue = #{AbundValue}</if>
			<if test="AbundUnit != null  and AbundUnit != ''"> and AbundUnit = #{AbundUnit}</if>
			<if test="FossilType != null  and FossilType != ''"> and FossilType = #{FossilType}</if>
			<if test="PollenDiagram != null  and PollenDiagram != ''"> and PollenDiagram = #{PollenDiagram}</if>
			<if test="SiteNo != null "> and SiteNo = #{SiteNo}</if>
			<if test="SiteName != null  and SiteName != ''"> and SiteName like concat('%', #{SiteName}, '%')</if>
			<if test="Extinct != null  and Extinct != ''"> and Extinct = #{Extinct}</if>
			<if test="TimeContext != null "> and TimeContext = #{TimeContext}</if>
		</where>
	</select>

	<select id="selectChattianByCid" parameterType="Long" resultMap="chattianMap">
		<include refid="selectChattianVo"/>
		where Cid = #{Cid}
	</select>

	<select id="selectChattianById" parameterType="integer" resultMap="chattianMap">
		<include refid="selectChattianVo"/>
		where ID = #{id}
	</select>

	<insert id="insertChattian" parameterType="com.tdkj.tdcloud.admin.api.entity.Chattian" useGeneratedKeys="true" keyProperty="Cid">
		insert into chattian
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="ID != null">ID,</if>
			<if test="OriginalName != null">OriginalName,</if>
			<if test="ScientificName1 != null">ScientificName1,</if>
			<if test="ScientificName2 != null">ScientificName2,</if>
			<if test="ScientificName3 != null">ScientificName3,</if>
			<if test="AcceptedRank != null">AcceptedRank,</if>
			<if test="Phylum != null">Phylum,</if>
			<if test="classNew != null">class_new,</if>
			<if test="orderNew != null">order_new,</if>
			<if test="Family != null">Family,</if>
			<if test="Genus != null">Genus,</if>
			<if test="Species1 != null">Species1,</if>
			<if test="Species2 != null">Species2,</if>
			<if test="Species3 != null">Species3,</if>
			<if test="PlantOrgan1 != null">PlantOrgan1,</if>
			<if test="PlantOrgan2 != null">PlantOrgan2,</if>
			<if test="AbundValue != null">AbundValue,</if>
			<if test="AbundUnit != null">AbundUnit,</if>
			<if test="FossilType != null">FossilType,</if>
			<if test="PollenDiagram != null">PollenDiagram,</if>
			<if test="SiteNo != null">SiteNo,</if>
			<if test="SiteName != null">SiteName,</if>
			<if test="Extinct != null">Extinct,</if>
			<if test="TimeContext != null">TimeContext,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="ID != null">#{ID},</if>
			<if test="OriginalName != null">#{OriginalName},</if>
			<if test="ScientificName1 != null">#{ScientificName1},</if>
			<if test="ScientificName2 != null">#{ScientificName2},</if>
			<if test="ScientificName3 != null">#{ScientificName3},</if>
			<if test="AcceptedRank != null">#{AcceptedRank},</if>
			<if test="Phylum != null">#{Phylum},</if>
			<if test="classNew != null">#{classNew},</if>
			<if test="orderNew != null">#{orderNew},</if>
			<if test="Family != null">#{Family},</if>
			<if test="Genus != null">#{Genus},</if>
			<if test="Species1 != null">#{Species1},</if>
			<if test="Species2 != null">#{Species2},</if>
			<if test="Species3 != null">#{Species3},</if>
			<if test="PlantOrgan1 != null">#{PlantOrgan1},</if>
			<if test="PlantOrgan2 != null">#{PlantOrgan2},</if>
			<if test="AbundValue != null">#{AbundValue},</if>
			<if test="AbundUnit != null">#{AbundUnit},</if>
			<if test="FossilType != null">#{FossilType},</if>
			<if test="PollenDiagram != null">#{PollenDiagram},</if>
			<if test="SiteNo != null">#{SiteNo},</if>
			<if test="SiteName != null">#{SiteName},</if>
			<if test="Extinct != null">#{Extinct},</if>
			<if test="TimeContext != null">#{TimeContext},</if>
		</trim>
	</insert>

	<insert id="insertChattianExcel" parameterType="com.tdkj.tdcloud.admin.api.vo.ChattianExcelVO" useGeneratedKeys="true" keyProperty="Cid">
		insert into chattian
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="ID != null">ID,</if>
			<if test="OriginalName != null">OriginalName,</if>
			<if test="ScientificName1 != null">ScientificName1,</if>
			<if test="ScientificName2 != null">ScientificName2,</if>
			<if test="ScientificName3 != null">ScientificName3,</if>
			<if test="AcceptedRank != null">AcceptedRank,</if>
			<if test="Phylum != null">Phylum,</if>
			<if test="classNew != null">class_new,</if>
			<if test="orderNew != null">order_new,</if>
			<if test="Family != null">Family,</if>
			<if test="Genus != null">Genus,</if>
			<if test="Species1 != null">Species1,</if>
			<if test="Species2 != null">Species2,</if>
			<if test="Species3 != null">Species3,</if>
			<if test="PlantOrgan1 != null">PlantOrgan1,</if>
			<if test="PlantOrgan2 != null">PlantOrgan2,</if>
			<if test="AbundValue != null">AbundValue,</if>
			<if test="AbundUnit != null">AbundUnit,</if>
			<if test="FossilType != null">FossilType,</if>
			<if test="PollenDiagram != null">PollenDiagram,</if>
			<if test="SiteNo != null">SiteNo,</if>
			<if test="SiteName != null">SiteName,</if>
			<if test="Extinct != null">Extinct,</if>
			<if test="TimeContext != null">TimeContext,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="ID != null">#{ID},</if>
			<if test="OriginalName != null">#{OriginalName},</if>
			<if test="ScientificName1 != null">#{ScientificName1},</if>
			<if test="ScientificName2 != null">#{ScientificName2},</if>
			<if test="ScientificName3 != null">#{ScientificName3},</if>
			<if test="AcceptedRank != null">#{AcceptedRank},</if>
			<if test="Phylum != null">#{Phylum},</if>
			<if test="classNew != null">#{classNew},</if>
			<if test="orderNew != null">#{orderNew},</if>
			<if test="Family != null">#{Family},</if>
			<if test="Genus != null">#{Genus},</if>
			<if test="Species1 != null">#{Species1},</if>
			<if test="Species2 != null">#{Species2},</if>
			<if test="Species3 != null">#{Species3},</if>
			<if test="PlantOrgan1 != null">#{PlantOrgan1},</if>
			<if test="PlantOrgan2 != null">#{PlantOrgan2},</if>
			<if test="AbundValue != null">#{AbundValue},</if>
			<if test="AbundUnit != null">#{AbundUnit},</if>
			<if test="FossilType != null">#{FossilType},</if>
			<if test="PollenDiagram != null">#{PollenDiagram},</if>
			<if test="SiteNo != null">#{SiteNo},</if>
			<if test="SiteName != null">#{SiteName},</if>
			<if test="Extinct != null">#{Extinct},</if>
			<if test="TimeContext != null">#{TimeContext},</if>
		</trim>
	</insert>

	<update id="updateChattian" parameterType="com.tdkj.tdcloud.admin.api.entity.Chattian">
		update chattian
		<trim prefix="SET" suffixOverrides=",">
			<if test="ID != null">ID = #{ID},</if>
			<if test="OriginalName != null">OriginalName = #{OriginalName},</if>
			<if test="ScientificName1 != null">ScientificName1 = #{ScientificName1},</if>
			<if test="ScientificName2 != null">ScientificName2 = #{ScientificName2},</if>
			<if test="ScientificName3 != null">ScientificName3 = #{ScientificName3},</if>
			<if test="AcceptedRank != null">AcceptedRank = #{AcceptedRank},</if>
			<if test="Phylum != null">Phylum = #{Phylum},</if>
			<if test="classNew != null">class_new = #{classNew},</if>
			<if test="orderNew != null">order_new = #{orderNew},</if>
			<if test="Family != null">Family = #{Family},</if>
			<if test="Genus != null">Genus = #{Genus},</if>
			<if test="Species1 != null">Species1 = #{Species1},</if>
			<if test="Species2 != null">Species2 = #{Species2},</if>
			<if test="Species3 != null">Species3 = #{Species3},</if>
			<if test="PlantOrgan1 != null">PlantOrgan1 = #{PlantOrgan1},</if>
			<if test="PlantOrgan2 != null">PlantOrgan2 = #{PlantOrgan2},</if>
			<if test="AbundValue != null">AbundValue = #{AbundValue},</if>
			<if test="AbundUnit != null">AbundUnit = #{AbundUnit},</if>
			<if test="FossilType != null">FossilType = #{FossilType},</if>
			<if test="PollenDiagram != null">PollenDiagram = #{PollenDiagram},</if>
			<if test="SiteNo != null">SiteNo = #{SiteNo},</if>
			<if test="SiteName != null">SiteName = #{SiteName},</if>
			<if test="Extinct != null">Extinct = #{Extinct},</if>
			<if test="TimeContext != null">TimeContext = #{TimeContext},</if>
		</trim>
		where Cid = #{Cid}
	</update>

	<delete id="deleteChattianByCid" parameterType="Long">
		delete from chattian where Cid = #{Cid}
	</delete>

	<delete id="deleteChattianByCids" parameterType="String">
		delete from chattian where Cid in
		<foreach item="Cid" collection="array" open="(" separator="," close=")">
			#{Cid}
		</foreach>
	</delete>
</mapper>
