/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.XTeamDto;
import com.tdkj.tdcloud.admin.api.entity.XTeam;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import com.tdkj.tdcloud.admin.service.XTeamService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 团队
 *
 * <AUTHOR> code generator
 * @date 2025-01-06 09:47:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/xteam" )
@Tag(description = "xteam" , name = "团队管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XTeamController {

    private final  XTeamService xTeamService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param xTeam 团队
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_xteam_view')" )
    public R getXTeamPage(Page page, XTeamDto xTeam) {
        return R.ok(xTeamService.getXTeamPage(page, xTeam));
    }


    /**
     * 通过id查询团队
     * @param id id
     * @return R
     */
	@Inner(value = false)
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_xteam_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return xTeamService.getXTeamById(id);
    }

    /**
     * 新增团队
     * @param xTeam 团队
     * @return R
     */
    @Operation(summary = "新增团队" , description = "新增团队" )
    @SysLog("新增团队" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_xteam_add')" )
    public R save(@RequestBody XTeam xTeam) {
        return xTeamService.saveXTeam(xTeam);
    }

    /**
     * 修改团队
     * @param xTeam 团队
     * @return R
     */
    @Operation(summary = "修改团队" , description = "修改团队" )
    @SysLog("修改团队" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_xteam_edit')" )
    public R updateById(@RequestBody XTeam xTeam) {
        return xTeamService.updateXTeamById(xTeam);
    }

    /**
     * 通过id删除团队
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id删除团队" , description = "通过id删除团队" )
    @SysLog("通过id删除团队" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xteam_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(xTeamService.removeById(id));
    }


    /**
     * 导出excel 表格
     * @param xTeam 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_xteam_export')" )
    public List<XTeam> export(XTeam xTeam) {
        return xTeamService.list(Wrappers.query(xTeam));
    }
}
