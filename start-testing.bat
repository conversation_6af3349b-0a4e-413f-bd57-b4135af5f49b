@echo off
echo ========================================
echo Paleoecology Migration Testing Startup
echo ========================================
echo.

echo Checking prerequisites...
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install JDK 1.8+ and add to PATH
    pause
    exit /b 1
)
echo ✓ Java is installed

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js and npm
    pause
    exit /b 1
)
echo ✓ Node.js is installed

echo.
echo Starting applications...
echo.

REM Start Java Spring Boot backend
echo Starting Java Spring Boot backend on port 8888...
cd /d "%~dp0paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz"
if not exist "target\tdcloud-upms-biz.jar" (
    echo ERROR: JAR file not found. Please build the project first:
    echo   mvn clean package -DskipTests
    pause
    exit /b 1
)

start "Java Backend" cmd /k "java -jar target\tdcloud-upms-biz.jar"
echo ✓ Java backend starting...

REM Wait a moment for backend to start
echo Waiting 10 seconds for backend to initialize...
timeout /t 10 /nobreak >nul

REM Start Vue.js frontend
echo Starting Vue.js frontend on port 8181...
cd /d "%~dp0map-table\map-table"
if not exist "node_modules" (
    echo Installing npm dependencies...
    npm install
)

start "Vue Frontend" cmd /k "npm run serve"
echo ✓ Vue.js frontend starting...

echo.
echo ========================================
echo Applications are starting up...
echo ========================================
echo.
echo Backend:  http://localhost:8888/admin
echo Frontend: http://localhost:8181
echo Testing:  http://localhost:8181/test-frontend-endpoints.html
echo.
echo Wait for both applications to fully start, then:
echo 1. Open http://localhost:8181 in your browser
echo 2. Test the paleoecology database interface
echo 3. Run automated tests at http://localhost:8181/test-frontend-endpoints.html
echo.
echo Press any key to open testing URLs...
pause >nul

REM Open testing URLs
start http://localhost:8181
start http://localhost:8181/test-frontend-endpoints.html

echo.
echo Testing URLs opened in browser.
echo Check the command windows for application logs.
echo.
pause
