/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.service;

import com.tdkj.tdcloud.admin.api.dto.PaleoecologyDataDTO;
import com.tdkj.tdcloud.common.core.util.R;

import java.util.List;

/**
 * Paleoecology Data Service Interface
 * Provides data query and lookup services for paleoecology research
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface PaleoecologyDataService {

    /**
     * Main data query endpoint with complex filtering
     * Equivalent to Python Flask /data endpoint
     *
     * @param queryParams Query parameters for filtering
     * @return Filtered paleoecology data
     */
    R getData(PaleoecologyDataDTO queryParams);

    /**
     * Get detail data from Chattian table by ID
     * Equivalent to Python Flask /detail endpoint
     *
     * @param id Record ID
     * @return Detail data
     */
    R getDetailData(String id);

    /**
     * Get unique dating method values
     * Equivalent to Python Flask /dating-methods endpoint
     *
     * @return List of unique dating methods
     */
    R getDatingMethods();

    /**
     * Get unique dating quality values
     * Equivalent to Python Flask /dating-qualities endpoint
     *
     * @return List of unique dating qualities
     */
    R getDatingQualities();

    /**
     * Get unique family values
     * Equivalent to Python Flask /taxa/families endpoint
     *
     * @return List of unique families
     */
    R getFamilies();

    /**
     * Get unique genus values
     * Equivalent to Python Flask /taxa/genera endpoint
     *
     * @return List of unique genera
     */
    R getGenera();

    /**
     * Get unique species values
     * Equivalent to Python Flask /taxa/species endpoint
     *
     * @return List of unique species
     */
    R getSpecies();

    /**
     * Get unique scientific name values
     * Equivalent to Python Flask /taxa/scientific-names endpoint
     *
     * @return List of unique scientific names
     */
    R getScientificNames();

    /**
     * Get unique original name values
     * Equivalent to Python Flask /taxa/original-names endpoint
     *
     * @return List of unique original names
     */
    R getOriginalNames();

    /**
     * Get unique fossil type values
     * Equivalent to Python Flask /fossil-types endpoint
     *
     * @return List of unique fossil types
     */
    R getFossilTypes();

    /**
     * Get unique plant organ values
     * Equivalent to Python Flask /plant-organs endpoint
     *
     * @return List of unique plant organs
     */
    R getPlantOrgans();
}
