<template>
  <div class="mapDetail">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/map' }">{{ $t('nav.paleoecologyNav') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $t('nav.leafCuticlDetailNav') }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="mapInfo">
      <div class="title">
        <div>Fossil assemblage information</div>
        <div class="download-percentage" v-if="infoContent.pollendiagram === 'Yes'" @click="downloadPercentageHandle">
          <span class="iconfont el-icon-download"></span>
          <span class="span-text">Download Percentage</span>
        </div>
      </div>
      <el-descriptions class="margin-top" title="" :column="4"  border>
        <el-descriptions-item label="ID" label-class-name="my-label" content-class-name="my-content">{{ infoContent.id }}</el-descriptions-item>
        <el-descriptions-item label="DatingMethod" label-class-name="my-label" content-class-name="my-content">{{ infoContent.datingmethod }}</el-descriptions-item>
        <el-descriptions-item label="EarlyInterval" label-class-name="my-label" content-class-name="my-content">{{ infoContent.earlyinterval }}</el-descriptions-item>
        <el-descriptions-item label="Author" label-class-name="my-label" content-class-name="my-content">{{ infoContent.author }}</el-descriptions-item>
        <el-descriptions-item label="SiteNo" label-class-name="my-label" content-class-name="my-content">{{ infoContent.siteno }}</el-descriptions-item>
        <el-descriptions-item label="DatingQuality" label-class-name="my-label" content-class-name="my-content">{{ infoContent.datingquality }}</el-descriptions-item>
        <el-descriptions-item label="LateInterval" label-class-name="my-label" content-class-name="my-content">{{ infoContent.lateinterval }}</el-descriptions-item>
        <el-descriptions-item label="Pubyr" label-class-name="my-label" content-class-name="my-content">{{ infoContent.pubyr }}</el-descriptions-item>
        <el-descriptions-item label="SiteName" label-class-name="my-label" content-class-name="my-content">{{ infoContent.sitename }}</el-descriptions-item>
        <el-descriptions-item label="Epoch" label-class-name="my-label" content-class-name="my-content">{{ infoContent.epoch }}</el-descriptions-item>
        <el-descriptions-item label="AgeMax(Ma)" label-class-name="my-label" content-class-name="my-content">{{ infoContent.agemax }}</el-descriptions-item>
        <el-descriptions-item label="Longitude" label-class-name="my-label" content-class-name="my-content">{{ infoContent.longitude }}</el-descriptions-item>
        <el-descriptions-item label="Country" label-class-name="my-label" content-class-name="my-content">{{ infoContent.country }}</el-descriptions-item>

        <el-descriptions-item label="Stage" label-class-name="my-label" content-class-name="my-content">{{ infoContent.stage }}</el-descriptions-item>
        <el-descriptions-item label="AgeMin(Ma)" label-class-name="my-label" content-class-name="my-content">{{ infoContent.agemin }}</el-descriptions-item>
        <el-descriptions-item label="Latitude" label-class-name="my-label" content-class-name="my-content">{{ infoContent.latitude }}</el-descriptions-item>
        <el-descriptions-item label="FossilType" label-class-name="my-label" content-class-name="my-content">{{ infoContent.fossiltype }}</el-descriptions-item>
        <el-descriptions-item label="TimeBin" label-class-name="my-label" content-class-name="my-content">{{ infoContent.timebin }}</el-descriptions-item>
        <el-descriptions-item label="AgeMiddle(Ma)" label-class-name="my-label" content-class-name="my-content">{{ infoContent.agemiddle }}</el-descriptions-item>
        <el-descriptions-item label="PollenDiagram" label-class-name="my-label" content-class-name="my-content">{{ infoContent.pollendiagram }}</el-descriptions-item>
        <el-descriptions-item label="Reference" label-class-name="my-label" content-class-name="my-content">
          {{ combinedReferences }}
        </el-descriptions-item>
<!--        <el-descriptions-item label="Reference2" label-class-name="my-label" content-class-name="my-content">{{ infoContent.reference2 }}</el-descriptions-item>-->
<!--        <el-descriptions-item label="Reference3" label-class-name="my-label" content-class-name="my-content">{{ infoContent.reference3 }}</el-descriptions-item>-->
<!--        <el-descriptions-item label="OtherReferences" label-class-name="my-label" content-class-name="my-content">{{ infoContent.otherreferences }}</el-descriptions-item>-->
<!--        <el-descriptions-item label="Source" label-class-name="my-label" content-class-name="my-content">{{ infoContent.source }}</el-descriptions-item>-->
<!--        <el-descriptions-item label="SourceID" label-class-name="my-label" content-class-name="my-content">{{ infoContent.sourceid }}</el-descriptions-item>-->
<!--        <el-descriptions-item label="Collectionme" label-class-name="my-label" content-class-name="my-content">{{ infoContent.collectionme }}</el-descriptions-item>-->
    </el-descriptions>
    </div>
    <div class="mapChildInfo">
      <div class="mapChildInfoTop">
        <div class="title">Fossiltaxa information</div>
        <div class="btn" @click="downloadHandle"> <span class="iconfont el-icon-download"></span>Download</div>
      </div>
      <el-table :data="dataList" style="width: 100%" :header-cell-style="{background: '#F2F3F5',color: '#333333'}" >
        <el-table-column fixed="left" width="180" show-overflow-tooltip prop="originalname" header-align="center" align="center" label="OriginalName"></el-table-column>
        <el-table-column width="180" show-overflow-tooltip prop="scientificname1" header-align="center" align="center" label="ScientificName">
          <template slot-scope="scope">
            <div>{{ getCombinedScientificname(scope.row) }}</div>
          </template>
        </el-table-column>
<!--        <el-table-column width="180" show-overflow-tooltip prop="scientificname2" header-align="center" align="center" label="scientificname2"></el-table-column>-->
<!--        <el-table-column width="180" show-overflow-tooltip prop="scientificname3" header-align="center" align="center" label="scientificname3"></el-table-column>-->
        <el-table-column width="180" show-overflow-tooltip prop="acceptedrank" header-align="center" align="center" label="AcceptedRank"></el-table-column>
        <el-table-column prop="phylum" width="140" show-overflow-tooltip header-align="center" align="center" label="Phylum"></el-table-column>
        <el-table-column width="180" prop="classNew" header-align="center" align="center" label="Class"></el-table-column>
        <el-table-column width="180" prop="orderNew" header-align="center" align="center" label="Order"></el-table-column>
        <el-table-column prop="family" width="140" show-overflow-tooltip header-align="center" align="center" label="Family"></el-table-column>
        <el-table-column prop="genus" header-align="center" align="center" label="Genus"></el-table-column>
        <el-table-column prop="species1" width="180" show-overflow-tooltip header-align="center" align="center" label="Species">
          <template slot-scope="scope">
            <div>{{ getCombinedSpecies(scope.row) }}</div>
<!--            <div>{{ (scope.row.species1 !== '' && scope.row.species1 ) ?  scope.row.species1 + ',' : ''}}{{(scope.row.species2 !== '' && scope.row.species2) ?  scope.row.species2 + ',' : ''}}{{(scope.row.species3 !== '' && scope.row.species3) ?  scope.row.species3 : ''}}</div>-->
          </template>
        </el-table-column>
<!--        <el-table-column prop="species2" header-align="center" align="center" label="species2"></el-table-column>-->
<!--        <el-table-column prop="species3" header-align="center" align="center" label="species3"></el-table-column>-->
        <el-table-column width="180" show-overflow-tooltip prop="plantorgan1" header-align="center" align="center" label="PlantOrgan">
          <template slot-scope="scope">
            <div>{{ getCombinedPlantorgan(scope.row) }}</div>
          </template>
        </el-table-column>
<!--        <el-table-column width="180" show-overflow-tooltip prop="plantorgan2" header-align="center" align="center" label="plantorgan2"></el-table-column>-->
        <el-table-column width="180" show-overflow-tooltip prop="abundvalue" header-align="center" align="center" label="AbundValue"></el-table-column>
        <el-table-column width="180" prop="abundunit" header-align="center" align="center" label="AbundUnit"></el-table-column>
        <el-table-column width="180" show-overflow-tooltip prop="fossiltype" header-align="center" align="center" label="FossilType"></el-table-column>
<!--        <el-table-column width="180" show-overflow-tooltip prop="pollendiagram" header-align="center" align="center" label="PollenDiagram"></el-table-column>-->
<!--        <el-table-column width="180" show-overflow-tooltip prop="assemblage" header-align="center" align="center" label="assemblage"></el-table-column>-->
        <el-table-column prop="siteno" header-align="center" align="center" label="SiteNo"></el-table-column>
        <el-table-column prop="sitename" width="180" show-overflow-tooltip header-align="center" align="center" label="SiteName"></el-table-column>
        <el-table-column prop="extinct" header-align="center" align="center" label="Extinct"></el-table-column>
        <el-table-column  fixed="right" width="180" show-overflow-tooltip prop="timecontext" header-align="center" align="center" label="TimeContext"></el-table-column>

      </el-table>
      <el-pagination style="text-align: right;margin-top: 16px;"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page.sync="pageIndex" background
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalPage">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getChattianList, getRupelianObj} from "@/api/map";
import {downBlobFile} from "@/util";
import {mapMutations} from "vuex";

export default {
  name: "mapDetail",
  data() {
    return {
      infoContent: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      searchForm: {}
    }
  },
  mounted() {
    this.getRupelianObj()
    // this.getChattianList()
  },
  computed: {
    combinedReferences() {
      const { reference1, reference2, reference3,otherreferences } = this.infoContent;
      const references = [reference1, reference2, reference3, otherreferences].filter(ref => ref && ref!== '');
      return references.join('， ');
    },
    getDate() {
      const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const dateStr = `${year}${month}${day}`;
            return dateStr;
    }
  },
  methods: {
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    async getRupelianObj() {
      this.dataListLoading = true
      let res = await getRupelianObj(this.$route.params.ID)

      if(res.data.code === 0) {
        this.infoContent = res.data.data.data
        this.dataListLoading = false
        await this.getChattianList()
      }
    },
    getChattianList() {
      getChattianList(Object.assign({
        current: this.pageIndex,
        size: this.pageSize,
        id: this.$route.params.ID
      })).then(res => {
        this.dataList = res.data.data.records
        this.totalPage = res.data.data.total
      })
      this.dataListLoading = false
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getChattianList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getChattianList()
    },
    downloadPercentageHandle() {
      if(this.$store.state.userInfo && this.$store.state.userInfo.username && this.$store.state.userInfo.username !== '') {
        if(this.infoContent.sysFileList.length > 0) {
          downBlobFile(
              "/admin/sys-file/" + this.infoContent.sysFileList[0].bucketName + "/" + this.infoContent.sysFileList[0].fileName,
              this.searchForm,
              this.infoContent.sysFileList[0].original
          );
        }else {
          return false
        }

      } else {
        this.$message.error('您还未登录，登录成功后才可下载数据！')
        this.SET_OPEN_LOGIN_BOX(true)
      }
        //  导出excel
    },
    downloadHandle() {
      if(this.$store.state.userInfo && this.$store.state.userInfo.username && this.$store.state.userInfo.username !== '') {
        //  导出excel
        this.searchForm.id = this.$route.params.ID
        downBlobFile('/admin/chattian/export', this.searchForm, `Species_information_${this.getDate}.xlsx`)
      }else {
        this.$message.error('您还未登录，登录成功后才可下载数据！')
        this.SET_OPEN_LOGIN_BOX(true)
      }


    },
    getCombinedSpecies(row) {
      const speciesKeys = ['species1', 'species2', 'species3'];
      const validSpecies = speciesKeys
          .map(key => row[key])
          .filter(value => value && value!== '');
      return validSpecies.join('， ');
    },
    getCombinedPlantorgan(row) {
      const speciesKeys = ['plantorgan1', 'plantorgan1'];
      const validSpecies = speciesKeys
          .map(key => row[key])
          .filter(value => value && value!== '');
      return validSpecies.join('， ');
    },
    getCombinedScientificname(row) {
      const speciesKeys = ['scientificname1', 'scientificname2', 'scientificname3'];
      const validSpecies = speciesKeys
          .map(key => row[key])
          .filter(value => value && value!== '');
      return validSpecies.join('， ');
    }
  }
}
</script>
<style>
.table-footer .el-pagination .el-pager .active{
  background-color: #304F56 !important;
}
</style>
<style scoped lang="scss">


::v-deep .my-label {
  background-color: #EFF5FA !important;
  color: #6B7280 !important;
  border-color: #C3D3E2 !important;
  width: 125px !important;
}
::v-deep .my-content {
  color: #333 !important;
}
::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
  border-color: #C3D3E2 !important;
}
.mapDetail {
  min-height: calc(100vh - 272px - 280px);
  background-color: var(--about-background-color);
  padding: 50px 80px;
}
.mapInfo {
  margin: 30px 0;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  border-top: 3px solid #304F56;
  padding: 20px;

  .title {
    font-size: 16px;
    color: #333333;
    margin-left: 10px;
    text-align: left;
    margin-top: 10px;
    padding-left: 10px;
    border-left: 4px solid #304F56;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .download-percentage {
      color: var(--primary-text-color);
      cursor: pointer;
      span{
        margin-right: 8px;
        font-size: 18px;
        font-weight: 700;
      }
      .span-text {
        font-weight: 400;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
.mapChildInfo {
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  border-top: 3px solid #304F56;
  padding: 20px;
  .mapChildInfoTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .title {
    font-size: 16px;
    color: #333333;
    margin-left: 10px;
    text-align: left;
    margin-top: 10px;
    padding-left: 10px;
    border-left: 4px solid #304F56;
    margin-bottom: 20px;
  }
  .btn {
    display: inline-block;
    cursor: pointer;
    font-size: 18px;
    &:hover {
      text-decoration: underline;
    }
    .iconfont {
      margin-right: 4px;
      font-weight: 700 ;
    }
  }
}
</style>