# Field Name Mapping Fixes for TableView.vue

## Issue Resolved
Fixed the field name mismatch between the API response (lowercase field names) and the table column properties (camelCase names) in the expandable table rows.

## Root Cause
The Java Spring Boot backend returns data with lowercase field names, but the Vue.js table columns were configured with camelCase property names, causing the data not to display.

## API Response Format (Confirmed)
```json
{
  "code": 0,
  "msg": null,
  "data": {
    "detail": [
      {
        "cid": 200,
        "id": "6",
        "originalname": "Caryapollenites",
        "scientificname1": "Carya",
        "scientificname2": null,
        "scientificname3": null,
        "acceptedrank": "genus",
        "phylum": "Angiosperms",
        "classNew": null,
        "orderNew": null,
        "family": "Juglandaceae",
        "genus": "Carya",
        "species1": null,
        "species2": null,
        "species3": null,
        "plantorgan1": null,
        "plantorgan2": null,
        "abundvalue": null,
        "abundunit": null,
        "fossiltype": "Pollen",
        "pollendiagram": null,
        "siteno": "3",
        "sitename": "Tangyuan-9",
        "extinct": "L",
        "timecontext": null
      }
    ]
  }
}
```

## Field Name Mappings Applied

### ✅ Fixed Table Column Properties
| Original (Incorrect) | Fixed (Correct) | API Field Name |
|---------------------|-----------------|----------------|
| `OriginalName` | `originalname` | `originalname` |
| `AcceptedRank` | `acceptedrank` | `acceptedrank` |
| `Phylum` | `phylum` | `phylum` |
| `Class` | `classNew` | `classNew` |
| `Order` | `orderNew` | `orderNew` |
| `Family` | `family` | `family` |
| `Genus` | `genus` | `genus` |
| `AbundValue` | `abundvalue` | `abundvalue` |
| `AbundUnit` | `abundunit` | `abundunit` |
| `FossilType` | `fossiltype` | `fossiltype` |
| `Assemblage` | `pollendiagram` | `pollendiagram` |
| `SiteNo` | `siteno` | `siteno` |
| `SiteName` | `sitename` | `sitename` |
| `Extinct` | `extinct` | `extinct` |
| `TimeContext` | `timecontext` | `timecontext` |

### ✅ Fixed Combined Field Processing
Updated `combineFields` function calls to use lowercase field names:

**ScientificName Fields**:
- `scientificname1`, `scientificname2`, `scientificname3` → Combined into `ScientificName`

**Species Fields**:
- `species1`, `species2`, `species3` → Combined into `Species`

**PlantOrgan Fields**:
- `plantorgan1`, `plantorgan2` → Combined into `PlantOrgan`

## Code Changes Applied

### 1. **Table Column Properties**
```html
<!-- Before (Incorrect) -->
<el-table-column prop="OriginalName" label="OriginalName"></el-table-column>
<el-table-column prop="AcceptedRank" label="AcceptedRank"></el-table-column>
<el-table-column prop="Phylum" label="Phylum"></el-table-column>

<!-- After (Fixed) -->
<el-table-column prop="originalname" label="OriginalName"></el-table-column>
<el-table-column prop="acceptedrank" label="AcceptedRank"></el-table-column>
<el-table-column prop="phylum" label="Phylum"></el-table-column>
```

### 2. **Combined Field Processing**
```javascript
// Before (Incorrect)
processedItem.ScientificName = this.combineFields(item, "ScientificName", 3);
processedItem.Species = this.combineFields(item, "Species", 3);
processedItem.PlantOrgan = this.combineFields(item, "PlantOrgan", 2);

// After (Fixed)
processedItem.ScientificName = this.combineFields(item, "scientificname", 3);
processedItem.Species = this.combineFields(item, "species", 3);
processedItem.PlantOrgan = this.combineFields(item, "plantorgan", 2);
```

### 3. **combineFields Function Logic**
The function now correctly looks for:
- `scientificname1`, `scientificname2`, `scientificname3`
- `species1`, `species2`, `species3`
- `plantorgan1`, `plantorgan2`

## Expected Results

### ✅ Before Fix
- Test column showed "Row 1", "Row 2", "Row 3" (table was working)
- Data columns were empty (field name mismatch)
- Debug info showed data was being fetched and processed correctly

### ✅ After Fix
- All data columns should now display the actual API data
- Combined fields (ScientificName, Species, PlantOrgan) should show comma-separated values
- Original field names should display correctly

## Testing Verification

### 1. **Expand a Table Row**
- Click the expand icon (▶) on any table row
- The expanded section should now show populated data columns

### 2. **Check Data Display**
- **OriginalName**: Should show values like "Caryapollenites"
- **ScientificName**: Should show combined values like "Carya"
- **AcceptedRank**: Should show values like "genus"
- **Family**: Should show values like "Juglandaceae"
- **FossilType**: Should show values like "Pollen"

### 3. **Verify Combined Fields**
- **ScientificName**: Should combine `scientificname1`, `scientificname2`, `scientificname3`
- **Species**: Should combine `species1`, `species2`, `species3`
- **PlantOrgan**: Should combine `plantorgan1`, `plantorgan2`

## Debug Information Cleanup

The debugging code added earlier should now show:
- Data is being fetched correctly ✅
- Data is being processed correctly ✅
- Data is being displayed correctly ✅

Once confirmed working, the debug code can be removed:
- Console.log statements in `fetchChildTableData`
- Console.log statements in `processedChildTableData`
- Visual debug info boxes in the template

## Summary

The expandable table rows should now properly display detail data from the `/admin/paleoecology/detail?id=X` API endpoint. The field name mismatch has been resolved by updating the table column `prop` attributes to match the exact lowercase field names returned by the Java Spring Boot backend.

**Key Fix**: Changed table column properties from camelCase to match the exact API field names (lowercase with some exceptions like `classNew` and `orderNew`).
