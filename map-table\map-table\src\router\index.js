import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/home/<USER>'
import store from "@/store";

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}
Vue.use(VueRouter)
const routes = [
    {
        path: '/',
        name: 'home',
        component: Home,
        meta: {
            isHidden: true
        }
    },
    {
        path: '/about',
        name: 'about',
        component: () => import(/* webpackChunkName: "about" */ '../views/about/index.vue'),
    },
    {
        path: '/map',
        name: 'map',
        component: () => import(/* webpackChunkName: "map" */ '../views/map/index.vue'),
    },
    {
        path: '/mapDetail/:ID',
        name: 'mapDetail',
        component: () => import(/* webpackChunkName: "mapDetail" */ '../views/map/mapDetail.vue'),
    },
    {
        path: '/team',
        name: 'team',
        component: () => import(/* webpackChunkName: "team" */ '../views/team/index.vue'),
    },
    {
        path: '/leafCuticle',
        name: 'leafCuticle',
        component: () => import(/* webpackChunkName: "leafCuticle" */ '../views/leafCuticle/index.vue'),
        meta: {
            customization: true,
            isHidden: true

        }
    },
    {
        path: '/education',
        name: 'education',
        component: () => import(/* webpackChunkName: "education" */ '../views/education/index.vue'),
    },
    {
        path: '/teamDetail/:id',
        name: 'teamDetail',
        component: () => import(/* webpackChunkName: "teamDetail" */ '../views/teamDetail/index.vue'),
    },
    {
        path: '/educationDetail/:id',
        name: 'educationDetail',
        component: () => import(/* webpackChunkName: "educationDetail" */ '../views/educationDetail/index.vue'),
    },
    {
        path: '/leafCuticleDetail/:id',
        name: 'leafCuticleDetail',
        component: () => import(/* webpackChunkName: "leafCuticlDetail" */ '../views/leafCuticlDetail/index.vue'),
        meta: {
            customization: true,
            isHidden: true
        }
    },
    {
        path: '/declaration',
        name: 'Citation Method Declaration',
        component: () => import(/* webpackChunkName: "citationMethodDeclaration" */ '../views/declaration.vue'),
    },
    {
        path: '/userInfo',
        name: 'UserInfo',
        component: () => import(/* webpackChunkName: "UserInfo" */ '../views/userInfo/index.vue'),
        meta: {
            customization: false,
            isHidden: true,
            requiresAuth: true // 添加需要认证的元字段
        }
    },

]
const router = new VueRouter({
    mode: 'hash',
    base: process.env.BASE_URL,
    routes
})
// 添加全局前置守卫
router.beforeEach((to, from, next) => {
    // 检查目标路由是否需要认证
    if (to.matched.some(record => record.meta.requiresAuth)) {
        // 这里假设你有一个方法来检查用户是否已登录
        // 例如从 Vuex store 或 localStorage 中检查
        const isAuthenticated = checkAuth(); // 你需要实现这个函数

        if (!isAuthenticated) {
            // 如果未登录，先控制 Vuex 状态打开登录弹框
            store.commit('SET_OPEN_LOGIN_BOX', true); // 调用 Vuex mutation 更新状态

            // 然后重定向到首页
            next({
                path: '/',
            });
        } else {
            // 已登录，继续导航
            next();
        }
    } else {
        // 不需要认证的路由，直接放行
        next();
    }
});
// 检查用户是否已登录的函数示例
function checkAuth() {
    // 检查1：Vuex 中的 access_token
    const hasToken = store.state.access_token && store.state.access_token !== ''
    // 检查2：sessionStorage 中的用户信息
    const hasUserInfo = sessionStorage.getItem('tdcloud-userInfo') !== null
    return hasToken && hasUserInfo
}

export default router