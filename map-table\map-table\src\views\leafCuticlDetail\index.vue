<template>
  <div class="leaf-detail">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/leafCuticle' }">{{ $t('nav.leafCuticlNav') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $t('nav.leafCuticlDetailNav') }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="leaf-detail-main">
      <div class="info" title="版权和引用方法声明" @click="goDeclaration"><span class=" info-icon" >i</span></div>
      <div class="name">{{ infoContent.speciesCn }} {{ infoContent.speciesEn }}</div>
      <div class="source">
        <span>{{ infoContent.familyCn }} {{ infoContent.familyEn }} </span>||
        <span>{{ infoContent.genusCn }} {{ infoContent.genusEn }}</span>
      </div>
      <div class="pic-group">
        <el-carousel style="padding-top: 75%;position: relative" :autoplay="false">
          <el-carousel-item  v-for="(item,index) in infoContent.picList" :key="index">
<!--            <h3 class="pic-group-tag"> {{ item.name? item.name.split('.')[0].split('_').slice(-2).join('_') : '' }}</h3>-->
            <h3 class="pic-group-tag"> {{ item.result}}</h3>
            <div class="pic-group-enlarge"><span class="enlarge-icon el-icon-zoom-in"></span></div>
            <el-image  class="pic-group-item" :src="item.url"  :preview-src-list="[item.url]" />
            <div class="pic-group-download" @click="downloadPic(item)"><i class="el-icon-download download-icon"></i></div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <section class="collect card">
        <div class="card-tag">标本号 Specimen No.</div>
        <div class="card-con">{{ infoContent.specimenNo }}</div>
      </section>
      <section class="collect card">
        <div class="card-tag">采集地点 Place of Collection</div>
        <div class="card-con">{{ infoContent.collectionPlace }} {{ infoContent.placeEn }}</div>
      </section>
      <section class="collect card">
        <div class="card-tag">采集人 Collector</div>
        <div class="card-con">{{ infoContent.collector }} {{ infoContent.collectorEn }}</div>
      </section>
    </div>
  </div>
</template>

<script>
import {getObj} from "@/api/leafCuticl";
import { downBlobFile } from '@/util/index'

export default {
  name: "leafCuticlDetail",
  data() {
    return {
      infoContent: {}
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    downloadPic(item) {
        downBlobFile(
            "/admin/sys-file/" + item.bucketName + "/" + item.fileName,
            this.searchForm,
            item.name
        );
    },
    goDeclaration() {
      this.$router.push('/declaration')
    },
    getInfo() {
      getObj(this.$route.params.id).then(res => {
        if(res.data.code === 0) {
          this.infoContent = res.data.data
          this.infoContent.picList = [...res.data.data.upSysFileList, ...res.data.data.lwSysFileList]
        }
      })
    },
    getTag(val) {
      const str = val
      const parts = str.split('.')[0].split('_')
      const result = parts.slice(-2).join('_')
      return result
    }
  }
}
</script>
<style>
 .el-image-viewer__close {
  color: #333333 !important;
  background-color: #ffffff !important;
}
  .el-image-viewer__img {
  max-height: 95% !important;
  padding: 16px !important;
  background-color: #fff !important;
    border-radius: 8px !important;
}
</style>
<style scoped lang="scss">

::v-deep .el-breadcrumb__inner {
  font-size: 16px !important;
}
::v-deep .el-carousel__arrow {
  width: 44px;
  height: 44px;
  background-color: #fff;
}
::v-deep .el-icon-arrow-left ,::v-deep .el-icon-arrow-right{
  font-size: 18px;
  color: rgba(0, 0, 0, .5) !important;
  font-weight: 600;
}
.leaf-detail {
  min-height: calc(100vh - 272px - 280px);
  background-color: var(--about-background-color);
  padding: 50px 80px;
  .leaf-detail-main {
    margin-top: 30px;
    text-align: left;
    position: relative;
    .info {
      display: inline-block;
      position: absolute;
      right: 4px;
      top: 30px;
      cursor: pointer;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 2px solid #304f56;
      .info-icon {
        display: block;
        font-size: 22px;
        text-align: center;
        line-height: 24px;
        color: #304f56;
        margin: 0 auto;
      }
    }
    .name {
      font-size: 24px;
      color: #333333;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .source {
      font-size: 14px;
      color: var(--primary-text-color);
      margin-bottom: 30px;
    }
    .pic-group {
      position: relative;
      ::v-deep .el-carousel__container {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
      }
      .pic-group-tag {
        position: absolute;
        z-index: 100;
        left: 16px;
        color: #ffffff;
      }
      .pic-group-enlarge {
        position: absolute;
        z-index: 100;
        top: 0;
        right: 0;
        width: 40px;
        height: 40px;
        background-color: #E8D9E0;
        text-align: center;
        border-bottom-left-radius: 4px;
        border-top-right-radius: 4px;
        cursor: pointer;
        .enlarge-icon {
          line-height: 40px;
          font-size: 26px;
          color: #953C55;
        }
      }
      .pic-group-item{
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        object-position: center !important;
        border-radius: 8px;
        ::v-deep .el-image__preview {
          width: 100% !important;
          height: 100% !important;
          object-fit: cover !important;
          object-position: center !important;

        }
      }
      .pic-group-download {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 10px;
        background-color: rgba(0,0,0,.3);
        color: #FFFFFF;
        border-top-right-radius: 8px;
        border-bottom-left-radius: 8px;
        cursor: pointer;
        .download-icon {
          font-size: 24px;

        }
      }
    }
    .card {
      margin-top: 20px;
      background-color: #fff;
      padding: 20px;
      .card-tag {
        padding-left: 14px;
        font-size: 16px;
        color: #333;
        border-left: 4px solid var(--primary-text-color);
        margin-bottom: 20px;
      }
      .card-con {
        font-size: 15px;
        color: #333;
        line-height: 1.6;
      }
    }
    .map {
      padding: 20px 0 0;
      .card-tag {
        margin-left: 20px;
      }
      .card-pic {
        width: 100%;
        height: 100%;
        .map-img {
          width: 100%;
          height: 100%;
          object-position: center;
          object-fit: cover;
          display: block;
        }
      }
    }
  }
}

</style>