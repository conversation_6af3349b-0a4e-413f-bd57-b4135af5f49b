<template>
  <el-upload
    class="upload-demo"
      :action="uploadUrl"
    :before-upload="beforeUpload"
    :file-list="fileList"
    :limit="1"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-change="handleChange"
    :on-remove="handleRemove"
    :show-file-list="true"
    :auto-upload="false"
  >
    <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
    <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
    <div slot="tip" class="el-upload__tip">只支持MP4、MPEG格式、AVI格式、nAVI格式、WMV格式、
      3GP格式、RM格式与RMVB格式、FLV/F4V格式、H.264、H.265、MOV格式ASF格式的视频文件，且不超过200M</div>
  </el-upload>
</template>

<script>
import {newUploadVideos} from "@/api/common";
import {delObj} from "@/api/admin/sys-file";

export default {
  name: "videoUpload",
  props: {
    fileList: Array,
    type: String
  },
  data() {
    return {
      uploadUrl: '/admin/sys-file/newUploadVideos',
      chooseFileList: {}
    };
  },
  methods: {

    beforeUpload(file) {
      const isLt200M = file.size / 1024 / 1024 < 200;
      const acceptedFormats = ['video/mpeg', 'video/x-msvideo', 'video/x-ms-wmv', 'video/3gpp', 'video/x-flv', 'video/mp4', 'video/quicktime', 'video/x-ms-asf'];
      const isAcceptedFormat = acceptedFormats.includes(file.raw.type);
      if (!isLt200M) {
        this.$message.error('上传视频大小不能超过 200MB!');
      }
      if (!isAcceptedFormat) {
        this.$message.error('上传视频只能是 MPEG/AVI/WMV/3GP/FLV/MP4/MOV/ASF 格式!');
      }
      return isLt200M && isAcceptedFormat;
    },
    handleChange(file, fileList) {
      // console.log(file)
      this.chooseFileList = fileList
      const isLt200M = file.size / 1024 / 1024 < 200;
      const acceptedFormats = ['video/mpeg', 'video/x-msvideo', 'video/x-ms-wmv', 'video/3gpp', 'video/x-flv', 'video/mp4', 'video/quicktime', 'video/x-ms-asf'];
      const isAcceptedFormat = acceptedFormats.includes(file.raw.type);
      if (!isLt200M) {
        this.$message.error('上传视频大小不能超过 200MB!');
      }
      if (!isAcceptedFormat) {
        this.$message.error('上传视频只能是 MPEG/AVI/WMV/3GP/FLV/MP4/MOV/ASF 格式!');
      }
      return isLt200M && isAcceptedFormat;
    },
    handleSuccess(response, file, fileList) {

    },
    handleError(err, file, fileList) {

    },
    async handleRemove(file, fileList) {
      // console.log('67',file, fileList,this.chooseFileList)
      if(file.id) {
        // console.log('服务器')
        try {
          // 调用 API 来从服务器删除文件
          await delObj(file.id)
          // this.$emit('fileRemoved', file);
        } catch (error) {
          // console.error('Error deleting file from server:', error);
        }
      }else {
        // console.log('no')
      const index = this.chooseFileList.indexOf(file);
        this.chooseFileList.splice(index, 1);
      }
      // 如果文件在 chooseFileList 中，说明它还没有上传到服务器
      // }
    },

    async submitUpload() {
      const file = []
      file.push(this.uploadFile) // 获取要上传的文件
      const formData = new FormData();
      // formData.append('files', file); // 添加文件到FormData对象中
      // formData.append('type', this.type); // 添加文件到FormData对象中
      let  uploadFiles = this.chooseFileList
      uploadFiles.forEach((i)=>{
        formData.append("files",i.raw)
        formData.append('type', this.type); // 添加文件到FormData对象中
      })
      let loading = this.$loading({
        lock:true,
        text:'上传中，请稍等',
        background:'rgba(0,0,0,0.5)'
      })
      try {
        const res = await newUploadVideos(formData);
        if(res.data.code === 0) {
          let fileInfoList = []
          for (let file of res.data.data){
            fileInfoList.push(file)
          }
          this.$message.success('上传成功')
          loading.close()
          this.$emit("successUpload", fileInfoList)
        }
      } catch (error) {
        // console.error('Error uploading video:', error);
      }

    },
  }
}
</script>

<style scoped>

</style>
