<template>
  <el-dialog
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
#foreach($column in $columns)
  #if($column.columnName != $pk.columnName)
    <el-form-item label="${column.comments}" prop="${column.lowerAttrName}">
      <el-input v-model="dataForm.${column.lowerAttrName}" placeholder="${column.comments}"></el-input>
    </el-form-item>
  #end
#end
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/${pathName}'

  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          #foreach($column in $columns)
            #if($column.columnName == $pk.columnName)
                ${column.lowerAttrName}: 0,
            #else
                ${column.lowerAttrName}: ''#if($velocityCount != $columns.size()),#end
            #end
          #end
        },
        dataRule: {
          #foreach($column in $columns)
            #if($column.columnName != $pk.columnName)
                ${column.lowerAttrName}: [
                {required: true, message: '${column.comments}不能为空', trigger: 'blur'}
              ]#if($velocityCount != $columns.size()),#end
            #end
          #end
        }
      }
    },
    methods: {
      init(id) {
        this.dataForm.${pk.lowerAttrName} = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.${pk.lowerAttrName}) {
            getObj(this.dataForm.${pk.lowerAttrName}).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
      #[[this.$refs['dataForm'].validate((valid) => {]]#
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.${pk.lowerAttrName}) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
