<!--
  -    Copyright (c) 2018-2025, tdcloud All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the pig4cloud.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: tdcloud
  -->
<template>
  <div class="execution">
    <basic-container>
      <avue-crud
        ref="crud"
        :page.sync="page"
        :data="tableData"
        :permission="permissionList"
        :table-loading="tableLoading"
        :option="tableOption"
        :upload-after="uploadAfter"
        @on-load="getList"
        @search-change="searchChange"
        @refresh-change="refreshChange"
        @size-change="sizeChange"
        @current-change="currentChange"
        @row-del="rowDel"
      >
        <template slot="menuLeft" slot-scope="scope">
          <el-button type="primary"
            size="small"
            icon="el-icon-upload"
            @click="picDialogVisible = true"
          >上传
          </el-button>
        </template>
        <template slot="menu" slot-scope="scope">
          <el-button
            type="text"
            size="small"
            icon="el-icon-download"
            @click="download(scope.row, scope.index)"
            >下载
          </el-button>
        </template>
      </avue-crud>
      <el-dialog :visible="picDialogVisible" title="文件上传" center @close="closeDialog">
        <el-upload
          class="upload-demo"
          ref="upload"
          action=""
          :on-remove="removeHandle"
          :on-change="handleChange"
          :multiple="true"
          :file-list="imgUrl"
          :show-file-list="true"
          :auto-upload="false">
          <el-button slot="trigger" size="small" type="primary">选取图片</el-button>
          <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">上传到服务器</el-button>
          <div slot="tip" class="el-upload__tip">
            <span style="color: #333;font-size: 15px">请上传文件，且文件大小不超过200MB。</span>
          </div>

        </el-upload>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
import { delObj, fetchList } from "@/api/admin/sys-file";
import { tableOption } from "@/const/crud/admin/sys-file";
import { mapGetters } from "vuex";
import { uploadSpecimenFiles } from '@/api/common'
export default {
  name: "sys-file",
  data() {
    return {
      searchForm: {},
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20 // 每页显示多少条
      },
      tableLoading: false,
      tableOption: tableOption,
      imgUrl: [],
      picDialogVisible: false,
      chooseFiles: []
    };
  },
  computed: {
    ...mapGetters(["permissions"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permissions.sys_file_add, true),
        delBtn: this.vaildData(this.permissions.sys_file_del, true),
        editBtn: this.vaildData(this.permissions.sys_file_edit, false)
      };
    }
  },
  methods: {
    handleChange(file, fileList){
      const allowedExtensions = ['png', 'jpg', 'jpeg'];
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        this.$message.error('只允许上传 png、jpg、jpeg 格式的文件');
        return;
      }
      // 限制文件大小不超过 200MB
      const maxSize = 200 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过 200MB');
        return;
      }
      // 只有同时满足格式和大小要求才更新 chooseFiles
      if (allowedExtensions.includes(extension) && file.size <= maxSize) {
        this.chooseFiles = fileList;
      }else {
        this.chooseFiles = [];
        return false
      }
    },
    submitUpload(){
      let formData = new FormData();
      let  uploadFiles = this.chooseFiles
      uploadFiles.forEach((i,index)=>{
        formData.append("files",i.raw)
      })
      const loading=this.$loading({
        lock: true,
        text: '文件正在上传，请勿关闭',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadSpecimenFiles(formData).then(res=>{
        if (res.data.code === 0 || res.data.code === 200){
          this.$message({
            message: '上传成功',
            type: 'success'
          });

          // this.$emit('successUpload', res.data.data);
          this.imgUrl = res.data.data
          this.getList(this.page)
            loading.close()
          this.picDialogVisible = false

        }
      })
    },
    removeHandle(file) {
      if (file.id) {
        delObj(file.id).then(res => {
          this.$message.success('移除成功');
          this.getList(this.page);
          this.picDialogVisible = false
        });
      }
      // 从本地 fileList 中移除文件
      this.imgUrl = this.imgUrl.filter(item => item.uid !== file.uid);
      this.getList(this.page);
    },
    closeDialog() {
      this.picDialogVisible =false
      this.getList(this.page)

    },
    getList(page, params) {
      this.tableLoading = true;
      fetchList(
        Object.assign(
          {
            descs: "create_time",
            current: page.currentPage,
            size: page.pageSize
          },
          params,
          this.searchForm
        )
      )
        .then(response => {
          this.tableData = response.data.data.records;
          this.page.total = response.data.data.total;
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    rowDel: function(row, index) {
      let _this = this;
      this.$confirm("是否确认删除ID为" + row.id, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function() {
          return delObj(row.id);
        })
        .then(data => {
          _this.$message.success("删除成功");
          this.getList(this.page);
        });
    },
    searchChange(form, done) {
      this.searchForm = form;
      this.page.currentPage = 1;
      this.getList(this.page, form);
      done();
    },
    refreshChange() {
      this.getList(this.page);
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    currentChange(current) {
      this.page.currentPage = current;
    },
    download: function(row, index) {
      this.downBlobFile(
        "/admin/sys-file/" + row.bucketName + "/" + row.fileName,
        this.searchForm,
        row.fileName
      );
    },
    uploadAfter(res, done, loading) {
      if (!this.validatenull(res.fileName)) {
        this.$message.success("上传成功");
        this.getList(this.page);
      }
      done();
    }
  }
};
</script>
