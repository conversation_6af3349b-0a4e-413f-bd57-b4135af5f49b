/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:20:56
 */
@Data
@TableName("chattian")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class Chattian extends Model<Chattian> {

    private static final long serialVersionUID = 1L;

    @Schema(description="cid")
	@ExcelIgnore
    private Integer cid;
    /**
     * id
     */
    @Schema(description="id")
	@ExcelProperty("ID")
    private String id;

    /**
     * originalname
     */
    @Schema(description="originalname")
	@ExcelProperty("Originalname")
    private String originalname;

    /**
     * scientificname1
     */
    @Schema(description="scientificname1")
	@ExcelProperty("Scientificname1")
    private String scientificname1;

    /**
     * scientificname2
     */
    @Schema(description="scientificname2")
	@ExcelProperty("Scientificname2")
    private String scientificname2;

    /**
     * scientificname3
     */
    @Schema(description="scientificname3")
	@ExcelProperty("Scientificname3")
    private String scientificname3;

    /**
     * acceptedrank
     */
    @Schema(description="acceptedrank")
	@ExcelProperty("Acceptedrank")
    private String acceptedrank;

    /**
     * phylum
     */
    @Schema(description="phylum")
	@ExcelProperty("Phylum")
    private String phylum;

    /**
     * classNew
     */
    @Schema(description="classNew")
	@ExcelProperty("Class")
    private String classNew;

    /**
     * orderNew
     */
    @Schema(description="orderNew")
	@ExcelProperty("Order")
    private String orderNew;

    /**
     * family
     */
    @Schema(description="family")
	@ExcelProperty("Family")
    private String family;

    /**
     * genus
     */
    @Schema(description="genus")
	@ExcelProperty("Genus")
    private String genus;

    /**
     * species1
     */
    @Schema(description="species1")
	@ExcelProperty("Species1")
    private String species1;

    /**
     * species2
     */
    @Schema(description="species2")
	@ExcelProperty("Species2")
    private String species2;

    /**
     * species3
     */
    @Schema(description="species3")
	@ExcelProperty("Species3")
    private String species3;

    /**
     * plantorgan1
     */
    @Schema(description="plantorgan1")
	@ExcelProperty("Plantorgan1")
    private String plantorgan1;

    /**
     * plantorgan2
     */
    @Schema(description="plantorgan2")
	@ExcelProperty("Plantorgan2")
    private String plantorgan2;

    /**
     * abundvalue
     */
    @Schema(description="abundvalue")
	@ExcelProperty("Abundvalue")
    private String abundvalue;

    /**
     * abundunit
     */
    @Schema(description="abundunit")
	@ExcelProperty("Abundunit")
    private String abundunit;

    /**
     * fossiltype
     */
    @Schema(description="fossiltype")
	@ExcelProperty("Fossiltype")
    private String fossiltype;

    /**
     * pollendiagram
     */
    @Schema(description="pollendiagram")
	@ExcelProperty("Pollendiagram")
    private String pollendiagram;

    /**
     * assemblage
     */
//    @Schema(description="assemblage")
//    private String assemblage;

    /**
     * siteno
     */
    @Schema(description="siteno")
    private String siteno;

    /**
     * sitename
     */
    @Schema(description="sitename")
    private String sitename;

    /**
     * extinct
     */
    @Schema(description="extinct")
    private String extinct;

    /**
     * timecontext
     */
    @Schema(description="timecontext")
    private String timecontext;

    /**
     * cid
     */
//    @TableId(type = IdType.ASSIGN_ID)


}
