import 'babel-polyfill'
import 'classlist-polyfill'
import Vue from 'vue'
import axios from './router/axios'
import VueAxios from 'vue-axios'
import App from './App'
import zhLocale from './lang/zh'
import './permission' // 权限
import './error' // 日志
import './cache'//页面缓冲
import './styles/primaryTheme/theme/index.css'
import router from './router/router'
import store from './store'
import {loadStyle, downBlobFile} from './util'
import {validatenull} from './util/validate'
import * as urls from '@/config/env'
import {iconfontUrl} from '@/config/env'
import * as filters from './filters' // 全局filter
import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'
import './styles/common.scss'
import AvueFormDesign from 'pig-avue-form-design'
import basicContainer from './components/basic-container/main'
// 字典数据组件
import DictResolver from '@/components/DictResolver'
// 字典标签组件
import DictTag from '@/components/DictTag'
//富文本编辑器
import VueQuillEditor from 'vue-quill-editor'
// 引入样式
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
Vue.use(VueQuillEditor, /* { 默认全局 } */)
// 挂载常用全局方法，import 引入
Vue.prototype.validatenull = validatenull
Vue.prototype.downBlobFile = downBlobFile

DictResolver.install()

// 插件 json 展示
Vue.use(router)
// 表单设计器
Vue.use(AvueFormDesign);

window.axios = axios
Vue.use(VueAxios, axios)

Vue.use(ElementUI, {
  size: 'small',
  menuType: 'text'
})

Vue.use(AVUE, {
  locale: zhLocale,
  size: 'small',
  menuType: 'text'
})
// 注册全局容器
Vue.component('basicContainer', basicContainer)
Vue.component('DictTag', DictTag)

// 加载相关url地址
Object.keys(urls).forEach(key => {
  Vue.prototype[key] = urls[key]
})

// 加载过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 动态加载阿里云字体库
iconfontUrl.forEach(ele => {
  loadStyle(ele)
})

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
