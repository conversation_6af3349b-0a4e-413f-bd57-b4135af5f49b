# 数据源
spring:
  redis:
    host: 127.0.0.1
    database: 9  #避免和微服务 PIGX 冲突
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      url: ********************************************************************************************************************************************************************************************************************************************************************
  web:
    resources:
      static-locations: classpath:/static/,classpath:/views/

# 租户表维护
tdcloud:
  tenant:
    column: tenant_id
    tables:
      - gen_datasource_conf
      - gen_form_conf
