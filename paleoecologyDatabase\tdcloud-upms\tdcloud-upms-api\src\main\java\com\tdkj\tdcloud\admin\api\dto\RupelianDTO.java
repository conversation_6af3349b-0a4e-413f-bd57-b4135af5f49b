/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:21:41
 */
@Data
@TableName("rupelian")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class RupelianDTO extends Model<RupelianDTO> {

    private static final long serialVersionUID = 1L;

    /**
     * pid
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="pid")
    private Integer pid;

    /**
     * id
     */
    @Schema(description="id")
    private String id;

    /**
     * siteno
     */
    @Schema(description="siteno")
    private String siteno;

    /**
     * sitename
     */
    @Schema(description="sitename")
    private String sitename;

    /**
     * source
     */
    @Schema(description="source")
    private String source;

    /**
     * sourceid
     */
    @Schema(description="sourceid")
    private String sourceid;

    /**
     * collectionme
     */
    @Schema(description="collectionme")
    private String collectionme;

    /**
     * country
     */
    @Schema(description="country")
    private String country;

    /**
     * datingmethod
     */
    @Schema(description="datingmethod")
    private String datingmethod;

    /**
     * datingquality
     */
    @Schema(description="datingquality")
    private String datingquality;

    /**
     * epoch
     */
    @Schema(description="epoch")
    private String epoch;

    /**
     * stage
     */
    @Schema(description="stage")
    private String stage;

    /**
     * earlyinterval
     */
    @Schema(description="earlyinterval")
    private String earlyInterval;

    /**
     * lateinterval
     */
    @Schema(description="lateinterval")
    private String lateInterval;

    /**
     * agemax
     */
    @Schema(description="agemax")
    private String agemax;

    /**
     * agemin
     */
    @Schema(description="agemin")
    private String agemin;

    /**
     * agemiddle
     */
    @Schema(description="agemiddle")
    private String agemiddle;

    /**
     * author
     */
    @Schema(description="author")
    private String author;

    /**
     * pubyr
     */
    @Schema(description="pubyr")
    private String pubyr;

    /**
     * longitude
     */
    @Schema(description="longitude")
    private String longitude;

    /**
     * latitude
     */
    @Schema(description="latitude")
    private String latitude;

    /**
     * timebin
     */
    @Schema(description="timebin")
    private String timebin;

    /**
     * fossiltype
     */
    @Schema(description="fossiltype")
    private String fossiltype;

    /**
     * reference1
     */
    @Schema(description="reference1")
    private String reference1;

    /**
     * reference2
     */
    @Schema(description="reference2")
    private String reference2;

    /**
     * reference3
     */
    @Schema(description="reference3")
    private String reference3;

    /**
     * otherreferences
     */
    @Schema(description="otherreferences")
    private String otherreferences;

}
