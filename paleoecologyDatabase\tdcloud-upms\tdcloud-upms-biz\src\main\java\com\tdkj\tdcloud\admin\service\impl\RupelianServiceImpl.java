/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.RupelianDTO;
import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.api.entity.Rupelian;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.vo.RupelianExcelVO;
import com.tdkj.tdcloud.admin.mapper.RupelianMapper;
import com.tdkj.tdcloud.admin.service.RupelianService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.vo.ErrorMessage;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 13:42:15
 */
@Service
public class RupelianServiceImpl extends ServiceImpl<RupelianMapper, Rupelian> implements RupelianService {

	@Resource
	private RupelianMapper rupelianMapper;

	@Override
	public R importRupelian(List<RupelianExcelVO> excelVOList, BindingResult bindingResult) throws Exception {
		// 通用校验获取失败的数据
		List<ErrorMessage> errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();

		try {
			// 执行数据插入操作 组装
			for (RupelianExcelVO excel : excelVOList) {

				Rupelian rupelian = rupelianMapper.selectRupelianById(Integer.valueOf(excel.getID()));

				if (rupelian==null){
					rupelianMapper.insertRupelianExcel(excel);
				}else {
					excel.setPid(Long.valueOf(rupelian.getPid()));
					rupelianMapper.updateRupelianExcel(excel);
				}


			}

		} catch (Exception e) {
			// 这里可以将具体的异常信息记录到日志文件中，以便后续排查问题
			log.error("导入数据时出错: ", e);
			throw new Exception("导入数据失败，请查看日志获取详细信息");
		}

		if (CollUtil.isNotEmpty(errorMessageList)) {
			return R.failed(errorMessageList);
		}
		return R.ok();

	}

	@Override
	public Page getRupelianPage(Page page, RupelianDTO rupelianDTO) {
		//查询条件
		QueryWrapper<Rupelian> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}

		wrapper.eq(StringUtils.isNotBlank(rupelianDTO.getId()), "ID", rupelianDTO.getId());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getSource()), "Source", rupelianDTO.getSource());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getSourceid()), "SourceID", rupelianDTO.getSourceid());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getCollectionme()), "Collectionme", rupelianDTO.getCollectionme());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getCountry()), "Country", rupelianDTO.getCountry());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getDatingmethod()), "DatingMethod", rupelianDTO.getDatingmethod());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getDatingquality()), "DatingQuality", rupelianDTO.getDatingquality());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getEpoch()), "Epoch", rupelianDTO.getEpoch());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getStage()), "Stage", rupelianDTO.getStage());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getEarlyInterval()), "EarlyInterval", rupelianDTO.getEarlyInterval());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getLateInterval()), "LateInterval", rupelianDTO.getLateInterval());

		// 处理 AgeMin
		if (rupelianDTO.getAgemin() != null) {
			wrapper.ge("AgeMin", rupelianDTO.getAgemin());
		}
		// 处理 AgeMax
		if (rupelianDTO.getAgemax() != null) {
			wrapper.le("AgeMax", rupelianDTO.getAgemax());
		}
		if (rupelianDTO.getAgemiddle() != null) {
			wrapper.like("AgeMiddle", String.valueOf(rupelianDTO.getAgemiddle()));
		}
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getAuthor()), "Author", rupelianDTO.getAuthor());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getPubyr()), "Pubyr", rupelianDTO.getPubyr());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getLongitude()), "Longitude", rupelianDTO.getLongitude());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getLatitude()), "Latitude", rupelianDTO.getLatitude());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getTimebin()), "TimeBin", rupelianDTO.getTimebin());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getFossiltype()), "FossilType", rupelianDTO.getFossiltype());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getReference1()), "Reference1", rupelianDTO.getReference1());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getReference2()), "Reference2", rupelianDTO.getReference2());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getReference3()), "Reference3", rupelianDTO.getReference3());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getOtherreferences()), "OtherReferences", rupelianDTO.getOtherreferences());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getSiteno()), "SiteNo", rupelianDTO.getSiteno());
		wrapper.like(StringUtils.isNotBlank(rupelianDTO.getSitename()), "SiteName", rupelianDTO.getSitename());

//		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}

	@Override
	public R getRupelianById(Integer id) {
		Rupelian rupelian = rupelianMapper.selectRupelianById(id);
		List<SysFile> sysFileList = new ArrayList<>();

		List<SysFile> percentage = rupelianMapper.getPercentageList("percentage", id + ".xlsx");
		sysFileList.addAll(percentage);
		List<SysFile> percentageXls = rupelianMapper.getPercentageList("percentage",id +".xls");
		sysFileList.addAll(percentageXls);
		List<SysFile> percentageCsv = rupelianMapper.getPercentageList("percentage",id +".csv");
		sysFileList.addAll(percentageCsv);
		rupelian.setSysFileList(sysFileList);
		return R.ok(rupelian,"父表id");
	}

	@Override
	public R getByPId(Long pId) {
		Rupelian rupelian = rupelianMapper.selectRupelianByPid(pId);
		return R.ok(rupelian,"父表pId");
	}
}
