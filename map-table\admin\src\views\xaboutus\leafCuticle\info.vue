<template>
  <el-dialog title="详情" :close-on-click-modal="false" append-to-body :visible.sync="visible" width="60%">
    <el-descriptions class="margin-top"  :column="1" border>
      <el-descriptions-item label="语言类型">
        <template>
          <div v-if="infoContent.languageType === 'zh'">中文</div>
          <div v-else>英文</div>
        </template>

      </el-descriptions-item>
      <el-descriptions-item label="内容">
       <div v-html="infoContent.content" class="content"></div>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
       {{infoContent.createTime}}
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
import { getObj } from '@/api/xaboutus/xaboutus'

export default {
  name: "info",
  data() {
    return {
      visible: false,
      infoContent: {}
    }
  },
  methods: {
    getInfo(id) {
      getObj(id).then(res => {
        this.visible = true
        this.infoContent = res.data.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .content {
  img {
    max-width: 100%;
  }
}
</style>
