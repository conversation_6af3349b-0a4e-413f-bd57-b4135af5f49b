/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.XAboutUs;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 关于我们
 *
 * <AUTHOR> code generator
 * @date 2025-01-03 14:40:32
 */
@Mapper
public interface XAboutUsMapper extends TdcloudBaseMapper<XAboutUs> {

	/**
	 * 查询关于我们
	 *
	 * @param id 关于我们主键
	 * @return 关于我们
	 */
	public XAboutUs selectXAboutUsById(Long id);

	/**
	 * 查询关于我们列表
	 *
	 * @param xAboutUs 关于我们
	 * @return 关于我们集合
	 */
	public List<XAboutUs> selectXAboutUsList(XAboutUs xAboutUs);

	/**
	 * 新增关于我们
	 *
	 * @param xAboutUs 关于我们
	 * @return 结果
	 */
	public int insertXAboutUs(XAboutUs xAboutUs);

	/**
	 * 修改关于我们
	 *
	 * @param xAboutUs 关于我们
	 * @return 结果
	 */
	public int updateXAboutUs(XAboutUs xAboutUs);

	/**
	 * 删除关于我们
	 *
	 * @param id 关于我们主键
	 * @return 结果
	 */
	public int deleteXAboutUsById(Long id);

	/**
	 * 批量删除关于我们
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteXAboutUsByIds(Long[] ids);
}
