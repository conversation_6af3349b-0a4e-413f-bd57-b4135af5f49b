<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module" module-name="tdcloud-auth" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-security:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-oauth2-jose:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-oauth2-core:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: com.nimbusds:nimbus-jose-jwt:9.22" level="project" />
    <orderEntry type="library" name="Maven: com.github.stephenc.jcip:jcip-annotations:1.0-1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-oauth2-authorization-server:0.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-oauth2-resource-server:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-core:11.10" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjrt:1.9.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-app-server-api:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.anji-plus:captcha-spring-boot-starter:1.2.7" level="project" />
    <orderEntry type="library" name="Maven: com.github.anji-plus:captcha:1.2.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-crypto:5.8.10" level="project" />
    <orderEntry type="module" module-name="tdcloud-upms-api" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-core:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-json:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:4.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.70" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.2.5.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.3.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:transmittable-thread-local:2.12.6" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-extension:3.5.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-core:3.5.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-annotation:3.5.3.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:4.4" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.10" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.7" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-feign:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-openfeign:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcutil-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-openfeign-core:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form-spring:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-slf4j:11.10" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-okhttp:11.10" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okhttp3:okhttp:4.9.3" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okio:okio:2.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains.kotlin:kotlin-stdlib-common:1.6.21" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains.kotlin:kotlin-stdlib:1.6.21" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains:annotations:13.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-loadbalancer:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-loadbalancer:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor.addons:reactor-extra:3.4.9" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-cache:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: com.stoyanr:evictor:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-excel:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.7" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel:3.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel-core:3.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:easyexcel-support:3.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-math3:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:SparseBitSet:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.06" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:4.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-csv:1.8" level="project" />
    <orderEntry type="library" name="Maven: org.ehcache:ehcache:3.10.8" level="project" />
    <orderEntry type="library" name="Maven: javax.cache:cache-api:1.1.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:jaxb-runtime:2.3.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.glassfish.jaxb:txw2:2.3.7" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.istack:istack-commons-runtime:3.0.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.21" level="project" />
    <orderEntry type="library" name="Maven: jakarta.servlet:jakarta.servlet-api:4.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-log:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-http:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-extra:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-setting:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-log:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-core:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security.oauth:spring-security-oauth2:2.3.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-config:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-web:5.7.6" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.15" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-mapper-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-core-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-data:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: io.lettuce:lettuce-core:6.1.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport-native-unix-common:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.86.Final" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.4.26" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-xss:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-core:5.8.10" level="project" />
    <orderEntry type="library" name="Maven: org.jsoup:jsoup:1.13.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.4.2" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: com.tdkj:tdcloud-common-swagger:4.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-webmvc-core:1.6.9" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-common:1.6.9" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-core:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.12.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-models:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springdoc:springdoc-openapi-security:1.6.9" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-annotations:2.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-quartz:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.11" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.11" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.1" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.30" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.3.24" level="project" />
    <orderEntry type="library" name="Maven: org.quartz-scheduler:quartz:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.mchange:mchange-commons-java:0.2.15" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-configuration-processor:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.github.ulisesbocchio:jasypt-spring-boot:3.0.4" level="project" />
    <orderEntry type="library" name="Maven: org.jasypt:jasypt:1.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.4" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-core:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-starter-client:2.7.9" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-client:2.7.9" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.projectlombok:lombok:1.18.24" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-impl:2.3.5" level="project" />
    <orderEntry type="library" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.sun.activation:jakarta.activation:1.2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.7.7" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.7.7" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.7.7" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.7.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.4.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:2.4.8" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:7.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.12.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest:2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-api:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.opentest4j:opentest4j:1.2.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-commons:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.apiguardian:apiguardian-api:1.1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-params:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-engine:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.28.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy:1.12.20" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.12.20" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-junit-jupiter:4.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.3.24" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.9.0" level="project" />
  </component>
</module>