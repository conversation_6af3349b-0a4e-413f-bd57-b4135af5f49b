<template>
  <div class="about">
    <div class="about-content">
      <div class="about-content-left" v-if="currentIndex === 0">
        <div v-html="infoContent.content" class="content ql-container ql-editor"></div>
      </div>
      <div class="about-content-left" v-if="currentIndex === 1">
        <div v-html="infoContent.content" class="content ql-container ql-editor"></div>
      </div>
      <div class="about-content-left contact-main" v-if="currentIndex === 2">
        <div class="contact-left">
          <div class="contact-title">{{$t('about.contactInformation')}}</div>
          <div class="contact-info">
            <div>{{ $t('footer.telFooter') }}:{{ infoContent.phone }}</div>
            <div>{{ $t('footer.addressFooter') }}:{{ infoContent.address }}</div>
            <div>{{ $t('footer.postalCode') }}:{{ infoContent.postalCode }}</div>
          </div>
        </div>
        <div class="contact-pic"><img class="pic" :src="infoContent.url" alt=""></div>
      </div>
      <ul class="about-content-right">
        <li class="right-item title">{{ $t('about.aboutText') }}</li>

        <li :class="['right-item child',currentIndex === 0 ? 'active' : '']" @click="toggle(0, 'overview')">{{ $t('nav.paleoecologyNav') }}</li>
        <li :class="['right-item child',currentIndex === 1 ? 'active' : '']" @click="toggle(1, 'leafCuticle')">{{ $t('nav.leafCuticlNav') }}</li>
        <li :class="['right-item child',currentIndex === 2 ?'active' : '']" @click="toggle(2, 'contactUs')">{{ $t('about.contactText') }}</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getAboutList } from "@/api/about";

export default {
  name: 'AboutIndex',
  data() {
    return {
      currentIndex: 0,
      pageIndex: 1,
      pageSize: 10,
      type: 'overview',
      infoContent: {}
    }
  },
  computed: {
    ...mapState({
      language: state => state.language // 将language从store映射到计算属性
    })
  },
  watch: {
    language() {
      this.getAboutList(); // 语言变化时重新获取内容
    }
  },
  mounted() {
    this.getAboutList(); // 初次加载
  },
  methods: {
    toggle(index, type) {
      this.currentIndex = index;
      this.type = type;
      this.getAboutList(); // 切换内容时获取不同内容
    },
    getAboutList() {
      getAboutList({
        current: this.pageIndex,
        size: this.pageSize,
        type: this.type,
        languageType: this.language // 使用 Vuex 中的 language
      }).then(res => {
        this.infoContent = res.data.data.records.length > 0 ? res.data.data.records[0] : []
        // if (this.type === 'overview') {
        //   this.infoContent = res.data.data.records.length > 0 ? res.data.data.records[0] : []
        // }else {
        //   this.infoContent = res.data.data.records.length > 0 ? res.data.data.records[0] : []
        // }
      });
    },
  }
}
</script>

<style scoped>
.about {
  width: 100%;
  min-height: calc(100vh - 372px);
  position: relative;
  background-color: var(--about-background-color);
}
.cover {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 90;
  width: 100%;
  height: 380px;
  line-height: 380px;
  font-size: 36px;
  color: var(--light-text-color);
  background: url("../../assets/top-banner.png") no-repeat ;
  background-size: cover;
}
.about-content {

  padding: 50px 80px 50px;
  text-align: left;
  display: flex;
  position: relative;
}
.about-content-left {
  width: calc(100% - 320px - 43px);

}
.about-content-left ::v-deep .content img{
  max-width: 75%;
}
.about-content-right {
  width: 320px;
  margin-left: 43px;
  padding: 0;
  position: fixed;
  bottom: 40%;
  right: 80px;
}
.contact-main {
  display: flex;
  
}
.contact-left {
  width: 555px;
  height: 300px;
  background-color: #fff;
  padding: 30px 0;
}
.contact-title {
  font-size: 36px;
  color: var(--primary-text-color);
  margin: 0 30px;
  margin-bottom: 30px;

}
.contact-info {
  margin: 0 30px;

}
.contact-info  div{
  margin: 30px 0;
  font-size: 18px;
}
.contact-pic {
  width: calc(100% - 555px);
  height: 360px;
}
.contact-pic .pic {
  width: 100%;
  height: 100%;
  object-position: center;
  object-fit: cover;
}
.right-item {
  height: 60px;
  line-height: 60px;
  border-bottom:  1px solid #DBDBDB;
  padding-left: 10px;
  font-size: 18px;
}
.title {
  font-size: 24px;
  color: var(--primary-text-color);
}
.child {
  cursor: pointer;
}
.active {
  background: var(--light-background-color);
  color: #2C4A52;
}
</style>