import { request } from '@/api/request'

/**
 * <PERSON>tch unique Original Name values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getOriginalNames() {
    return request({
        url: '/admin/paleoecology/taxa/original-names',
        method: 'get'
    })
}

/**
 * Fetch unique Fossil Type values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getFossilTypes() {
    return request({
        url: '/admin/paleoecology/fossil-types',
        method: 'get'
    })
}

/**
 * Fetch unique Plant Organ values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getPlantOrgans() {
    return request({
        url: '/admin/paleoecology/plant-organs',
        method: 'get'
    })
}
