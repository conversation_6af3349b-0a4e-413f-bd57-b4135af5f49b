/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.service.impl;

import com.tdkj.tdcloud.admin.api.dto.PaleoecologyDataDTO;
import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.mapper.PaleoecologyDataMapper;
import com.tdkj.tdcloud.admin.service.PaleoecologyDataService;
import com.tdkj.tdcloud.admin.util.GeographicUtils;
import com.tdkj.tdcloud.common.core.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Paleoecology Data Service Implementation
 * Implements complex data filtering and geographic spatial queries
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
@Service
public class PaleoecologyDataServiceImpl implements PaleoecologyDataService {

    @Resource
    private PaleoecologyDataMapper paleoecologyDataMapper;

    @Override
    public R getData(PaleoecologyDataDTO queryParams) {
        try {
            log.debug("Processing paleoecology data query with parameters: {}", queryParams);

            // Execute database query with basic filtering
            List<Map<String, Object>> results = paleoecologyDataMapper.selectPaleoecologyData(queryParams);
            log.debug("Database query returned {} results", results.size());

            // Process geographic spatial filtering
            List<Map<String, Object>> filteredResults = processGeographicFiltering(results, queryParams);

            // Process multi-level fields (Species, ScientificName, PlantOrgan)
            List<Map<String, Object>> processedResults = processMultiLevelFields(filteredResults);

            log.debug("Final processed results: {}", processedResults.size());
            
            Map<String, Object> response = new HashMap<>();
            response.put("table1", processedResults);
            
            return R.ok(response);
        } catch (Exception e) {
            log.error("Error processing paleoecology data query", e);
            return R.failed("Database query failed: " + e.getMessage());
        }
    }

    @Override
    public R getDetailData(String id) {
        try {
            if (id == null || id.trim().isEmpty()) {
                return R.failed("ID parameter is required");
            }

            List<Chattian> detailData = paleoecologyDataMapper.selectChattianDetailById(id.trim());
            
            Map<String, Object> response = new HashMap<>();
            response.put("detail", detailData);
            
            return R.ok(response);
        } catch (Exception e) {
            log.error("Error fetching detail data for ID: {}", id, e);
            return R.failed("Error fetching detail data: " + e.getMessage());
        }
    }

    @Override
    public R getDatingMethods() {
        try {
            List<String> datingMethods = paleoecologyDataMapper.selectUniqueDatingMethods();
            return R.ok(datingMethods);
        } catch (Exception e) {
            log.error("Error fetching dating methods", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getDatingQualities() {
        try {
            List<String> datingQualities = paleoecologyDataMapper.selectUniqueDatingQualities();
            return R.ok(datingQualities);
        } catch (Exception e) {
            log.error("Error fetching dating qualities", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getFamilies() {
        try {
            List<String> families = paleoecologyDataMapper.selectUniqueFamilies();
            return R.ok(families);
        } catch (Exception e) {
            log.error("Error fetching families", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getGenera() {
        try {
            List<String> genera = paleoecologyDataMapper.selectUniqueGenera();
            return R.ok(genera);
        } catch (Exception e) {
            log.error("Error fetching genera", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getSpecies() {
        try {
            List<String> species = paleoecologyDataMapper.selectUniqueSpecies();
            return R.ok(species);
        } catch (Exception e) {
            log.error("Error fetching species", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getScientificNames() {
        try {
            List<String> scientificNames = paleoecologyDataMapper.selectUniqueScientificNames();
            return R.ok(scientificNames);
        } catch (Exception e) {
            log.error("Error fetching scientific names", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getOriginalNames() {
        try {
            List<String> originalNames = paleoecologyDataMapper.selectUniqueOriginalNames();
            return R.ok(originalNames);
        } catch (Exception e) {
            log.error("Error fetching original names", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getFossilTypes() {
        try {
            List<String> fossilTypes = paleoecologyDataMapper.selectUniqueFossilTypes();
            return R.ok(fossilTypes);
        } catch (Exception e) {
            log.error("Error fetching fossil types", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    @Override
    public R getPlantOrgans() {
        try {
            List<String> plantOrgans = paleoecologyDataMapper.selectUniquePlantOrgans();
            return R.ok(plantOrgans);
        } catch (Exception e) {
            log.error("Error fetching plant organs", e);
            return R.failed("Database operation failed: " + e.getMessage());
        }
    }

    /**
     * Process geographic spatial filtering (polygons, circles, bounding boxes)
     * Replicates Python Flask server geographic filtering logic
     */
    private List<Map<String, Object>> processGeographicFiltering(List<Map<String, Object>> results, 
                                                                 PaleoecologyDataDTO queryParams) {
        // Parse geographic parameters
        List<List<GeographicUtils.Point>> polygonData = parsePolygons(queryParams.getPolygons());
        List<BoundingBox> bboxData = parseBoundingBoxes(queryParams.getBboxes());
        List<Circle> circleData = parseCircles(queryParams.getCircleCenters(), queryParams.getCircleRadii());

        // If no geographic filtering, return all results
        if (polygonData.isEmpty() && bboxData.isEmpty() && circleData.isEmpty()) {
            return results;
        }

        int originalCount = results.size();
        int filteredCount = 0;

        List<Map<String, Object>> filteredResults = new ArrayList<>();

        for (Map<String, Object> row : results) {
            try {
                Object lngObj = row.get("Longitude");
                Object latObj = row.get("Latitude");
                
                if (lngObj == null || latObj == null) {
                    continue;
                }

                double lng = Double.parseDouble(lngObj.toString());
                double lat = Double.parseDouble(latObj.toString());
                GeographicUtils.Point point = new GeographicUtils.Point(lng, lat);

                boolean inAnyShape = false;

                // Check polygons
                for (List<GeographicUtils.Point> polygon : polygonData) {
                    if (GeographicUtils.pointInPolygon(point, polygon)) {
                        inAnyShape = true;
                        log.debug("Point at ({}, {}) is inside a polygon", lng, lat);
                        break;
                    }
                }

                // Check circles if not in polygon
                if (!inAnyShape) {
                    for (Circle circle : circleData) {
                        double distance = GeographicUtils.haversineDistance(lat, lng, circle.lat, circle.lng);
                        if (distance <= circle.radius) {
                            inAnyShape = true;
                            log.debug("Point at ({}, {}) is inside a circle", lng, lat);
                            break;
                        }
                    }
                }

                // Check bounding boxes if not in polygon or circle
                if (!inAnyShape) {
                    for (BoundingBox bbox : bboxData) {
                        if (lng >= bbox.minLng && lng <= bbox.maxLng && 
                            lat >= bbox.minLat && lat <= bbox.maxLat) {
                            inAnyShape = true;
                            log.debug("Point at ({}, {}) is inside a rectangle", lng, lat);
                            break;
                        }
                    }
                }

                if (inAnyShape) {
                    filteredResults.add(row);
                } else {
                    filteredCount++;
                    log.debug("Filtered out point at ({}, {}) - outside all shapes", lng, lat);
                }
            } catch (Exception e) {
                log.error("Error checking point in shapes: {}", e.getMessage());
            }
        }

        log.debug("Original results: {}, Filtered out: {}, Final results: {}", 
                 originalCount, filteredCount, filteredResults.size());
        
        return filteredResults;
    }

    /**
     * Process multi-level fields (Species, ScientificName, PlantOrgan)
     * Combines multiple numbered fields into single comma-separated values
     */
    private List<Map<String, Object>> processMultiLevelFields(List<Map<String, Object>> results) {
        return results.stream().map(row -> {
            Map<String, Object> processedRow = new HashMap<>(row);

            // Process Species fields (Species1, Species2, Species3)
            List<String> speciesValues = Arrays.asList("Species1", "Species2", "Species3")
                .stream()
                .map(field -> row.get(field))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.toList());
            
            if (!speciesValues.isEmpty()) {
                processedRow.put("Species", String.join(", ", speciesValues));
            }

            // Process ScientificName fields
            List<String> scientificNameValues = Arrays.asList("ScientificName1", "ScientificName2", "ScientificName3")
                .stream()
                .map(field -> row.get(field))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.toList());
            
            if (!scientificNameValues.isEmpty()) {
                processedRow.put("ScientificName", String.join(", ", scientificNameValues));
            }

            // Process PlantOrgan fields
            List<String> plantOrganValues = Arrays.asList("PlantOrgan1", "PlantOrgan2")
                .stream()
                .map(field -> row.get(field))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.toList());
            
            if (!plantOrganValues.isEmpty()) {
                processedRow.put("PlantOrgan", String.join(", ", plantOrganValues));
            }

            return processedRow;
        }).collect(Collectors.toList());
    }

    /**
     * Parse polygon coordinates from string parameter
     */
    private List<List<GeographicUtils.Point>> parsePolygons(String polygons) {
        List<List<GeographicUtils.Point>> polygonData = new ArrayList<>();

        if (polygons == null || polygons.trim().isEmpty()) {
            return polygonData;
        }

        String[] polygonList = polygons.split("\\|");
        for (String polygonCoords : polygonList) {
            if (polygonCoords.trim().isEmpty()) {
                continue;
            }

            try {
                String[] coords = polygonCoords.split(",");
                if (coords.length >= 6 && coords.length % 2 == 0) {
                    List<GeographicUtils.Point> points = new ArrayList<>();

                    for (int i = 0; i < coords.length; i += 2) {
                        double lng = Double.parseDouble(coords[i].trim());
                        double lat = Double.parseDouble(coords[i + 1].trim());
                        points.add(new GeographicUtils.Point(lng, lat));
                    }

                    // Normalize polygon
                    points = GeographicUtils.normalizePolygon(points);
                    polygonData.add(points);

                    log.debug("Parsed polygon with {} vertices", points.size());
                }
            } catch (NumberFormatException e) {
                log.error("Error parsing polygon coordinates: {}", e.getMessage());
            }
        }

        return polygonData;
    }

    /**
     * Parse bounding box coordinates from string parameter
     */
    private List<BoundingBox> parseBoundingBoxes(String bboxes) {
        List<BoundingBox> bboxData = new ArrayList<>();

        if (bboxes == null || bboxes.trim().isEmpty()) {
            return bboxData;
        }

        String[] bboxList = bboxes.split("\\|");
        for (String bbox : bboxList) {
            if (bbox.trim().isEmpty()) {
                continue;
            }

            try {
                String[] coords = bbox.split(",");
                if (coords.length == 4) {
                    double minLng = Double.parseDouble(coords[0].trim());
                    double minLat = Double.parseDouble(coords[1].trim());
                    double maxLng = Double.parseDouble(coords[2].trim());
                    double maxLat = Double.parseDouble(coords[3].trim());

                    bboxData.add(new BoundingBox(minLng, minLat, maxLng, maxLat));
                    log.debug("Parsed bounding box: [{}, {}, {}, {}]", minLng, minLat, maxLng, maxLat);
                }
            } catch (NumberFormatException e) {
                log.error("Error parsing bounding box coordinates: {}", e.getMessage());
            }
        }

        return bboxData;
    }

    /**
     * Parse circle coordinates and radii from parameters
     */
    private List<Circle> parseCircles(List<String> circleCenters, List<String> circleRadii) {
        List<Circle> circleData = new ArrayList<>();

        if (circleCenters == null || circleRadii == null) {
            return circleData;
        }

        // Process pipe-separated centers if needed
        List<String> centers = new ArrayList<>();
        for (String center : circleCenters) {
            if (center.contains("|")) {
                centers.addAll(Arrays.asList(center.split("\\|")));
            } else {
                centers.add(center);
            }
        }

        // Process pipe-separated radii if needed
        List<Double> radii = new ArrayList<>();
        for (String radiusStr : circleRadii) {
            if (radiusStr.contains("|")) {
                for (String r : radiusStr.split("\\|")) {
                    try {
                        double radius = Double.parseDouble(r.trim());
                        if (radius > 0) {
                            radii.add(radius);
                        }
                    } catch (NumberFormatException e) {
                        log.error("Error parsing radius: {}", e.getMessage());
                    }
                }
            } else {
                try {
                    double radius = Double.parseDouble(radiusStr.trim());
                    if (radius > 0) {
                        radii.add(radius);
                    }
                } catch (NumberFormatException e) {
                    log.error("Error parsing radius: {}", e.getMessage());
                }
            }
        }

        // Combine centers and radii
        for (int i = 0; i < centers.size() && i < radii.size(); i++) {
            try {
                String[] coords = centers.get(i).split(",");
                if (coords.length == 2) {
                    double lng = Double.parseDouble(coords[0].trim());
                    double lat = Double.parseDouble(coords[1].trim());
                    double radius = radii.get(i);

                    circleData.add(new Circle(lng, lat, radius));
                    log.debug("Parsed circle: center=({}, {}), radius={}m", lng, lat, radius);
                }
            } catch (NumberFormatException e) {
                log.error("Error parsing circle coordinates: {}", e.getMessage());
            }
        }

        return circleData;
    }

    /**
     * Helper class for bounding box
     */
    private static class BoundingBox {
        double minLng, minLat, maxLng, maxLat;

        BoundingBox(double minLng, double minLat, double maxLng, double maxLat) {
            this.minLng = minLng;
            this.minLat = minLat;
            this.maxLng = maxLng;
            this.maxLat = maxLat;
        }
    }

    /**
     * Helper class for circle
     */
    private static class Circle {
        double lng, lat, radius;

        Circle(double lng, double lat, double radius) {
            this.lng = lng;
            this.lat = lat;
            this.radius = radius;
        }
    }
}
