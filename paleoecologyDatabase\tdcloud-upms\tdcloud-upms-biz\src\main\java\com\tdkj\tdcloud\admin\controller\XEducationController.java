/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.XEducationDto;
import com.tdkj.tdcloud.admin.api.entity.XEducation;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import com.tdkj.tdcloud.admin.service.XEducationService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教育
 *
 * <AUTHOR> code generator
 * @date 2025-01-07 13:57:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/xeducation" )
@Tag(description = "xeducation" , name = "教育管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XEducationController {

    private final  XEducationService xEducationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param xEducation 教育
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_xeducation_view')" )
    public R getXEducationPage(Page page, XEducationDto xEducation) {
        return R.ok(xEducationService.getXEducationPage(page, xEducation));
    }


    /**
     * 通过id查询教育
     * @param id id
     * @return R
     */
	@Inner(value = false)
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_xeducation_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return xEducationService.getXEducationById(id);
    }

    /**
     * 新增教育
     * @param xEducation 教育
     * @return R
     */
    @Operation(summary = "新增教育" , description = "新增教育" )
    @SysLog("新增教育" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_xeducation_add')" )
    public R save(@RequestBody XEducation xEducation) {
        return xEducationService.saveXEducation(xEducation);
    }

    /**
     * 修改教育
     * @param xEducation 教育
     * @return R
     */
    @Operation(summary = "修改教育" , description = "修改教育" )
    @SysLog("修改教育" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_xeducation_edit')" )
    public R updateById(@RequestBody XEducation xEducation) {
        return xEducationService.updateXEducationById(xEducation);
    }

    /**
     * 通过id删除教育
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id删除教育" , description = "通过id删除教育" )
    @SysLog("通过id删除教育" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xeducation_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(xEducationService.removeById(id));
    }


    /**
     * 导出excel 表格
     * @param xEducation 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_xeducation_export')" )
    public List<XEducation> export(XEducation xEducation) {
        return xEducationService.list(Wrappers.query(xEducation));
    }
}
