<template>
  <div class="team-detail">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/team' }">{{ $t('nav.teamNav') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $t('nav.teamDetailNav') }}</el-breadcrumb-item>
    </el-breadcrumb>
    <section class="detail-main">
      <section class="detail-main-intro">
        <div class="intro-left">
          <div class="intro-left-name">{{ infoContent.personName }}</div>
          <div class="intro-left-item">
            <span class="intro-left-item-text">{{ $t('team.academicTitle') }}: {{ infoContent.academicTitle }}</span>
            <span>{{ $t('team.titleText') }}: {{ infoContent.title }}</span>
          </div>
          <div class="intro-left-item">
            <span class="intro-left-item-text">{{ $t('team.email') }}: {{ infoContent.email }}</span>
<!--            <span>{{ $t('footer.postalCode') }}: {{ infoContent.postalCode }}</span>-->
          </div>
          <div class="intro-left-item">
            <span class="intro-left-item-text">
              {{$t('team.introduction') }}: {{ infoContent.mailingAddress }}
            </span>
          </div>
        </div>
        <div class="intro-pic"><img class="pic-img" :src="infoContent.url" alt=""></div>
      </section>
      <section class="detail-main-resume">
        <div class="resume-title">{{ $t('team.resumeText') }}</div>
        <div v-html="infoContent.resume"></div>
       </section>
      <section class="detail-main-publications">
        <div class="publications-title">{{ $t('team.publicationsText') }}</div>
        <div v-html="infoContent.publications"></div>

      </section>
    </section>
  </div>
</template>

<script>
import {getTeamObj} from "@/api/team";

export default {
  name: "TeamDetailIndex",
  data() {
    return {
      infoContent: {},
      teamId : null
    }
  },
  mounted() {
    this.getTeamDetail()
  },
  methods: {
    getTeamDetail() {
      getTeamObj(this.$route.params.id).then(res => {
        if(res.data.code === 0) {
          this.infoContent = res.data.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-breadcrumb__inner {
  font-size: 16px !important;
}
.team-detail {
  padding: 30px 80px;
  text-align: left;
  background-color: var(--about-background-color);
}
.detail-main {
  margin-top: 30px;
  .detail-main-intro {
    height: 252px;
    background-color: #fff;
    border-top: 3px solid #304F56;
    margin-bottom: 30px;
    padding: 30px;
    display: flex;
    //align-items: center;
    justify-content: space-between;
    .intro-left{
      .intro-left-name {
        font-size: 24px;
        color: var(--primary-text-color);
        margin-bottom: 40px;
      }
      .intro-left-item {
        font-size: 16px;
        color: #666666;
        margin-bottom: 30px;
        .intro-left-item-text {
          display: inline-block;
          width: 484px;
          margin-right: 60px;
          text-align: left;
        }
      }
      .intro-left-item-text {
        width: 484px;
        margin-right: 60px;
        text-align: left;
        display: inline-block;

      }
    }
    .intro-pic{
      width: 184px;
      height: 100%;
      .pic-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .detail-main-resume {
    background-color: #fff;
    margin-bottom: 30px;
    padding: 30px;
    .resume-title {
      font-weight: 600;
      font-size: 20px;
      color: var(--primary-text-color);
      margin-bottom: 20px;
    }
  }
  .detail-main-publications {
    background-color: #fff;
    padding: 30px;
    .publications-title {
      font-weight: 600;
      font-size: 20px;
      color: var(--primary-text-color);
      margin-bottom: 20px;
    }
  }
}
</style>