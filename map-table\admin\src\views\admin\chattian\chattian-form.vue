<template>
  <el-dialog
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="80px">
    <el-form-item label="id" prop="id">
      <el-input v-model="dataForm.id" placeholder="id"></el-input>
    </el-form-item>
    <el-form-item label="originalname" prop="originalname">
      <el-input v-model="dataForm.originalname" placeholder="originalname"></el-input>
    </el-form-item>
    <el-form-item label="scientificname1" prop="scientificname1">
      <el-input v-model="dataForm.scientificname1" placeholder="scientificname1"></el-input>
    </el-form-item>
    <el-form-item label="scientificname2" prop="scientificname2">
      <el-input v-model="dataForm.scientificname2" placeholder="scientificname2"></el-input>
    </el-form-item>
    <el-form-item label="scientificname3" prop="scientificname3">
      <el-input v-model="dataForm.scientificname3" placeholder="scientificname3"></el-input>
    </el-form-item>
    <el-form-item label="acceptedrank" prop="acceptedrank">
      <el-input v-model="dataForm.acceptedrank" placeholder="acceptedrank"></el-input>
    </el-form-item>
    <el-form-item label="phylum" prop="phylum">
      <el-input v-model="dataForm.phylum" placeholder="phylum"></el-input>
    </el-form-item>
    <el-form-item label="class" prop="class">
      <el-input v-model="dataForm.class" placeholder="class"></el-input>
    </el-form-item>
    <el-form-item label="order" prop="order">
      <el-input v-model="dataForm.order" placeholder="order"></el-input>
    </el-form-item>
    <el-form-item label="family" prop="family">
      <el-input v-model="dataForm.family" placeholder="family"></el-input>
    </el-form-item>
    <el-form-item label="genus" prop="genus">
      <el-input v-model="dataForm.genus" placeholder="genus"></el-input>
    </el-form-item>
    <el-form-item label="species1" prop="species1">
      <el-input v-model="dataForm.species1" placeholder="species1"></el-input>
    </el-form-item>
    <el-form-item label="species2" prop="species2">
      <el-input v-model="dataForm.species2" placeholder="species2"></el-input>
    </el-form-item>
    <el-form-item label="species3" prop="species3">
      <el-input v-model="dataForm.species3" placeholder="species3"></el-input>
    </el-form-item>
    <el-form-item label="plantorgan1" prop="plantorgan1">
      <el-input v-model="dataForm.plantorgan1" placeholder="plantorgan1"></el-input>
    </el-form-item>
    <el-form-item label="plantorgan2" prop="plantorgan2">
      <el-input v-model="dataForm.plantorgan2" placeholder="plantorgan2"></el-input>
    </el-form-item>
    <el-form-item label="abundvalue" prop="abundvalue">
      <el-input v-model="dataForm.abundvalue" placeholder="abundvalue"></el-input>
    </el-form-item>
    <el-form-item label="abundunit" prop="abundunit">
      <el-input v-model="dataForm.abundunit" placeholder="abundunit"></el-input>
    </el-form-item>
    <el-form-item label="fossiltype" prop="fossiltype">
      <el-input v-model="dataForm.fossiltype" placeholder="fossiltype"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/chattian'

  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
                id: '',
                originalname: '',
                scientificname1: '',
                scientificname2: '',
                scientificname3: '',
                acceptedrank: '',
                phylum: '',
                classNew: '',
                order: '',
                family: '',
                genus: '',
                species1: '',
                species2: '',
                species3: '',
                plantorgan1: '',
                plantorgan2: '',
                abundvalue: '',
                abundunit: '',
                fossiltype: '',
        },
        dataRule: {
                id: [
                {required: true, message: 'id不能为空', trigger: 'blur'}
              ],
                originalname: [
                {required: true, message: 'originalname不能为空', trigger: 'blur'}
              ],
                scientificname1: [
                {required: true, message: 'scientificname1不能为空', trigger: 'blur'}
              ],
                scientificname2: [
                {required: true, message: 'scientificname2不能为空', trigger: 'blur'}
              ],
                scientificname3: [
                {required: true, message: 'scientificname3不能为空', trigger: 'blur'}
              ],
                acceptedrank: [
                {required: true, message: 'acceptedrank不能为空', trigger: 'blur'}
              ],
                phylum: [
                {required: true, message: 'phylum不能为空', trigger: 'blur'}
              ],
          classNew: [
                {required: true, message: 'class不能为空', trigger: 'blur'}
              ],
                order: [
                {required: true, message: 'order不能为空', trigger: 'blur'}
              ],
                family: [
                {required: true, message: 'family不能为空', trigger: 'blur'}
              ],
                genus: [
                {required: true, message: 'genus不能为空', trigger: 'blur'}
              ],
                species1: [
                {required: true, message: 'species1不能为空', trigger: 'blur'}
              ],
                species2: [
                {required: true, message: 'species2不能为空', trigger: 'blur'}
              ],
                species3: [
                {required: true, message: 'species3不能为空', trigger: 'blur'}
              ],
                plantorgan1: [
                {required: true, message: 'plantorgan1不能为空', trigger: 'blur'}
              ],
                plantorgan2: [
                {required: true, message: 'plantorgan2不能为空', trigger: 'blur'}
              ],
                abundvalue: [
                {required: true, message: 'abundvalue不能为空', trigger: 'blur'}
              ],
                abundunit: [
                {required: true, message: 'abundunit不能为空', trigger: 'blur'}
              ],
                fossiltype: [
                {required: true, message: 'fossiltype不能为空', trigger: 'blur'}
              ],
        }
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
