<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.codegen.mapper.GeneratorDB2Mapper">

	<select id="queryTable" resultType="map">
		SELECT
			'db2' AS "dbType",
			TABNAME AS "tableName",
			REMARKS AS "tableComment",
			CREATE_TIME AS "createTime"
		FROM syscat.TABLES
		WHERE TABSCHEMA = (SELECT CURRENT_SCHEMA
			FROM SYSIBM.SYSDUMMY1
		)
		<if test="tableName != null and tableName.trim() != ''">
			and TABNAME = #{tableName}
		</if>
		ORDER BY CREATE_TIME DESC
	</select>


	<sql id="queryColumn">
		SELECT 'db2'    AS "dbType",
			   COLNAME  AS "columnName",
			   TYPENAME AS "dataType",
			   REMARKS  AS "comments",
			   TYPENAME AS "columnType"
		FROM SYSCAT.COLUMNS
		WHERE TABSCHEMA = (SELECT CURRENT_SCHEMA
						   FROM SYSIBM.SYSDUMMY1
		)
		  and TABNAME = #{tableName}
	</sql>

	<select id="selectTableColumn" resultType="com.tdkj.tdcloud.codegen.entity.ColumnEntity">
		<include refid="queryColumn"/>
	</select>

	<select id="selectMapTableColumn" resultType="map">
		<include refid="queryColumn"/>
	</select>

</mapper>
