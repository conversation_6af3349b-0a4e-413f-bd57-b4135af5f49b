import { request } from '@/api/request'

/**
 * Main data query endpoint with complex filtering
 * Equivalent to Python Flask /data endpoint
 * @param {Object} query - Query parameters for filtering
 * @returns {Promise} Promise object representing the API response
 */
export function getPaleoecologyData(query) {
    return request({
        url: '/admin/paleoecology/data',
        method: 'get',
        params: query
    })
}

/**
 * Get detail data from Chattian table by ID
 * Equivalent to Python Flask /detail endpoint
 * @param {string} id - Record ID
 * @returns {Promise} Promise object representing the API response
 */
export function getDetailData(id) {
    return request({
        url: '/admin/paleoecology/detail',
        method: 'get',
        params: { id }
    })
}
