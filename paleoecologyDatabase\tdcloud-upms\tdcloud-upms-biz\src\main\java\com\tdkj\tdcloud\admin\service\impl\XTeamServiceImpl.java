/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.XTeamDto;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XAboutUs;
import com.tdkj.tdcloud.admin.api.entity.XTeam;
import com.tdkj.tdcloud.admin.mapper.XTeamMapper;
import com.tdkj.tdcloud.admin.service.SysFileService;
import com.tdkj.tdcloud.admin.service.XTeamService;
import com.tdkj.tdcloud.common.core.util.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 团队
 *
 * <AUTHOR> code generator
 * @date 2025-01-06 09:47:46
 */
@Service
public class XTeamServiceImpl extends ServiceImpl<XTeamMapper, XTeam> implements XTeamService {

	@Resource
	private XTeamMapper xTeamMapper;
	@Resource
	private SysFileService sysFileService;

	@Override
	public Page getXTeamPage(Page page, XTeamDto xTeamDto) {
		//查询条件
		QueryWrapper<XTeam> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}
		wrapper.eq(StringUtils.isNotBlank(xTeamDto.getType()), "type", xTeamDto.getType());
		wrapper.eq(StringUtils.isNotBlank(xTeamDto.getLanguageType()), "language_type", xTeamDto.getLanguageType());
//		wrapper.like(StringUtils.isNotBlank(kibSpecimenDto.getTitle()), "title", kibSpecimenDto.getTitle());
		wrapper.orderByAsc("sort");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}

	@Override
	public R saveXTeam(XTeam xTeam) {
		xTeam.setCreateTime(LocalDateTime.now());
		int i = xTeamMapper.insertXTeam(xTeam);
		if (i==1){
			if (xTeam.getSysFile()!=null){
				xTeam.getSysFile().setXId(xTeam.getId());
				xTeam.getSysFile().setXType("team");
				sysFileService.save(xTeam.getSysFile());
			}
			return R.ok(i,"添加成功");
		}
		return null;
	}

	@Override
	public R updateXTeamById(XTeam xTeam) {
		int i = xTeamMapper.updateXTeam(xTeam);
		if (i==1){
			if (xTeam.getSysFile()!=null){
				if (xTeam.getSysFile().getId()==null){
					xTeam.getSysFile().setXId(xTeam.getId());
					xTeam.getSysFile().setXType("team");
					sysFileService.save(xTeam.getSysFile());
				}else {
					sysFileService.updateById(xTeam.getSysFile());
				}

			}
			return R.ok(i,"修改成功");
		}
		return null;
	}

	@Override
	public R getXTeamById(Integer id) {
		XTeam xTeam = xTeamMapper.selectXTeamById(Long.valueOf(id));
		SysFile sysFile = xTeamMapper.getFileByXId(id,"team");
		if (sysFile!=null){
			xTeam.setSysFile(sysFile);
		}
		return R.ok(xTeam,"详情");
	}
}
