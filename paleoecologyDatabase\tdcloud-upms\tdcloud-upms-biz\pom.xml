<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tdkj</groupId>
        <artifactId>tdcloud-upms</artifactId>
        <version>4.6.0</version>
    </parent>

    <artifactId>tdcloud-upms-biz</artifactId>
    <packaging>jar</packaging>

    <description>tdcloud 通用用户权限管理系统业务处理模块</description>

    <dependencies>
        <!--认证中心-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-auth</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--定时任务-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-quartz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-upms-api</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-data</artifactId>
        </dependency>
        <!--文件系统-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-oss</artifactId>
        </dependency>
        <!--XSS 安全过滤-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-xss</artifactId>
        </dependency>

        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!-- cas sdk -->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>${cas.sdk.version}</version>
        </dependency>
        <!--旧版api,新版api未包含全部的服务端API的产品能力-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>${dingtalk.old.version}</version>
        </dependency>
        <!--企业微信-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>${springdoc.version}</version>
		</dependency>

		<dependency>
			<groupId>com.tdkj</groupId>
			<artifactId>tdcloud-common-swagger</artifactId>
		</dependency>

	</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
