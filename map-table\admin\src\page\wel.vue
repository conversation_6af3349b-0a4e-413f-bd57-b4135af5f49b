<template>
  <div class="home">
<!--    <basic-container>-->
      <section class="home-statistics">
        <ul class="statistics-ul">
          <li class="statistics-item" v-for="statisticItem in statisticsList" :key="statisticItem.id">
            <div class="statistics-item-pic"><img class="pic-icon" :src="statisticItem.icon" alt=""></div>
            <div class="statistics-item-name">{{ statisticItem.name }}</div>
            <div class="statistics-item-number">{{ statisticItem.number }}</div>
          </li>
        </ul>
      </section>
    <section class="home-chart">
      <ul class="chart-ul">
        <li class="chart-item"><div id="category" style="width: 100%;height:100%;"></div></li>
        <li class="chart-item"><div id="pie" style="width: 100%;height:100%;"></div></li>
      </ul>
    </section>

<!--    </basic-container>-->
  </div>
</template>

<script>
import * as echarts from 'echarts';
import {getSpecimenCount} from "@/api/common";
export default {
  name: 'Wel',
  data() {
    return {
      statisticsList: [
        {id: 1, name: 'Number of specimens', number: '', icon: require('../assets/images/numberSpecimens.png')},
        {id: 2, name: 'Sampling Sites', number: '', icon: require('../assets/images/samplingSites.png')},
        {id: 3, name: 'Place of distribution', number: '', icon: require('../assets/images/placeDistribution.png')},
        {id: 4, name: 'Research publication', number: '', icon: require('../assets/images/researchPublication.png')},
      ],
      rupelianEpochTotal: [],
      rupelianFossilTotal: [],
    }
  },
  mounted() {
    this.getInitData()
  },
  methods: {
    async getInitData() {
      let res = await getSpecimenCount()
      if(res.data.code === 0) {
        this.statisticsList[0].number = res.data.data.specimenNum.toLocaleString()
        this.statisticsList[1].number = res.data.data.collectionPlaceTotal.toLocaleString()
        this.statisticsList[2].number = res.data.data.distributionTotal.toLocaleString()
        this.statisticsList[3].number = res.data.data.publicationTotal.toLocaleString()
        this.rupelianEpochTotal = res.data.data.rupelianEpochTotal
        this.rupelianFossilTotal = res.data.data.rupelianFossilTotal
      }
      await  this.getCategory()
      await  this.getPie()
    },
    //柱状图
    getCategory() {
      let chartDom = document.getElementById('category');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: 'Cenozoic distribution statistics',
          left: 50,
          top: 10,
          textStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#2C4A52'
          }
        },
        xAxis: {
          type: 'category',
          data: ['Paleocene', 'Eocene', 'Oligocene', 'Miocene', 'Pliocene', 'Pleistocene', 'Holocene'],
          axisLabel: {
            textStyle: {
              color: '#2C4A52', // 这里可以设置你想要的颜色，例如 '#333' 是深灰色
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#C8CACA'],
              type: 'solid'
            }
          }

        },
        yAxis: {
          type: 'value',
          axisTick: {
            length: 5,
            lineStyle: {
              color: '#C8CACA'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#2C4A52', // 这里可以设置你想要的颜色，例如 '#333' 是深灰色
              fontSize: 16
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#C8CACA'],
              type: 'solid'
            }
          }
        },
        series: [
          {
            data: [this.rupelianEpochTotal.paleoceneCount, this.rupelianEpochTotal.eoceneCount,
              this.rupelianEpochTotal.oligoceneCount, this.rupelianEpochTotal.mioceneCount,
              this.rupelianEpochTotal.plioceneCount, this.rupelianEpochTotal.pleistoceneCount,
              this.rupelianEpochTotal.holoceneCount,],
            type: 'bar',
            itemStyle: {
              fontSize: 20,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#BB915D' }, // 起始颜色（深棕色）
                { offset: 1, color: '#324543' } // 结束颜色（浅棕色）
              ])
            }
          }
        ]
      };
      option && myChart.setOption(option);
    },
    //饼图
    getPie() {
      let chartDom = document.getElementById('pie');
      let myChart = echarts.init(chartDom);
      let option;
      option = {
        title: {
          text: 'Distribution of fossil types',
          left: 50,
          top: 10,
          textStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#2C4A52'
          }
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical', // 设置为垂直方向
          left: '5%',
          top: '20%',
          right: '20%',
          textStyle: {
            fontSize: 16,
            color: '#333'
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '75%'],
            center: ['70%', '50%'], // 通过调整 center 的值来改变饼图位置，从而拉开与图例的距离
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
              itemStyle: { color: '#2C4A52' }

            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold',
              }
            },
            labelLine: {
              show: false
            },
            data: this.rupelianFossilTotal,
            itemStyle: {
              color: function (params) {
                const colors = ['#924E4E','#45656E', '#528961', '#98725A' ];
                return colors[params.dataIndex % colors.length];
              }
            }
            // data: [
            //   { value: 400, name: 'Plant fossils',itemStyle: { color: '#45656E' }  },
            //   { value: 420, name: 'Pollen and spore fossils',itemStyle: { color: '#528961' } },
            //   { value: 50, name: 'Vertebrates' ,itemStyle: { color: '#98725A' }},
            //   { value: 484, name: 'Others',itemStyle: { color: '#924E4E' } },
            // ],

          }
        ]
      };

      option && myChart.setOption(option);
    },
  }
}
</script>

<style lang="scss" scoped>
#avue-view {
  width: 100vw;
  height: 100vh;
  background-color: #F2EEE5;
}
.home-statistics {
  margin-top: 50px;
  .statistics-ul {
    display: flex;
    align-items: center;
    .statistics-item {
      width: calc((100% - 130px) / 4);
      height: 447px;
      background: rgba(255,255,255,0.5);
      border-radius: 8px;
      margin-right: 39px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      //&:nth-child(4) {
      //  margin-right: 0;
      //}
      .statistics-item-pic {
        width: 200px;
        height: 200px;
        .pic-icon {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
      .statistics-item-name {
        font-size: 24px;
        color: var(--home-text-color);
        margin: 20px 0;
      }
      .statistics-item-number {
        font-size: 36px;
        color: var(--home-text-color);

      }
    }
  }
}
.home-chart {
  margin-top: 50px;
  .chart-ul {
    display: flex;
    align-items: center;
    list-style: none;
    .chart-item {
      width: calc((100% - 42px) / 2);
      height: 457px;
      margin-right: 39px;
      border-radius: 8px;
      background: rgba(255,255,255,0.5);
      //&:nth-child(2) {
      //  margin-right: 0;
      //}
    }
  }
}
</style>
