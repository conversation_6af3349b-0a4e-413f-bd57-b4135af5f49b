/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.XAboutUsDto;
import com.tdkj.tdcloud.admin.api.entity.XAboutUs;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import com.tdkj.tdcloud.admin.service.XAboutUsService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 关于我们
 *
 * <AUTHOR> code generator
 * @date 2025-01-03 14:40:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/xaboutus" )
@Tag(description = "xaboutus" , name = "关于我们管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XAboutUsController {

    private final  XAboutUsService xAboutUsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param xAboutUs 关于我们
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_xaboutus_view')" )
    public R getXAboutUsPage(Page page, XAboutUsDto xAboutUs) {
        return R.ok(xAboutUsService.getXAboutUsPage(page, xAboutUs));
    }


    /**
     * 通过id查询关于我们
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xaboutus_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return xAboutUsService.getXAboutUsById(id);
    }

    /**
     * 新增关于我们
     * @param xAboutUs 关于我们
     * @return R
     */
    @Operation(summary = "新增关于我们" , description = "新增关于我们" )
    @SysLog("新增关于我们" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_xaboutus_add')" )
    public R save(@RequestBody XAboutUs xAboutUs) {
        return xAboutUsService.saveXAboutUs(xAboutUs);
    }

    /**
     * 修改关于我们
     * @param xAboutUs 关于我们
     * @return R
     */
    @Operation(summary = "修改关于我们" , description = "修改关于我们" )
    @SysLog("修改关于我们" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_xaboutus_edit')" )
    public R updateById(@RequestBody XAboutUs xAboutUs) {
        return xAboutUsService.updateXAboutUsById(xAboutUs);
    }

    /**
     * 通过id删除关于我们
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id删除关于我们" , description = "通过id删除关于我们" )
    @SysLog("通过id删除关于我们" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xaboutus_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(xAboutUsService.removeById(id));
    }


    /**
     * 导出excel 表格
     * @param xAboutUs 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_xaboutus_export')" )
    public List<XAboutUs> export(XAboutUs xAboutUs) {
        return xAboutUsService.list(Wrappers.query(xAboutUs));
    }
}
