<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Endpoint Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Paleoecology API Frontend Testing</h1>
    <p>This page tests the API endpoints through the Vue.js development proxy.</p>
    
    <div>
        <button onclick="testAllEndpoints()">Test All Endpoints</button>
        <button onclick="testMainDataEndpoint()">Test Main Data Endpoint</button>
        <button onclick="testDetailEndpoint()">Test Detail Endpoint</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        // Base URL for frontend proxy (Vue.js dev server)
        const BASE_URL = '/admin/paleoecology';
        
        // Test endpoints
        const endpoints = [
            { name: 'Dating Methods', url: `${BASE_URL}/dating-methods` },
            { name: 'Dating Qualities', url: `${BASE_URL}/dating-qualities` },
            { name: 'Families', url: `${BASE_URL}/taxa/families` },
            { name: 'Genera', url: `${BASE_URL}/taxa/genera` },
            { name: 'Species', url: `${BASE_URL}/taxa/species` },
            { name: 'Scientific Names', url: `${BASE_URL}/taxa/scientific-names` },
            { name: 'Original Names', url: `${BASE_URL}/taxa/original-names` },
            { name: 'Fossil Types', url: `${BASE_URL}/fossil-types` },
            { name: 'Plant Organs', url: `${BASE_URL}/plant-organs` }
        ];

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testEndpoint(endpoint) {
            try {
                addResult(`Testing ${endpoint.name}...`, 'info');
                
                const response = await fetch(endpoint.url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ ${endpoint.name}: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && Array.isArray(data.data)) {
                        addResult(`   Data length: ${data.data.length}`, 'info');
                        if (data.data.length > 0) {
                            addResult(`   Sample data: ${data.data.slice(0, 3).join(', ')}`, 'info');
                        }
                    } else if (Array.isArray(data)) {
                        addResult(`   Array length: ${data.length}`, 'info');
                        if (data.length > 0) {
                            addResult(`   Sample data: ${data.slice(0, 3).join(', ')}`, 'info');
                        }
                    }
                } else {
                    addResult(`❌ ${endpoint.name}: FAILED - Status ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ ${endpoint.name}: ERROR - ${error.message}`, 'error');
            }
        }

        async function testMainDataEndpoint() {
            try {
                addResult('Testing Main Data Endpoint...', 'info');
                
                const params = new URLSearchParams({
                    family: 'Fagaceae',
                    southLatitude: '-90',
                    northLatitude: '90',
                    westLongitude: '-180',
                    eastLongitude: '180'
                });
                
                const response = await fetch(`${BASE_URL}/data?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Main Data Endpoint: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && data.data.table1) {
                        addResult(`   Results count: ${data.data.table1.length}`, 'info');
                        if (data.data.table1.length > 0) {
                            const sample = data.data.table1[0];
                            addResult(`   Sample fields: ${Object.keys(sample).slice(0, 5).join(', ')}`, 'info');
                        }
                    }
                } else {
                    addResult(`❌ Main Data Endpoint: FAILED - Status ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Main Data Endpoint: ERROR - ${error.message}`, 'error');
            }
        }

        async function testDetailEndpoint() {
            try {
                addResult('Testing Detail Endpoint...', 'info');
                
                const response = await fetch(`${BASE_URL}/detail?id=1`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Detail Endpoint: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && data.data.detail) {
                        addResult(`   Detail records: ${data.data.detail.length}`, 'info');
                    }
                } else {
                    addResult(`❌ Detail Endpoint: FAILED - Status ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Detail Endpoint: ERROR - ${error.message}`, 'error');
            }
        }

        async function testAllEndpoints() {
            clearResults();
            addResult('='.repeat(60), 'info');
            addResult('PALEOECOLOGY API FRONTEND TESTING', 'info');
            addResult('='.repeat(60), 'info');
            
            // Test lookup endpoints
            addResult('Testing Lookup Endpoints:', 'info');
            addResult('-'.repeat(30), 'info');
            for (const endpoint of endpoints) {
                await testEndpoint(endpoint);
            }
            
            // Test main data endpoint
            addResult('Testing Main Data Endpoint:', 'info');
            addResult('-'.repeat(30), 'info');
            await testMainDataEndpoint();
            
            // Test detail endpoint
            addResult('Testing Detail Endpoint:', 'info');
            addResult('-'.repeat(30), 'info');
            await testDetailEndpoint();
            
            addResult('='.repeat(60), 'info');
            addResult('TESTING COMPLETE', 'info');
            addResult('='.repeat(60), 'info');
        }

        // Add instructions
        addResult('Instructions:', 'info');
        addResult('1. Make sure the Vue.js development server is running on port 8181', 'info');
        addResult('2. Make sure the Java Spring Boot backend is running on port 8888', 'info');
        addResult('3. Open this file through the Vue.js dev server: http://localhost:8181/test-frontend-endpoints.html', 'info');
        addResult('4. Click "Test All Endpoints" to run the tests', 'info');
        addResult('-'.repeat(60), 'info');
    </script>
</body>
</html>
