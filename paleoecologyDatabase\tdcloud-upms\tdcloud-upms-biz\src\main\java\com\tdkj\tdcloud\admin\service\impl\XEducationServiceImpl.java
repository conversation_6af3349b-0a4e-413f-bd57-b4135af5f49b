/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.XEducationDto;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XEducation;
import com.tdkj.tdcloud.admin.mapper.XEducationMapper;
import com.tdkj.tdcloud.admin.mapper.XTeamMapper;
import com.tdkj.tdcloud.admin.service.SysFileService;
import com.tdkj.tdcloud.admin.service.XEducationService;
import com.tdkj.tdcloud.common.core.util.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 教育
 *
 * <AUTHOR> code generator
 * @date 2025-01-07 13:57:12
 */
@Service
public class XEducationServiceImpl extends ServiceImpl<XEducationMapper, XEducation> implements XEducationService {

	@Resource
	private XEducationMapper xEducationMapper;
	@Resource
	private SysFileService sysFileService;
	@Resource
	private XTeamMapper xTeamMapper;

	@Override
	public Page getXEducationPage(Page page, XEducationDto xEducationDto) {
		//查询条件
		QueryWrapper<XEducation> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}
		wrapper.eq(StringUtils.isNotBlank(xEducationDto.getType()), "type", xEducationDto.getType());
		wrapper.eq(StringUtils.isNotBlank(xEducationDto.getLanguageType()), "language_type", xEducationDto.getLanguageType());
//		wrapper.like(StringUtils.isNotBlank(kibSpecimenDto.getTitle()), "title", kibSpecimenDto.getTitle());
		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);
		List<XEducation> xEducationList = page1.getRecords();
		if (xEducationList!=null && xEducationList.size()>0){
			for (XEducation xe: xEducationList){
				SysFile sysFile = xTeamMapper.getFileByXId(xe.getId(),"education");
				SysFile video = xTeamMapper.getFileByXId(xe.getId(),"video");
				if (sysFile!=null){
					xe.setSysFile(sysFile);
				}
				if (video!=null){
					xe.setVideo(video);
				}
			}
		}


		return page1;
	}

	@Override
	public R saveXEducation(XEducation xEducation) {
		xEducation.setCreateTime(LocalDateTime.now());
		int i = xEducationMapper.insertXEducation(xEducation);
		if (i==1){
			if (xEducation.getSysFile()!=null){
				xEducation.getSysFile().setXId(xEducation.getId());
				xEducation.getSysFile().setXType("education");
				sysFileService.save(xEducation.getSysFile());
			}
			if (xEducation.getVideo()!=null){
				xEducation.getVideo().setXId(xEducation.getId());
				xEducation.getVideo().setXType("video");
				sysFileService.save(xEducation.getVideo());
			}
			return R.ok(i,"添加成功");
		}
		return null;
	}

	@Override
	public R updateXEducationById(XEducation xEducation) {
		int i = xEducationMapper.updateXEducation(xEducation);
		if (i==1){
			if (xEducation.getSysFile()!=null){
				if (xEducation.getSysFile().getId()==null){
					xEducation.getSysFile().setXId(xEducation.getId());
					xEducation.getSysFile().setXType("education");
					sysFileService.save(xEducation.getSysFile());
				}else {
					sysFileService.updateById(xEducation.getSysFile());
				}

			}
			if (xEducation.getVideo()!=null){
				if (xEducation.getVideo().getId()==null){
					xEducation.getVideo().setXId(xEducation.getId());
					xEducation.getVideo().setXType("video");
					sysFileService.save(xEducation.getVideo());
				}else {
					sysFileService.updateById(xEducation.getVideo());
				}
			}
			return R.ok(i,"修改成功");
		}
		return null;
	}

	@Override
	public R getXEducationById(Integer id) {
		XEducation xEducation = xEducationMapper.selectXEducationById(Long.valueOf(id));
		SysFile sysFile = xTeamMapper.getFileByXId(id,"education");
		SysFile video = xTeamMapper.getFileByXId(id,"video");
		if (sysFile!=null){
			xEducation.setSysFile(sysFile);
		}
		if (video!=null){
			xEducation.setVideo(video);
		}
		return R.ok(xEducation,"详情");
	}
}
