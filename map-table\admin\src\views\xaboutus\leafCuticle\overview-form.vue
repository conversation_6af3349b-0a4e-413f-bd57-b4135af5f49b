<template>
  <el-dialog
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="80px">
    <el-form-item label="语言类型" prop="languageType">
        <el-select v-model="dataForm.languageType" placeholder="语言类型" style="width: 100%;">
          <el-option label="中文" value="zh"></el-option>
          <el-option label="英文" value="en"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="内容" prop="content">
      <quill-editor ref="myQuillEditor" style="width: 100%;" :options="tl" v-model="dataForm.content"/>
    </el-form-item>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/xaboutus/xaboutus'
  import tl from '@/util/editor'
  export default {
    data() {
      return {
        tl:tl,
        visible: false,
        canSubmit: false,
        dataForm: {
          id: null,
          languageType: '',
          type: 'leafCuticle',
          content: '',
          phone: '',
          address: '',
          postalCode: '',
          createTime: '',
          name: '',
          url: '',
        },
        dataRule: {
                languageType: [
                {required: true, message: '语言类型不能为空', trigger: 'blur'}
              ],
                content: [
                {required: true, message: '内容不能为空', trigger: 'blur'}
              ],

        }
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
