#\u4EE3\u7801\u751F\u6210\u5668\uFF0C\u914D\u7F6E\u4FE1\u606F
mainPath=com.pig4cloud.pigx
#\u5305\u540D
package=com.pig4cloud.pigx
moduleName=generator
#\u4F5C\u8005
author=pigx code generator
#\u8868\u524D\u7F00(\u7C7B\u540D\u4E0D\u4F1A\u5305\u542B\u8868\u524D\u7F00)
tablePrefix=tb_
# hidden columns in Swagger Models
hiddenColumn=tenant_id

#MYSQL \u7C7B\u578B\u8F6C\u6362
tinyint=Integer
smallint=Integer
mediumint=Integer
int=Integer
integer=Integer
bigint=Long
float=Float
double=Double
decimal=BigDecimal
bit=Boolean
char=String
varchar=String
tinytext=String
text=String
mediumtext=String
longtext=String
date=LocalDateTime
datetime=LocalDateTime
timestamp=LocalDateTime

# PG \u7C7B\u578B\u8F6C\u5316\u89C4\u5219
int2=Integer
int4=Integer
int8=Long
bpchar=String
float4=Float
float8=Float

#ORACLE \u7279\u6B8A\u7C7B\u578B\u8F6C\u6362\u89C4\u5219
INT=Integer
INTEGER=Integer
LONG=LONG
NUMBER=Integer
BINARY_INTEGER=Integer
FLOAT=Float
BINARY_FLOAT=Float
DOUBLE=Double
BINARY_DOUBLE=Double
DECIMAL=BigDecimal
CHAR=String
VARCHAR=String
VARCHAR2=String
NVARCHAR=String
NVARCHAR2=String
DATE=LocalDateTime
DATETIME=LocalDateTime
TIMESTAMP=LocalDateTime
CLOB=String
BLOB=String

# MSSQL \u7C7B\u578B
nvarchar=String
nchar=String
datetime2=LocalDateTime

#\u8FBE\u68A6
BIGINT=Long
