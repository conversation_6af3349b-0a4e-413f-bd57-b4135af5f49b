<template>
  <div class="education-detail">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/education' }">{{ $t('nav.educationNav') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $t('nav.educationDetailNav') }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="detail-title">{{infoContent.title}}</div>
    <template>
      <div class="detail-cover " v-if="$route.query.type !== 'video'">
        <img class="cover-pic" :src="infoContent.sysFile?infoContent.sysFile.url : ''" alt="">
      </div>
      <div v-else  class="detail-main-video">
        <video :poster="infoContent.sysFile.url" style="width: 100%;height: 100%;" ref="videoPlayer"  class="video-js vjs-default-skin" controls></video>
      </div>
    </template>
    <div class="detail-content ql-container ql-editor" v-html="infoContent.content"></div>
  </div>
</template>

<script>
import {getEducationObj} from "@/api/education";
import 'video.js/dist/video-js.css';
import videojs from 'video.js';
import 'videojs-contrib-hls';
export default {
  name: "educationDetail",
  data() {
    return {
      infoContent: {}
    }
  },
  mounted() {
    this.getEducationObj()
  },
  methods: {
    getEducationObj() {
      getEducationObj(this.$route.params.id).then(res => {
        if (res.data.code === 0) {
          this.infoContent = res.data.data
          if (res.data.data.video) {
             let videoUrl = res.data.data.video.url
            this.$nextTick(() => {
              this.player = videojs(this.$refs.videoPlayer, {
                playbackRates: [0.5, 1, 1.25, 1.5, 2], // 添加倍速播放选项
                autoplay: true, // 自动播放
                loop: true, // 循环播放
                sources: [{
                  src: videoUrl,
                  type: 'application/x-mpegURL'
                }]
              }, function onPlayerReady() {
                // console.log('onPlayerReady', this);
              });
            })
          }
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-breadcrumb__inner {
  font-size: 16px !important;
}
.education-detail {
  min-height: calc(100vh - 272px - 280px);
  background-color: var(--about-background-color);
  padding: 50px 80px;
  .detail-title {
    font-size: 24px;
    margin-bottom: 16px;
    margin-top: 30px;
  }
  .detail-cover {
    //max-width: 75%;
    margin: 0 auto;
    .cover-pic  {
      display: block;
      max-width: 75%;
      height: 100%;
      margin: 0 auto;

      object-position: center;
      object-fit: cover;
    }
  }
  .detail-main-video {
    width: 800px;
    height: calc((800px * 9) / 16);
    background: rgba(0,0,0,0);
    margin: 0 auto 16px;
    border-radius: 8px;
    .video-container {
      position: relative !important;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0);
      border-radius: 8px;
    }
    .video-js {
      width: 100% !important;
      height: 100% !important;
    }
  }
  .detail-content {
    line-height: 1.6;
    font-size: 15px !important;
  }
}
</style>