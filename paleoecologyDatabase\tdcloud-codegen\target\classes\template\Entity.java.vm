/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package ${package}.${moduleName}.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@TableName("${tableName}")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "${comments}")
public class ${className} extends Model<${className}> {

    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
    /**
     * $column.comments
     */
#if($column.columnName == $pk.columnName)
    @TableId(type = IdType.ASSIGN_ID)
#end
#if($column.columnName == 'del_flag')
    @TableLogic
#end
    @Schema(description="$column.comments"#if($column.hidden),hidden=$column.hidden#end)
    private $column.attrType $column.lowerAttrName;

#end
}
