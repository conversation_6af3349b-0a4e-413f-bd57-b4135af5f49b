/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:20:56
 */
@Data
@TableName("chattian")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class ChattianExcelVO extends Model<ChattianExcelVO> {

    private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ExcelProperty("ID")
	private String ID;

	/**
	 * originalname
	 */
	@ExcelProperty("OriginalName")
	private String OriginalName;

	/**
	 * scientificname1
	 */
	@ExcelProperty("ScientificName1")
	private String ScientificName1;

	/**
	 * scientificname2
	 */
	@ExcelProperty("ScientificName2")
	private String ScientificName2;

	/**
	 * scientificname3
	 */
	@ExcelProperty("ScientificName3")
	private String ScientificName3;

	/**
	 * acceptedrank
	 */
	@ExcelProperty("AcceptedRank")
	private String AcceptedRank;

	/**
	 * phylum
	 */
	@ExcelProperty("Phylum")
	private String Phylum;

	/**
	 * classNew
	 */
	@ExcelProperty("ClassNew")
	private String ClassNew;

	/**
	 * orderNew
	 */
	@ExcelProperty("OrderNew")
	private String OrderNew;

	/**
	 * family
	 */
	@ExcelProperty("Family")
	private String Family;

	/**
	 * genus
	 */
	@ExcelProperty("Genus")
	private String Genus;

	/**
	 * species1
	 */
	@ExcelProperty("Species1")
	private String Species1;

	/**
	 * species2
	 */
	@ExcelProperty("Species2")
	private String Species2;

	/**
	 * species3
	 */
	@ExcelProperty("Species3")
	private String Species3;

	/**
	 * plantorgan1
	 */
	@ExcelProperty("PlantOrgan1")
	private String PlantOrgan1;

	/**
	 * plantorgan2
	 */
	@ExcelProperty("PlantOrgan2")
	private String PlantOrgan2;

	/**
	 * abundvalue
	 */
	@ExcelProperty("AbundValue")
	private String AbundValue;

	/**
	 * abundunit
	 */
	@ExcelProperty("AbundUnit")
	private String AbundUnit;

	/**
	 * fossiltype
	 */
	@ExcelProperty("FossilType")
	private String FossilType;

	/**
	 * pollendiagram
	 */
	@ExcelProperty("PollenDiagram")
	private String PollenDiagram;

	/**
	 * assemblage
	 */
//	@ExcelProperty("Assemblage")
//	private String Assemblage;

	/**
	 * siteno
	 */
	@ExcelProperty("SiteNo")
	private String SiteNo;

	/**
	 * sitename
	 */
	@ExcelProperty("SiteName")
	private String SiteName;

	/**
	 * extinct
	 */
	@ExcelProperty("Extinct")
	private String Extinct;

	/**
	 * timecontext
	 */
	@ExcelProperty("TimeContext")
	private String TimeContext;

	/**
	 * cid
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@ExcelProperty("Cid")
	private Integer Cid;

}
