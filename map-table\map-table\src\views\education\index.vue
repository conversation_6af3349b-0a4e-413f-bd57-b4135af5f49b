<template>
  <div class="education">
    <section class="education-main">
      <template>
        <div class="education-main-top">
          <div>
            <template v-if="type === 'articles'">
              <ul class="education-ul" v-if="total > 0">
                <li class="education-ul-item" v-for="articles in articlesList" :key="articles.id" @click="clickDetails(articles.id)">
                  <div class="education-item-left"><img class="pic" :src="articles.sysFile ? articles.sysFile.url : ''" alt=""></div>
                  <div class="education-item-right">
                    <div class="intro">
                      <div class="title">{{articles.title}}</div>
                      <div class="content ql-container ql-editor" v-html="articles.content"></div>
                    </div>
                    <div class="time">{{ articles.time }}</div>
                  </div>
                </li>
              </ul>
              <el-empty v-else :image-size="250"></el-empty>
            </template>

            <ul class="education-video" v-else-if="type === 'video'">
              <li class="video-item" v-for="video in videosList" :key="video.id" @click="clickDetails(video.id)">
                <div class="video-item-pic">
                  <img class="pic" :src="video.sysFile ? video.sysFile.url : ''" alt=""/>
                  <div class="pic-icon el-icon-caret-right"></div>
                </div>
                <div class="video-item-name">{{video.title}}</div>
              </li>
            </ul>
            <ul class="education-publication" v-else>
              <li class="publication-item" v-for="publicItem in publicationList" :key="publicItem.id" @click="clickDetails(publicItem.id)">
                <div class="publication-item-pic"><img class="img" :src="(publicItem.sysFile ) ? publicItem.sysFile.url : ''" alt=""/></div>
                <div class="publication-item-bottom">
                  <div class="publication-item-name">{{ publicItem.title}}</div>
                  <div class="publication-item-time">{{ publicItem.time }}</div>
                </div>
              </li>
            </ul>
          </div>
          <el-pagination style="text-align: right;margin-right: 365px;" v-if="total > 0"
              background  @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="pageIndex"
                         :page-size="pageSize"
              layout="prev, pager, next"
              :total="total">
          </el-pagination>
        </div>
      </template>
      <div class="education-nav">
        <div class="education-nav-title">{{ $t('education.educationText') }}</div>
        <ul class="education-nav-ul">
          <li :class="[currentIndex === 0 ? 'active' : '']" @click="toggleBtn(0,'articles')">{{$t('education.articlesText')}}</li>
          <li :class="[currentIndex === 1 ? 'active' : '']" @click="toggleBtn(1,'video')">{{$t('education.videoText')}}</li>
          <li :class="[currentIndex === 2 ? 'active' : '']" @click="toggleBtn(2,'publication')">{{$t('education.publicationText')}}</li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script>
import {getEducationList} from "@/api/education";
import {mapState} from "vuex";

export default {
  name: "EducationIndex",
  data() {
    return {
      currentIndex: 0,
      type: 'articles',
      articlesList: [],
      videosList: [],
      publicationList: [],
      pageIndex: 1,
      pageSize: 10,
      total: 0
    }
  },
  computed: {
    ...mapState({
      language: state => state.language // 将language从store映射到计算属性
    })
  },
  watch: {
    language() {
      this.getEducationList(); // 语言变化时重新获取内容
    }
  },
  mounted() {
    this.getEducationList()
  },
  methods: {
    toggleBtn(index,type) {
      this.currentIndex = index
      this.type = type
      this.pageIndex = 1
      this.getEducationList()
    },
    getEducationList() {
      this.pageSize = this.type === 'articles'  ? 10 : (this.type === 'video' ? 9 : 8)
      getEducationList(Object.assign({
        current: this.pageIndex,
        size: this.pageSize,
        type: this.type,
        languageType: this.language
      })).then(res => {
        if(res.data.code === 0) {

          if(this.type === 'articles') {
            this.articlesList = res.data.data.records.map(item => {
              let dateObj = new Date(item.createTime);
              let year = dateObj.getFullYear();
              let month = String(dateObj.getMonth() + 1).padStart(2, '0');
              let day = String(dateObj.getDate()).padStart(2, '0');
              let dateStr = `${year}-${month}-${day}`;
              return {
                ...item,
                time: dateStr
              }
            })
            this.total = res.data.data.total
          }else if(this.type === 'video') {
            this.videosList = res.data.data.records
            this.total = res.data.data.total
          }else {
            this.publicationList = res.data.data.records.map(item => {
              let dateObj = new Date(item.createTime);
              let year = dateObj.getFullYear();
              let month = String(dateObj.getMonth() + 1).padStart(2, '0');
              let dateStr = `${year}-${month}`;
              return {
                ...item,
                time: dateStr
              }
            })
            this.total = res.data.data.total
          }
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getEducationList()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pageIndex = val
      this.getEducationList()
    },
    clickDetails(id) {
      this.$router.push({path: `/educationDetail/${id}`,query: {type: this.type}})
    }
  }
}
</script>

<style scoped lang="scss">
.education {
  width: 100%;
  min-height: calc(100vh - 273px - 280px);
  position: relative;
  background-color: var(--about-background-color);
  .education-main {
    display: flex;
    padding: 50px 80px 50px;
    position: relative;
    .education-main-top {
      display:flex;
      flex-direction: column;
      width: 100%;
    }
    .education-ul {
      width: calc(100% - 320px - 43px );
      text-align: left;
      .education-ul-item {
        height: 268px;
        margin-bottom: 40px;
        display: flex;
        cursor: pointer;
        &:last-child {
          margin-bottom: 0;
        }
        .education-item-left {
          width: 400px;
          height: 100%;
          margin-right: 20px;
          overflow: hidden;
          .pic {
            width: 100%;
            height: 100%;
            object-position: center;
            object-fit: cover;
            -webkit-transition: all 0.3s linear;
          }
          &:hover .pic {
              -webkit-transform: scale(1.05);
          }
        }
        .education-item-right {
          width: calc(100% - 400px - 20px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .intro {
            .title {
              font-size: 18px;
              color: #2C4A52;
              margin-bottom: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .content {
              height: 40px;
              font-size: 14px;
              color: #666666;
              padding: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              word-break: break-all;
              -webkit-box-orient: vertical;

            }
          }
          .time {
            font-size: 14px;
            color: var(--primary-text-color);
          }
        }
      }
    }
    .education-video {
      width: calc(100% - 320px - 43px );
      text-align: left;
      .video-item {
        float: left;
        flex-direction: row;
        width: calc((100% - 60px) / 3);
        margin-right: 30px;
        margin-bottom: 30px;
        cursor: pointer;
        &:nth-child(3n) {
          margin-right: 0;
        }
        &:nth-child(3n + 1) {
          clear: both;
        }
        .video-item-pic {
          width: 100%;
          aspect-ratio: 4 / 3;
          position: relative;
          .pic {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            border-radius: 8px;
            display: block;
          }
          .pic-icon {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 46px;
            height: 46px;
            line-height: 46px;
            border-radius: 50%;
            background: rgba(0,0,0,0.5);
            text-align: center;
            font-size: 32px;
            color: #fff;
          }
        }
        .video-item-name {
          text-align: center;
          height: 46px;
          padding-top: 20px;
          font-size: 18px;
          color: #2C4A52;
          display: -webkit - box; /* 将对象作为弹性伸缩盒子模型显示 */
          -webkit-box-orient: vertical; /* 设置伸缩盒子的子元素排列方式为垂直排列 */
          -webkit-line-clamp: 2; /* 显示的行数为 2 行 */
          overflow: hidden; /** 超出部分隐藏 */
          text-overflow: ellipsis; /* 超出部分用省略号表示 */
        }
      }
    }
    .education-publication {
      width: calc(100% - 320px - 43px );
      text-align: left;
      .publication-item {
        float: left;
        width: calc((100% - 90px) / 4);
        height: 400px;
        margin-right: 30px;
        margin-bottom: 30px;
        position: relative;
        box-shadow: 24px 10px 15px -1px rgba(0, 0, 0, 0.1), 20px 4px 6px -4px rgba(0, 0, 0, 0.1);
        background: linear-gradient( 360deg, rgba(0,0,0,.3) 0%, rgba(0,0,0,0) 100%), rgba(0,0,0,0);
        border-radius: 8px;
        z-index: 1;
        cursor: pointer;
        &:nth-child(4n) {
          margin-right: 0;
        }
        &:nth-child(4n + 1) {
          clear: both;
        }
        .publication-item-pic{
          width: 100%;
          height: 100%;
          border-radius: 8px;
          .img{
            width: calc(100% - 32px);
            height: calc(100% - 16px);
            object-fit: cover;
            object-position: center;
            //border-radius: 8px;
            padding: 16px 16px 0;
            background: rgba(0,0,0,0);
            box-shadow: 8px 10px 15px -2px rgba(0, 0, 0, 0.1), 14px 32px 76px -4px rgba(0, 0, 0, 0.1);
          }
        }
        .publication-item-bottom {
          position: absolute;
          bottom: 0px;
          height: 80px;
          width: calc(100% - 32px);
          background: linear-gradient( 360deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%), rgba(0,0,0,0);
          padding: 0 16px;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          .publication-item-name {
            font-size: 18px;
            color: #ffffff;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .publication-item-time {
            font-size: 14px;
            color: #ffffff;
          }
        }
        /*.publication-item-name {
          font-size: 18px;
          color: #ffffff;
          font-weight: 600;
          position: absolute;
          bottom: 36px;
          //left: 16px;
          //right: 16px;
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          background: linear-gradient( 360deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%), rgba(0,0,0,0);
        }
        .publication-item-time {
          font-size: 14px;
          color: #ffffff;
          position: absolute;
          bottom: 10px;
          //left: 16px;
          //right: 16px;
          background: linear-gradient( 360deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%), rgba(0,0,0,0);

        }*/
      }
    }
    .education-nav {
      width: 320px;
      margin-left: 43px;
      position: fixed;
      right: 80px;
      text-align: left;
      .education-nav-title {
        height: 60px;
        line-height: 60px;
        padding-left: 10px;
        font-size: 24px;
        color: var(--primary-text-color);
        border-bottom:  1px solid #DBDBDB;
      }
      .education-nav-ul {
        li {
          height: 60px;
          line-height: 60px;
          padding-left: 10px;
          font-size: 18px;
          color: #2C4A52;
          border-bottom:  1px solid #DBDBDB;
          cursor: pointer;
        }
        .active {
          background: #FFFFFF;
        }
      }
    }
  }
}
</style>