<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-demo"
      :on-change="handleChange"
      :on-remove="handleMove"
      drag
      :action="actionUrl"
      :limit="1"
      :auto-upload="false"
      accept = '.xls,.xlsx'>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">只能选择.xls文件、.xlsx文件
      </div>
    </el-upload>
    <template>
<!--      判断是采集地导入-->

      <template v-if="importType === 'xspecimen'">
        <el-button type="primary" @click="downloadFileSpecimen">下载模板</el-button>
        <el-button style="margin-top:20px;" size="small" type="primary" @click="submitUploadSpecimen">开始导入</el-button>
      </template>
      <template v-else-if="importType === 'rupelian'">
        <el-button type="primary" @click="downloadRupelian">下载模板</el-button>
        <el-button style="margin-top:20px;" size="small" type="primary" @click="submitUploadRupelian">开始导入</el-button>
      </template>
      <template v-else>
        <el-button type="primary" @click="downloadFile">下载模板</el-button>
        <el-button style="margin-top:20px;" size="small" type="primary" @click="submitUpload">开始导入</el-button>
      </template>
    </template>

  </div>

</template>

<script>
import store from "@/store";
import { importSpecimen, importRupelian, importChattian} from '@/api/common'
export default {
  name: "fileUpload",
  props:{
    fileInfoList:Array,
    isShowTip:{
      default:true,
      type:Boolean
    },
    isMultiple:{
      default: true,
      type:Boolean
    },
    importType: {
      default:'',
      type:String
    },
    limitNumber:{
      default:100,
      type:Number
    },
    exportFileName: {
      type: String
    }
  },
  data(){
    return{
      actionUrl:'',
      chooseFiles:[],
      uploaderFiles:[],
      exportFileNames: this.exportFileName + '模板',
      searchForm: {

      },

    }
  },
mounted() {
  // console.log(this.exportFileName)
},
  methods:{
    getInit() {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()

      })
    },

    handleChange(file){
      this.chooseFiles = file
    },
    handleMove(){
      this.chooseFiles = []
    },
    onSuccess(){
      this.$message({
        message: '上传成功',
        type: 'success'
      });
    },
    onError(){
      this.$message({
        message: '上传失败',
        type: 'error'
      });
    },

    //下载模板-采集地子表
    downloadFile() {
      this.downBlobFile('/admin/sys-file/local/file/fossiltaxa.xlsx', this.searchForm, 'fossiltaxa.xlsx')
    },
    //开始导入-采集地子表
    submitUpload(){
      const loading=this.$loading({
        lock: true,
        text: '数据正在导入，请勿关闭',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let formData = new FormData();
      formData.append("file",this.chooseFiles.raw)
      const token = store.getters.access_token
      importChattian(formData).then(res=>{
        if (res.data.code === 0 || res.data.code === 200){
          this.$message({
            type: 'success',
            message: '导入成功!'
          });
          loading.close()
          this.$refs.upload.clearFiles()
          this.$emit('exportSuccess')
        }else {
        }
      }).catch(err => {
        this.chooseFiles = []
        loading.close()
        this.$refs.upload.clearFiles()
        this.$emit('errorSuccess')
      })
    },
    //下载模板-标本信息表
    downloadFileSpecimen() {
      this.downBlobFile(`/admin/sys-file/local/file/xSpecimen.xlsx`, {}, "叶角质层数据库信息模版.xlsx");
    },
    //开始导入-标本信息表
    submitUploadSpecimen() {
      const loading=this.$loading({
        lock: true,
        text: '数据正在导入，请勿关闭',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let formData = new FormData();
      formData.append("file",this.chooseFiles.raw)
      const token = store.getters.access_token
      importSpecimen(formData).then(res=>{
        if (res.data.code === 0 || res.data.code === 200){
          this.$message({
            type: 'success',
            message: '导入成功!'
          });
          loading.close()
          this.$refs.upload.clearFiles()
          this.$emit('exportSuccess')
        }else {
        }
      }).catch(err => {
        this.chooseFiles = []
        loading.close()
        this.$refs.upload.clearFiles()
        this.$emit('errorSuccess')
      })
    },

    //下载模板-采集地管理
    downloadRupelian() {
      this.downBlobFile(`/admin/sys-file/local/file/fossilassemblage.xlsx`, {}, "fossilassemblage.xlsx");
    },
    //开始导入-采集地管理
    submitUploadRupelian() {
      const loading=this.$loading({
        lock: true,
        text: '数据正在导入，请勿关闭',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let formData = new FormData();
      formData.append("file",this.chooseFiles.raw)
      const token = store.getters.access_token
      importRupelian(formData).then(res=>{
        if (res.data.code === 0 || res.data.code === 200){
          this.$message({
            type: 'success',
            message: '导入成功!'
          });
          loading.close()
          this.$refs.upload.clearFiles()
          this.$emit('exportSuccess')
        }else {
        }
      }).catch(err => {
        this.chooseFiles = []
        loading.close()
        this.$refs.upload.clearFiles()
        this.$emit('errorSuccess')
      })
    },
  }
}
</script>

<style scoped>
::v-deep .el-upload,
::v-deep .el-upload-dragger {
  width: 100%;
}
</style>
