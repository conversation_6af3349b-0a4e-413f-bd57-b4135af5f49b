// Simple Node.js script to test the migrated API endpoints
// Run with: node test-endpoints.js
//
// Note: This tests the backend directly. For frontend testing,
// use the proxy URL: http://localhost:8181/admin/paleoecology

const axios = require('axios');

const BASE_URL = 'http://localhost:8888/admin/paleoecology';

// Test endpoints
const endpoints = [
    { name: 'Dating Methods', url: `${BASE_URL}/dating-methods` },
    { name: 'Dating Qualities', url: `${BASE_URL}/dating-qualities` },
    { name: 'Families', url: `${BASE_URL}/taxa/families` },
    { name: 'Genera', url: `${BASE_URL}/taxa/genera` },
    { name: 'Species', url: `${BASE_URL}/taxa/species` },
    { name: 'Scientific Names', url: `${BASE_URL}/taxa/scientific-names` },
    { name: 'Original Names', url: `${BASE_URL}/taxa/original-names` },
    { name: 'Fossil Types', url: `${BASE_URL}/fossil-types` },
    { name: 'Plant Organs', url: `${BASE_URL}/plant-organs` }
];

async function testEndpoint(endpoint) {
    try {
        console.log(`Testing ${endpoint.name}...`);
        const response = await axios.get(endpoint.url, { timeout: 5000 });
        
        if (response.status === 200) {
            const data = response.data;
            console.log(`✅ ${endpoint.name}: SUCCESS`);
            console.log(`   Status: ${response.status}`);
            console.log(`   Data type: ${typeof data}`);
            
            if (Array.isArray(data)) {
                console.log(`   Array length: ${data.length}`);
                if (data.length > 0) {
                    console.log(`   Sample data: ${data.slice(0, 3).join(', ')}`);
                }
            } else if (data && data.data && Array.isArray(data.data)) {
                console.log(`   Array length: ${data.data.length}`);
                if (data.data.length > 0) {
                    console.log(`   Sample data: ${data.data.slice(0, 3).join(', ')}`);
                }
            }
        } else {
            console.log(`❌ ${endpoint.name}: FAILED - Status ${response.status}`);
        }
    } catch (error) {
        console.log(`❌ ${endpoint.name}: ERROR`);
        if (error.code === 'ECONNREFUSED') {
            console.log(`   Connection refused - Is the server running on port 8888?`);
        } else if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Message: ${error.response.statusText}`);
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
    console.log('');
}

async function testDataEndpoint() {
    try {
        console.log('Testing Main Data Endpoint...');
        const response = await axios.get(`${BASE_URL}/data`, {
            params: {
                family: 'Fagaceae',
                southLatitude: -90,
                northLatitude: 90,
                westLongitude: -180,
                eastLongitude: 180
            },
            timeout: 10000
        });
        
        if (response.status === 200) {
            const data = response.data;
            console.log(`✅ Main Data Endpoint: SUCCESS`);
            console.log(`   Status: ${response.status}`);
            
            if (data && data.table1) {
                console.log(`   Results count: ${data.table1.length}`);
                if (data.table1.length > 0) {
                    const sample = data.table1[0];
                    console.log(`   Sample fields: ${Object.keys(sample).slice(0, 5).join(', ')}`);
                }
            }
        }
    } catch (error) {
        console.log(`❌ Main Data Endpoint: ERROR`);
        if (error.code === 'ECONNREFUSED') {
            console.log(`   Connection refused - Is the server running on port 8888?`);
        } else if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Message: ${error.response.statusText}`);
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
    console.log('');
}

async function testDetailEndpoint() {
    try {
        console.log('Testing Detail Endpoint...');
        const response = await axios.get(`${BASE_URL}/detail`, {
            params: { id: '1' },
            timeout: 5000
        });
        
        if (response.status === 200) {
            const data = response.data;
            console.log(`✅ Detail Endpoint: SUCCESS`);
            console.log(`   Status: ${response.status}`);
            
            if (data && data.detail) {
                console.log(`   Detail records: ${data.detail.length}`);
            }
        }
    } catch (error) {
        console.log(`❌ Detail Endpoint: ERROR`);
        if (error.code === 'ECONNREFUSED') {
            console.log(`   Connection refused - Is the server running on port 8888?`);
        } else if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Message: ${error.response.statusText}`);
        } else {
            console.log(`   Error: ${error.message}`);
        }
    }
    console.log('');
}

async function runTests() {
    console.log('='.repeat(60));
    console.log('PALEOECOLOGY API ENDPOINT TESTING');
    console.log('='.repeat(60));
    console.log('');
    
    // Test lookup endpoints
    console.log('Testing Lookup Endpoints:');
    console.log('-'.repeat(30));
    for (const endpoint of endpoints) {
        await testEndpoint(endpoint);
    }
    
    // Test main data endpoint
    console.log('Testing Main Data Endpoint:');
    console.log('-'.repeat(30));
    await testDataEndpoint();
    
    // Test detail endpoint
    console.log('Testing Detail Endpoint:');
    console.log('-'.repeat(30));
    await testDetailEndpoint();
    
    console.log('='.repeat(60));
    console.log('TESTING COMPLETE');
    console.log('='.repeat(60));
}

// Run the tests
runTests().catch(console.error);
