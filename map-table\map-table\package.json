{"name": "map-table", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.7.9", "crypto-js": "^3.1.9-1", "core-js": "^3.8.3", "echarts": "^5.6.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "video.js": "^8.6.1", "videojs-contrib-hls": "^5.15.0", "vue": "^2.6.11", "vue-i18n": "^8.28.2", "vue-quill-editor": "3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}