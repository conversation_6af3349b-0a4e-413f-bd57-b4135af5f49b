<template>
  <div class="leaf-cuticl">
    <div class="leaf-cuticl-sidebar" >
<!--      <el-tree-->
<!--          :data="treeData"-->
<!--          :props="defaultProps"-->
<!--          :lazy="true"-->
<!--          :load="loadNode"-->
<!--          @node-click="handleNodeClick">-->
<!--        <div slot-scope="{ node, data }" style="display:flex;justify-content: space-between;width: 100%;">-->
<!--          <div class="label">{{ node.label }}</div>-->
<!--          <div class="number" v-if="node.childNodes && node.childNodes.length > 0">-->
<!--            <span class="total">{{ node.childNodes.length }}</span>-->
<!--            <template>-->
<!--              <span v-if="node.expanded" @click="toggleNode(node, 'open')" class="el-icon-arrow-up icon"></span>-->
<!--              <span v-else @click="toggleNode(node, 'close')" class="el-icon-arrow-down icon"></span>-->
<!--            </template>-->
<!--          </div>-->
<!--        </div>-->
<!--      </el-tree>-->
      <el-tree :data="treeData"
               :props="defaultProps"
               @node-click="handleNodeClick">
        <div slot-scope="{ node, data }" style="display:flex;justify-content: space-between;width: 100%;">
          <div class="label">{{ node.label }}</div>
          <div class="number" v-if="node.childNodes && node.childNodes.length > 0">
            <span class="total">{{ data.childCount }}</span>
            <template>
              <span v-if="node.expanded" @click="toggleNode(node, 'open')" class="el-icon-arrow-up icon"></span>
              <span v-else @click="toggleNode(node, 'close')" class="el-icon-arrow-down icon"></span>
            </template>
          </div>
        </div>
      </el-tree>

      <!--
      <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick">
        <div slot-scope="{ node, data }" style="display:flex;justify-content: space-between;width: 100%;">
          <div class="label" >{{ node.label }}</div>
          <div class="number" v-if="data.children && data.children.length > 0">
            <span class="total"> {{data.children.length}}</span>
            <template>
              <span v-if="node.expanded" @click="toggleNode(node,'open')" class="el-icon-arrow-up icon"></span>
              <span v-else @click="toggleNode(node,'close')" class="el-icon-arrow-down icon"></span>
            </template>

          </div>
        </div>
      </el-tree>
      -->
    </div>
    <div class="leaf-cuticl-main">
      <el-form :model="searchForm"  ref="searchForm" size="small"
               label-position="top" class="demo-ruleForm" :inline="true" @keyup.enter.native="enterHandle">
        <el-form-item label="Family">
          <el-input  v-model="searchForm.familyEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="科名" prop="familyCn">
          <el-input  v-model="searchForm.familyCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="Genus" prop="genusEn">
          <el-input  v-model="searchForm.genusEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="属名" prop="genusCn">
          <el-input  v-model="searchForm.genusCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="Species" prop="speciesEn">
          <el-input  v-model="searchForm.speciesEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="种名" prop="speciesCn">
          <el-input  v-model="searchForm.speciesCn" clearable></el-input>
        </el-form-item>
        <el-form-item label="Place of Collection" prop="placeEn">
          <el-input  v-model="searchForm.placeEn" clearable></el-input>
        </el-form-item>
        <el-form-item label="采集地点" prop="collectionPlace">
          <el-input  v-model="searchForm.collectionPlace" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="leaf-main-box">
        <ul class="leaf-ul">
          <li class="leaf-item"  v-for="(leaf,index) in tableList" :key="leaf.id">
            <div class="leaf-item-pic" @click="goDetail(leaf.id)">
              <img
                  v-if="(leaf.lwSysFileList && leaf.lwSysFileList.length > 0) || (leaf.upSysFileList && leaf.upSysFileList.length > 0)"
                  class="pic"
                  :src="(leaf.lwSysFileList && leaf.lwSysFileList[0] && leaf.lwSysFileList[0].url) || (leaf.upSysFileList && leaf.upSysFileList[0] && leaf.upSysFileList[0].url)"
                  alt=""
              />
              <img v-if="(leaf.upSysFileList && leaf.upSysFileList.length > 0)"
                  class="pic"
                  :src="leaf.upSysFileList[0].url"
                  alt=""
              />
              <img v-else class="pic" src="../../assets/nothing.png" alt="" />
            </div>
            <div class="leaf-item-con">
              <div class="con-name">{{ leaf.speciesCn }}</div>
              <div class="con-name">{{leaf.speciesEn}}</div>
              <div class="con-latin family">{{ leaf.familyCn }}</div>
              <div class="con-latin family">{{ leaf.familyEn }}</div>
              <div class="con-latin">标本号/Specimen No.</div>
              <div class="con-latin">{{ leaf.specimenNo }}</div>
<!--              <div class="con-num">采集地/Place of Collection:{{ leaf.collectionPlace }} {{ leaf.placeEn }}</div>-->
            </div>
          </li>

        </ul>
        <div style="display:flex;align-items: center;justify-content: center">
          <span>共 {{ total }}条</span>
          <el-pagination  @size-change="sizeChangeHandle"
                          @current-change="currentChangeHandle"
              background
              layout="prev, pager, next"
              :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/*

叶角质层页面
 */
import {getSpecimenList, getXSpecimenTree} from "@/api/leafCuticl";

export default {
  name: "LeafCuticleIndex",
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'child',
        label: `nameCnNameEn`
      },
      searchForm: {},
      tableList: [],
      pageIndex: 1,
      pageSize: 6,
      total: 0
    }
  },
  mounted() {
    this.getDataList()
    this.getTreeList('root')
  },
  methods: {
    // 获取树数据
    async getTreeList(nameEn) {
      // 调用接口获取树数据
      const res = await getXSpecimenTree(nameEn); // 替换为实际的 API 调用
      if (res.data.code === 0) {
        this.treeData = res.data.data; // 假设返回的数据是树结构
      }
    },
    enterHandle() {
      this.pageIndex = 1
      this.getDataList()
    },
    getDataList() {
      getSpecimenList(Object.assign({
        current: this.pageIndex,
        size: this.pageSize,
        ...this.searchForm
        // languageType: this.language // 使用 Vuex 中的 language
      })).then(res => {
        if(res.data.code === 0) {
          this.tableList = res.data.data.records
          this.total = res.data.data.total
        }
      })
    },

    /*async loadNode(node, resolve) {
      // 重置搜索表单
      this.resetSearchForm();
      // 根据节点的层级设置搜索条件
      this.setSearchFormByNode(node);
      try {
        if (node.level === 0) {
          // 加载子节点数据
          await this.getTreeList('root');
          await this.getDataList();
        }else if ( node.level === 1) {
          // 加载子节点数据
          await this.getTreeList(node.data.nameEn);
          await this.getDataList();
        } else {
          // 没有更多子节点，返回空数组
          this.$message.warning('没有更多子节点了');
          resolve([]);
        }
      } catch (error) {
        this.$message.error('加载节点失败，请稍后重试');
        resolve([]);
      }
    },*/

// 根据节点设置搜索条件
    setSearchFormByNode(node) {
      // const { belongLevel, nameEn } = node.data;
      const { belongLevel, nameEn } = node;

      // 清空搜索表单
      this.searchForm = {};

      // 根据节点的层级设置搜索条件
      if (belongLevel === '科') {
        this.searchForm.familyEn = nameEn;
      } else if (belongLevel === '属') {
        this.searchForm.genusEn = nameEn;
      } else if (belongLevel === '种') {
        this.searchForm.speciesEn = nameEn;
      }
    },

    resetSearchForm() {
      this.searchForm = {
        familyEn: '',
        familyCn: '',
        genusEn: '',
        genusCn: '',
        speciesEn: '',
        speciesCn: '',
        placeEn: '',
        collectionPlace: '',
      };
    },

    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    handleNodeClick(data) {
      this.pageIndex = 1
      this.setSearchFormByNode(data)
      this.getDataList()
    },
    toggleNode(node,type) {
      if(type === 'open') {
        node.expanded = true
      }else {
        node.expanded = false
      }
    },
    //详情
    goDetail(id) {
      this.$router.push(`/leafCuticleDetail/${id}`)
    },
    submitForm() {
      this.pageIndex = 1
      this.getDataList()
    },
    //重置
    resetForm() {
      this.searchForm = {}
      this.resetSearchForm()
      this.getDataList()
      this.getTreeList('root')
    },
  }
}
</script>
<style>
::v-deep .el-button--primary {
  background-color: var(--primary-text-color);
}
::v-deep .el-button--primary {
  border-color: var(--primary-text-color);
}
.el-tree-node__children {
  margin-left: 10px !important;
}
</style>
<style scoped lang="scss">
.leaf-cuticl {
  min-height: calc(100vh - 272px - 280px);
  background-color: var(--about-background-color);
  text-align: left;
  padding: 50px 80px;
  display: flex;
  .leaf-cuticl-sidebar {
    width: 300px;
    margin-right: 20px;
    background-color: #fff;
    padding: 8px 16px;
   overflow-y: scroll;
    height: 1100px;
    ::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
      display: none !important;
    }
    ::v-deep .el-tree-node__content {
      padding-left: 0 !important;
      height: 40px;
      line-height: 40px;
    }
    .label {
      font-size: 15px;
      color: #333333;
    }
    .total {
      font-size: 14px;
      color: var(--primary-text-color);
      margin-right: 5px;
    }
    .icon {
      font-size: 14px;
      color: var(--primary-text-color);

    }
  }
  .leaf-cuticl-main {
    width: calc(100%  - 300px - 20px);
    background-color: #fff;
    padding: 20px;
    .demo-ruleForm {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .el-form-item {
        width: calc((100% - 60px) / 4);
        margin-right: 20px;
        margin-bottom: 10px ;
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
      ::v-deep .el-form-item__label {
        padding: 0 !important;
      }
    }
    .leaf-main-box {
      .leaf-ul {
        display: flex;
        flex-wrap: wrap;
        .leaf-item {
          width: calc((100% - 20px) / 2);
          margin-right: 20px;
          margin-bottom: 20px;
          //background-color: lightcoral;
          height: 220px;
          padding: 20px 0;
          border-bottom: 1px solid #DDDDDD;
          display: flex;
          &:nth-child(2n) {
            margin-right: 0;
          }
          .leaf-item-pic {
            width: 270px;
            height: 100%;
            margin-right: 10px;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            .pic {
              width: 100%;
              height: 100%;
              border-radius: 4px;
              object-fit: cover;
              -webkit-transition: all 0.3s linear;
            }
            &:hover .pic {
              -webkit-transform: scale(1.05);
            }
          }
          .leaf-item-con {
            width: calc(100% - 270px);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .con-name {
              font-size: 20px;
              color: var(--primary-text-color);
            }
            .con-latin{
              font-size: 16px;
              color: #333;
            }
            .family {
              color:var(--primary-text-color);
            }
            .con-num{
              font-size: 16px;
              color: #333;
            }
            .con-address{
              font-size: 16px;
              color: #333;
            }
            .con-time{
              font-size: 16px;
              color: #333;
            }
          }
        }
      }
    }
  }
}
</style>