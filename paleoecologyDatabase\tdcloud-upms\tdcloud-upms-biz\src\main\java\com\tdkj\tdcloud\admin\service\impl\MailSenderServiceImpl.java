package com.tdkj.tdcloud.admin.service.impl;

import com.tdkj.tdcloud.admin.api.entity.EmailSender;
import com.tdkj.tdcloud.admin.api.entity.SysUser;
import com.tdkj.tdcloud.admin.mapper.SysUserMapper;
import com.tdkj.tdcloud.admin.service.MailSenderService;
import com.tdkj.tdcloud.common.core.util.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 实现qq邮箱发送
 */
@Service
public class MailSenderServiceImpl implements MailSenderService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Resource
	private JavaMailSenderImpl sender;

	@Value("${spring.mail.username}")
	private String from;

	private static Properties pro;

	@Resource
	private RedisTemplate redisTemplate;

	@Resource
	private SysUserMapper sysUserMapper;


	@Override
	public R sendSimpleMail(EmailSender emailSender) {
//		sender.setJavaMailProperties(pro);

		MimeMessage message = sender.createMimeMessage();

		try {
			MimeMessageHelper helper = new MimeMessageHelper(message, true);
			helper.setFrom(from); // 发送人
			helper.setTo(emailSender.getToEmail()); // 收件人

			// TODO: 使用安全的随机数生成器生成验证码
			Random rand = new Random();
			int num = rand.nextInt(900000) + 100000;
			Date currentDate = new Date();
			SimpleDateFormat y = new SimpleDateFormat("yyyy");
			SimpleDateFormat m = new SimpleDateFormat("MM");
			SimpleDateFormat d = new SimpleDateFormat("dd");
			if ("register".equals(emailSender.getEmailType())){
				helper.setSubject("【Registered Account】"); // 标题
				int i1 = sysUserMapper.getUserVoByEmail(emailSender.getToEmail());
				if (i1==1){
					return R.failed("邮箱已被注册");
				}
				String textContent = "<p>Dear Dr. " + emailSender.getName() + "</p>" +
						"<p>Your registration verification code for the Paleoecology Database is:</p>" +
						"<p><strong>%s</strong> (Valid for 15 minutes)</p>" +
						"<p>Critical Guidelines:</p>" +
						"<p>Email Authentication</p>" +
						"<p>Registration requires an official university/enterprise email address (e.g., @cam.ac.uk). Public domains (Gmail, QQ etc.) are prohibited to ensure data security compliance.</p>" +
						"<p>Account Security Protocol</p>" +
						"<p>This account is non-transferable under institutional cybersecurity policies.</p>" +
						"<p>Monitor login activity via the dashboard. Immediately reset credentials if:</p>" +
						"<p>Unrecognized IP addresses are detected</p>" +
						"<p>Session durations exceed 30 minutes of inactivity.</p>" +
						"<p>Legal & Ethical Obligations</p>" +
						"<p>Unauthorized data sharing, password disclosure, or API misuse will trigger automated system audits and may result in legal accountability.</p>" +
						"<p>The datasets in this database were obtained from publicly available academic repositories. Data are solely for scientific research purposes. If any potential copyright infringement is identified, please contact the corresponding author immediately.</p>" +
						"<p>Technical Support</p>" +
						"<p>For urgent assistance, contact Prof. Shufeng Li or Dr. Jian Huang:</p>" +
						"<p>email: <EMAIL>; <EMAIL></p>" +
						"<p>System Autonotification</p>" +
						"<p>This message contains confidential data. Forwarding or replication is strictly prohibited.</p>" +
						"<p>" + y.format(currentDate) + " 年 " + m.format(currentDate) + " 月 " + d.format(currentDate) + " 日</p>";

				// 确保第二个参数为 true，以表示这是 HTML 内容
				helper.setText(String.format(textContent, num), true);
				redisTemplate.opsForValue().set(emailSender.getToEmail() + emailSender.getEmailType(), String.valueOf(num), 900, TimeUnit.SECONDS);//随机验证码

			}

			if ("forgetPassword".equals(emailSender.getEmailType())){
				helper.setSubject("【Confirmation of Your Password Change Request】"); // 标题
				SysUser sysUserByEmail = sysUserMapper.getSysUserByEmail(emailSender.getToEmail());
				if (sysUserByEmail==null){
					return R.failed("邮箱不存在");
				}
				String textContent = "<p>Dear " + sysUserByEmail.getName() + "</p>" +
						"<p>Your password change code for the Paleoecology Database is:</p>" +
						"<p><strong>%s</strong> (Valid for 15 minutes)</p>" +
						"<p>Security Tips</p>" +
						"<p>Please ensure that your new password is strong and unique. A strong password should be at least 8 characters long, including a combination of uppercase and lowercase letters, numbers, and special symbols.</p>" +
						"<p>Avoid using easily guessable information such as your name, birthdate, or common words as your password.</p>" +
						"<p>If you did not initiate this password change request, please contact our support team immediately at Prof. Shufeng Li ( <EMAIL>) or Dr. Jianhuang (<EMAIL>). We will assist you in securing your account.</p>" +
						"<p>Thank you for your patience and understanding.</p>" +
						"<p>Best regards,</p>" +
						"<p>Paleoecoloy Database Team</p>" +
						"<p>" + y.format(currentDate) + " 年 " + m.format(currentDate) + " 月 " + d.format(currentDate) + " 日</p>";

				helper.setText(String.format(textContent, num), true);
				redisTemplate.opsForValue().set(emailSender.getToEmail() + emailSender.getEmailType(), String.valueOf(num), 900, TimeUnit.SECONDS);//随机验证码

			}

			sender.send(message);
			logger.info("邮件已经发送。");

		} catch (MessagingException e) {
			logger.error("发送邮件时发生异常！", e);
			e.printStackTrace();
		} catch (Exception e) {
			logger.error("发送简单邮件时发生异常！", e);
			e.printStackTrace();
		}
		return R.ok("发送成功");
	}

}

