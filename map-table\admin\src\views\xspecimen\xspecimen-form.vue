<template>
  <el-dialog
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
              label-position="top">
    <el-form-item label="标本号/Specimen No." prop="specimenNo">
      <el-input v-model="dataForm.specimenNo" placeholder="标本编号"></el-input>
    </el-form-item>
      <el-form-item label="物种中文名" prop="speciesCn">
        <el-input v-model="dataForm.speciesCn" placeholder="物种中文名"></el-input>
      </el-form-item>
    <el-form-item label="species" prop="speciesEn">
      <el-input v-model="dataForm.speciesEn" placeholder="物种名称"></el-input>
    </el-form-item>
      <el-form-item label="属名" prop="genusCn">
        <el-input v-model="dataForm.genusCn" placeholder="属名"></el-input>
      </el-form-item>
    <el-form-item label="Genus" prop="genusEn">
      <el-input v-model="dataForm.genusEn" placeholder="Genus"></el-input>
    </el-form-item>
      <el-form-item label="科名" prop="familyCn">
        <el-input v-model="dataForm.familyCn" placeholder="科名"></el-input>
      </el-form-item>
    <el-form-item label="Family" prop="familyEn">
      <el-input v-model="dataForm.familyEn" placeholder="Family"></el-input>
    </el-form-item>
    <el-form-item label="采集地点" prop="collectionPlace">
      <el-input v-model="dataForm.collectionPlace" placeholder="标本的采集地点"></el-input>
    </el-form-item>
    <el-form-item label="Place of Collection" prop="placeEn">
      <el-input v-model="dataForm.placeEn" placeholder="Place of Collection"></el-input>
    </el-form-item>
    <el-form-item label="采集日期/Collect Date" prop="collectionTime">
      <el-input v-model="dataForm.collectionTime" placeholder="采集日期/Collect Date"></el-input>
    </el-form-item>
    <el-form-item label="采集人" prop="collector">
      <el-input v-model="dataForm.collector" placeholder="采集人"></el-input>
    </el-form-item>
      <el-form-item label="Collector" prop="collectorEn">
        <el-input v-model="dataForm.collectorEn" placeholder="Collector"></el-input>
      </el-form-item>
    <el-form-item label="数据库导入批次" prop="importBatch">
      <el-input v-model="dataForm.importBatch" placeholder="数据库导入批次"></el-input>
    </el-form-item>
      <el-form-item label="语言类型" prop="languageType">
        <el-select v-model="dataForm.languageType" placeholder="语言类型" style="width: 100%;">
          <el-option label="中文" value="zh"></el-option>
          <el-option label="英文" value="en"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/xspecimen/xspecimen'

  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        dataForm: {
          id: 0,
          specimenNo: '',
          chineseName: '',
          speciesEn: '',
          speciesCn: '',
          genusEn: '',
          genusCn: '',
          familyEn: '',
          familyCn: '',
          collectionPlace: '',
          placeEn: '',
          collectionTime: '',
          collector: '',
          importBatch: '',
          languageType: '',
        },
        dataRule: {
                specimenNo: [
                {required: false, message: '标本编号', trigger: 'blur'}
              ],
              //   chineseName: [
              //   {required: true, message: '标本对应的中文名称不能为空', trigger: 'blur'}
              // ],
                speciesEn: [
                {required: false, message: '物种名称不能为空', trigger: 'blur'}
              ],
                speciesCn: [
                {required: false, message: '物种中文名称不能为空', trigger: 'blur'}
              ],
                genusEn: [
                {required: false, message: 'Genus不能为空', trigger: 'blur'}
              ],
                genusCn: [
                {required: false, message: '属名不能为空', trigger: 'blur'}
              ],
                familyEn: [
                {required: false, message: 'Family不能为空', trigger: 'blur'}
              ],
                familyCn: [
                {required: false, message: '科名不能为空', trigger: 'blur'}
              ],
                collectionPlace: [
                {required: false, message: '采集地点不能为空', trigger: 'blur'}
              ],
                placeEn: [
                {required: false, message: 'Place of Collection不能为空', trigger: 'blur'}
              ],
                collectionTime: [
                {required: false, message: '采集日期/Collect Date不能为空', trigger: 'blur'}
              ],
                collector: [
                {required: false, message: '采集人不能为空', trigger: 'blur'}
              ],
                importBatch: [
                {required: false, message: '数据库导入批次不能为空', trigger: 'blur'}
              ],
                languageType: [
                {required: true, message: '语言类型不能为空', trigger: 'change'}
              ],
        }
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
