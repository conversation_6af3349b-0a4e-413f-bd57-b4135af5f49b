<template>
  <footer class="footer">
    <div class="footer-top">
      <div class="footer-top-left">
        <img class="footer-top-left-img" src="../assets/logo.png" alt="">
        <div class="footer-top-left-name">{{ $t('nav.paleoecologyNav') }}</div>
      </div>
      <div class="footer-top-right">
        <div class="footer-top-right-con">{{ $t('footer.addressFooter') }}：{{ $t('footer.addressText') }}</div>
        <div class="footer-top-right-con">{{ $t('footer.telFooter') }}：{{ $t('footer.telText') }}</div>
        <div class="footer-top-right-con">{{ $t('footer.emailFooter') }}：{{ $t('footer.emailText')  }}</div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-bottom-con">{{ $t('footer.copyrightNotice') }}</div>
      <div class="footer-bottom-con">{{ $t('footer.visitsFooter') }}：{{ visits }}</div>
    </div>
  </footer>
</template>

<script>
import { getSpecimenVisits} from "@/api/home";

export default {
  name: "FooterPanle",
  data() {
    return {
      visits: 0
    }
  },
  mounted() {
    this.getSpecimenVisits()
  },
  methods:{
    //获取访问量功能
    async getSpecimenVisits() {
      let res = await getSpecimenVisits()
      if(res.data.code === 0) {
        const formatted = res.data.data.toLocaleString('en-IN'); // 印度格式使用逗号分隔
        this.visits = formatted
      }else {
        this.$message.warning(res.data.msg)
      }
    }
  }
}
</script>

<style scoped>
.footer {
  height: 272px;
  width: 100%;
  background-color: var(--footer-background-color);
  color: var(--light-text-color);
}
.footer-top {
  width: 100%;
  height: 169px;
  border-bottom: 1px solid #FFFFFF;
}
.footer-top-left {
  padding-left: 80px;
  float: left;
  height: 100%;
  display: flex;
  align-items: center;
}
.footer-top-left-img {
  display: inline-block;
  width: 70px;
  height: 70px;
}
.footer-top-left-name {
  font-size: 42px;
  margin-left: 20px;
}
.footer-top-right {
  width: 400px;
  height: 90px;
  padding-right: 80px;
  float: right;
  text-align: left;
  margin-top: calc((169px - 80px) / 2);
  font-size: 16px;
}
.footer-top-right-con {
  margin-bottom: 10px;
}
.footer-bottom {
  height: 103px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.footer-bottom-con {
  font-size: 16px;
  margin-bottom: 10px;
}
</style>