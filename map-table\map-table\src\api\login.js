import qs from 'qs'
import {request } from '@/api/request'
import website from "@/util/website";
import {getStore, setStore} from "@/util/store";
import store from "@/store";
import {validatenull} from "@/util/validate";
//发送邮箱
export function getEmailCode(toEmail,name,emailType) {
    return request({
        url: '/admin/mailSender/getEmailCode',
        method: 'post',
        data: {toEmail,name,emailType},
    })
}

//用户注册
export function userRegister(data) {
    return request({
        url: '/admin/user/userRegister',
        method: 'post',
        data: data,

    })
}

const scope = 'server'
export const loginByUsername = (username, password, randomStr) => {
    let grant_type = 'password'
    let dataObj = qs.stringify({'username': username, 'password': password})
    let basicAuth = 'Basic ' + window.btoa(website.formLoginClient)
    // 保存当前选中的 basic 认证信息
    setStore({
        name: 'basicAuth',
        content: basicAuth,
        type: 'session'
    })

    return request({
        url: '/admin/oauth2/token',
        headers: {
            isToken: false,
            'TENANT-ID': '1',
            'Authorization': basicAuth
        },
        method: 'post',
        params: {randomStr, grant_type, scope},
        data: dataObj
    })
}

export const refreshToken = (refresh_token) => {
    const grant_type = 'refresh_token'
    // 获取当前选中的 basic 认证信息
    let basicAuth = getStore({name: 'basicAuth'})

    return request({
        url: '/admin/oauth2/token',
        headers: {
            'isToken': false,
            'TENANT-ID': '1',
            'Authorization': basicAuth
        },
        method: 'post',
        params: {refresh_token, grant_type, scope}
    })
}
//获取用户信息
export const getUserInfo = () => {
    return request({
        url: '/admin/user/info',
        method: 'get',
        headers: {
            isToken: true,
        }
    })
}
//退出登录
export const logout = () => {
    return request({
        url: '/auth/token/logout',
        method: 'delete',
        headers: {
            isToken: true,
        }
    })
}

//修改密码{username， code ，password，repassword}
export function updateUserPassword(data) {
    return request({
        url: '/admin/user/updateUserPassword',
        method: 'post',
        data: data,
    })
}

/**
 * 校验令牌，若有效期小于半小时自动续期
 * @param refreshLock
 */
export const checkToken = (refreshTime, refreshLock, $store) => {
    const token = store.state.access_token
    // 获取当前选中的 basic 认证信息
    const basicAuth = getStore({ name: 'basicAuth' })

    request({
        url: '/admin/token/check_token',
        headers: {
            isToken: false,
            Authorization: basicAuth
        },
        method: 'get',
        params: { token }
    })
        .then((response) => {
            if (validatenull(response)) {
                clearInterval(refreshTime)
                return
            }

            const expire = response && response.data && response.data.exp
            if (expire) {
                const expiredPeriod = expire * 1000 - new Date().getTime()
                //小于半小时自动续约
                if (expiredPeriod <= 10 * 60 * 1000) {
                    if (!refreshLock) {
                        refreshLock = true
                        $store.dispatch('RefreshToken').catch(() => {
                            clearInterval(refreshTime)
                        })
                        refreshLock = false
                    }
                }
            }
        })
        .catch((error) => {
            console.error(error)
        })
}

//接口文档
export function getInterFaceList(query) {
    return request({
        url: '/admin/xinterface/page',
        method: 'get',
        params: query
    })
}
// 更改个人信息--修改密码
export function editInfo(obj) {
    return request({
        url: '/admin/user/edit',
        method: 'put',
        data: obj
    })
}