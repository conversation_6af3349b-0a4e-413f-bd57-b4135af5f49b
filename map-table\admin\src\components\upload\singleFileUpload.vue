
<template>
  <el-upload
    class="upload-demo"
    :action="actionUrl"
    :on-change="handleChange"
    :file-list="fileList"
    :before-remove="removeHandle"
    :headers="headers"
    :auto-upload="true"
    :before-upload="beforeUploadSmallTwoImg"
    :show-file-list="true"
    accept=".jpg,.png,.jpeg,.gif"
    :limit="1"
    :http-request="requestHandle">
    <el-button size="small" type="primary" style="font-size: 14px">
      <span>选择图片</span>
    </el-button>
    <div slot="tip" class="el-upload__tip">只能上传图片，且大小不能超过200MB</div>
  </el-upload>
</template>

<script>
import { editorUpload } from '@/api/common'
import { delObj } from "@/api/admin/sys-file";

export default {
  name: "singleFileUpload",
  props: {
    file: Array
  },
  data() {
    return {
      actionUrl: '/admin/sys-file/upload',
      headers: {},
      fileList: [...this.file], // 使用展开运算符创建 file 的副本
    }
  },
  watch: {
    file: {
      handler(newVal) {
        this.fileList = [...newVal]; // 监听 prop 的变化，以更新本地的 fileList
      },
      immediate: true,
    },
  },
  methods: {
    handleChange(file, fileList) {
      this.fileList = fileList; // 更新本地的 fileList
      this.$emit('update:file', fileList); // 通过 emit 更新父组件的 file prop
    },
    beforeUploadSmallTwoImg(file) {
      if (file.size > 200 * 1024 * 1024) {
        this.$message.error('图片大小不能超过 200MB');
        return false;
      }
      const isJpgPng = file.type === "image/jpeg" || file.type === "image/png";
      if (!isJpgPng) {
        this.$message.error("文件格式不正确，只能上传JPG/PNG 格式!");
        return false;
      }
    },
    removeHandle(file,fileList) {
      if (file.id) {
        delObj(file.id).then(res => {
          this.$message.success('移除成功');
          this.$emit('successUpload', null);
        });
      }else {
        // 从本地 fileList 中移除文件
        this.fileList = this.fileList.filter(item => item.uid !== file.uid);
        this.$emit('successUpload', null); // 更新 parent component
      }
    },
    async requestHandle(upload) {
      let loading = this.$loading({
        lock:true,
        text:'图片上传中，请稍等',
        background:'rgba(0,0,0,0.4)'
      })
      let formData = new FormData();
      formData.append('file', upload.file);
      let res = await editorUpload(formData);
      if (res.data.code === 0) {
        this.$message.success('上传成功');
        this.$emit('successUpload', res);
        loading.close()
      }
    },
  }
}
</script>

<style scoped>

</style>
