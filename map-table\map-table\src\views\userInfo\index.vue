<template>
  <div class="user">
    <div class="user-main">
      <template>
        <div class="user-main-box" v-if="currentIndex === 1">
          <div class="user-avatar-box"><span class="iconfont icon-wode"></span></div>
          <el-form ref="dataForm" class="dataForm" :model="dataForm">
            <div class="form_item">
              <div class="label">{{ $t('login.emailPlaceholder') }}</div>
              <el-form-item prop="username" >
                <el-input :disabled="true" v-model="dataForm.username" :placeholder="$t('login.emailPlaceholder')"></el-input>
              </el-form-item>
            </div>
            <div class="form_item">
              <div class="label">{{ $t('login.namePlaceholder') }}</div>
              <el-form-item prop="name" >
                <el-input :disabled="true" v-model="dataForm.name" :placeholder="$t('login.namePlaceholder')"></el-input>
              </el-form-item>
            </div>
            <div class="form_item">
              <div class="label">{{ $t('login.utilPlaceholder') }}</div>

              <el-form-item prop="unit" >
                <el-input :disabled="true" v-model="dataForm.unit" :placeholder="$t('login.utilPlaceholder')"></el-input>
              </el-form-item>
            </div>
            <div class="form_item">
              <div class="label">{{ $t('login.phonePlaceholder') }}</div>

              <el-form-item prop="phone" >
                <el-input :disabled="true" v-model="dataForm.phone" :placeholder="$t('login.phonePlaceholder')"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="user-main-box" v-if="currentIndex === 2">
          <el-form ref="updateForm" class="updateForm" :model="updateForm" :rules="updateRules">
            <div class="form_item">
              <div class="label">{{ $t('login.oldPassPlaceholder') }}</div>
              <el-form-item prop="password">
                <el-input
                    auto-complete="off"
                    v-model="updateForm.password"
                    :type="passwordType"
                    :placeholder="$t('login.oldPassPlaceholder') ">
                  <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
                  <i slot="prefix" class="icon-mima icon"></i>
                </el-input>
              </el-form-item>
            </div>
            <div class="form_item">
              <div class="label">{{ $t('login.newPassPlaceholder') }}</div>
              <el-form-item prop="newpassword1">
                <el-input
                    auto-complete="off"
                    v-model="updateForm.newpassword1"
                    :type="passwordType"
                    :placeholder="$t('login.newPassPlaceholder') ">
                  <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
                  <i slot="prefix" class="icon-mima icon"></i>
                </el-input>
              </el-form-item>
            </div>
            <div class="form_item">
              <div class="label">{{ $t('login.rePassPlaceholder') }}</div>
              <el-form-item prop="newpassword2">
                <el-input
                    auto-complete="off"
                    v-model="updateForm.newpassword2"
                    :type="passwordType"
                    :placeholder="$t('login.rePassPlaceholder')">
                  <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
                  <i slot="prefix" class="icon-mima icon"></i>
                </el-input>
              </el-form-item>
            </div>
            <div class="form_item_btn">
              <el-button class="btn" type="primary" @click="submitHandle">{{ $t('login.saveBtn') }}</el-button>
            </div>
          </el-form>
        </div>
        <div class="user-main-box" v-if="currentIndex === 3">
          <div class="interface-box">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item :title="inter.title" :name="index + 1 + ''" v-for="(inter, index) in interfaceData" :key="inter.id">
                <div v-html="inter.content" class="detail-content ql-container ql-editor"></div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </template>
      <div class="user-main-slider">
        <ul class="slider-ul">
          <li class="first-item">{{ $t('login.userInfo') }}</li>
          <li :class="['slider-item', currentIndex == 1 ? 'active' : '']" @click="changeItem(1)">{{ $t('login.info') }}</li>
          <li :class="['slider-item', currentIndex == 2 ? 'active' : '']"  @click="changeItem(2)">{{ $t('login.updatePass') }}</li>
          <li :class="['slider-item', currentIndex == 3 ? 'active' : '']" @click="changeItem(3)">{{ $t('login.interface') }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import {rule} from "@/util/validateRules";
import {getInterFaceList, editInfo} from "@/api/login";
import {mapState} from "vuex";
import webiste from "@/util/website";
import {aesEncrypt} from "@/util/encryption";

export default {
  name: "UserInfo",
  data() {
    return {
      activeNames: ['1'],
      currentIndex :1,
      dataForm: {},
      passwordType: "password",
      updateForm: {
        username: "",
        password: "", //原密码
        newpassword1: "", //新密码
        newpassword2: "",//确认密码
        avatar: "",
        phone: "",
        nickname:"",
        name:"",
        email:"",
        // password: '',
        // newPassword: '',
        // rePassword: ''
      },
      updateRules: {
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {validator: rule.validatorPassword,trigger: 'blur'}
        ],
        newpassword1: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          {validator: rule.validatorPassword,trigger: 'blur'}
        ],
        newpassword2: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value !== this.updateForm.newpassword1) {
                callback(new Error('两次输入的密码不一致!'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'] // 在输入和字段变化时都触发验证
          }
        ]
      },
      interfaceData: []
    }
  },
  mounted() {
    this.getUserObj()
  },
  computed: {
    ...mapState({
      language: state => state.language // 将language从store映射到计算属性
    })
  },
  watch: {
    language() {
      this.getInterFaceList(); // 语言变化时重新获取内容
    }
  },
  methods: {
    getInterFaceList(){
      getInterFaceList(Object.assign({
        languageType: this.language // 使用 Vuex 中的 language
      })).then(res =>{
        if(res.data.code === 0) {
          // console.log(res)
          this.interfaceData = res.data.data.records
        }else {
          this.$message.error('请求失败')
        }
      })
    },
    handleChange(val) {
      // console.log(val);

    },
    getUserObj() {
      // let userInfo = await this.$store.dispatch('GetUserInfo');
      this.dataForm = this.$store.state.userInfo
    },
    getUpdateForm() {
      this.updateForm.username = this.$store.state.userInfo.username
      this.updateForm.avatar = this.$store.state.userInfo.avatar
      this.updateForm.phone = this.$store.state.userInfo.phone
      this.updateForm.nickname = this.$store.state.userInfo.nickname
      this.updateForm.name = this.$store.state.userInfo.name
      this.updateForm.email = this.$store.state.userInfo.email

    },
    changeItem(index) {
      this.currentIndex = index
      if(this.currentIndex === 1) this.getUserObj()
      if(this.currentIndex === 2) this.getUpdateForm()
      if(this.currentIndex === 3) this.getInterFaceList()
    },
    showPassword() {
      this.passwordType === ""
          ? (this.passwordType = "password")
          : (this.passwordType = "");
    },
    submitHandle() {
      this.$refs['updateForm'].validate((valid) => {
        if (valid) {
          //todo 调用修改密码接口
          editInfo(this.updateForm).then((response) => {
            if(response.data.code === 0) {
              this.$notify.success("修改成功");
              // 修改后注销当前token,重新登录
              this.$store.dispatch("LogOut").then(() => {
                // location.reload();
                this.$router.push('/')
              });
            }else {
              this.$notify.error(response.data.msg);
            }

          });
        } else {

          return false;
        }
      });
    },
  }
}
</script>

<style scoped lang="scss">
.user{
  margin-top: 100px;
  text-align: left;
  min-height: calc(100vh - 100px - 272px);
  background-color: #F2EEE5;
  padding: 50px 80px;
  .user-main {
    width: 100%;
    display: flex;
    .user-main-box {
      width: calc(100vw - 338px - 160px);
      background-color: #fff;
      .user-avatar-box {
        width: 80px;
        height: 80px;
        border: 1px solid var(--primary-text-color);
        border-radius: 50%;
        text-align: center;
        line-height: 80px;
        margin: 40px auto 20px;
        .iconfont {
          font-size: 36px;
        }
      }
      .dataForm ,.updateForm {
        padding: 0 175px;
        display: flex;
        flex-wrap: wrap;
        .form_item {
          .label {
            margin-bottom: 8px;
            font-size: 16px;
            color: var(--default-text-color);
          }
          margin-bottom: 20px;
          width: calc((100% - 40px) / 2);
          margin-right: 40px;
          &:nth-child(2n) {
            margin-right: 0;
          }
        }
      }
      .updateForm {
        .form_item {
          width: 100%;
          margin: 0 auto;
          .label {
            margin-bottom: 8px;
            font-size: 16px;
            color: var(--default-text-color);
          }
          &:first-child {
            margin-top: 16px;
          }
        }
        .form_item_btn {
          width: 100%;
          text-align: center;
          .el-button {
            margin: 0 auto 16px;
            text-align: center;
          }
        }

      }
      .interface-box {
        padding: 16px;
      }

    }
    .user-main-slider {
      width: 338px;
      .slider-ul {
        margin-left: 60px;
        li {
          height: 60px;
          line-height: 60px;
          border-bottom: 1px solid #DBDBDB;
          padding-left: 10px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 500;
          font-size: 18px;
          color: var(--primary-text-color);
          cursor: pointer;
        }
        .first-item {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 24px;
          color: var(--primary-text-color);
        }
        .active {
          background-color: #fff;
        }
      }
    }
  }
}
::v-deep .el-input__inner {
  padding: 0 15px !important;
}
::v-deep .el-collapse-item__header {
  font-size: 16px;
}
</style>