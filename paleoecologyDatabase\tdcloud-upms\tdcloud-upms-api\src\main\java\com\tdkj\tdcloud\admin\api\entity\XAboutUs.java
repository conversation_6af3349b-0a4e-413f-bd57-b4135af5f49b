/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 关于我们
 *
 * <AUTHOR> code generator
 * @date 2025-01-03 14:40:32
 */
@Data
@TableName("x_about_us")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "关于我们")
public class XAboutUs extends Model<XAboutUs> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
//    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Integer id;

    /**
     * 语言类型
     */
    @Schema(description="语言类型")
    private String languageType;

    /**
     * overview概况contactUs联系我们
     */
    @Schema(description="overview概况contactUs联系我们")
    private String type;

    /**
     * 内容
     */
    @Schema(description="内容")
    private String content;

    /**
     * 联系电话
     */
    @Schema(description="联系电话")
    private String phone;

    /**
     * 地址
     */
    @Schema(description="地址")
    private String address;

    /**
     * 邮政编码
     */
    @Schema(description="邮政编码")
    private String postalCode;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 图片名字
     */
    @Schema(description="图片名字")
    private String name;

    /**
     * 图片地址
     */
    @Schema(description="图片地址")
    private String url;
	@TableField(exist = false)
	private SysFile sysFile;

}
