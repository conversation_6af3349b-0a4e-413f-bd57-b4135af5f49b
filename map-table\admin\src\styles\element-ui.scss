.el-menu--popup{
  margin-left: -10px;
  background-color: #fff ;
  .el-menu-item{
    background-color: rgb(32,34,42) ;
    span,i {
      color:rgb(184,182,187) ;
    }
    &.is-active{
      background-color:rgba(0,0,0,1) ;
      span,i {
        color:#fff ;
      }
      &:hover{
        background-color:rgba(0,0,0,1) ;
      }
    }
    &:hover{
      background-color: rgb(32,34,42) ;
      i,span{
        color:#fff ;
      }
    }
  }
}

.el-dropdown-menu__item {
  font-size: 12px !important;
  line-height: 28px !important;
}

.el-card.is-always-shadow {
  box-shadow: none;
  border: none !important;
}

.el-scrollbar__view {
  height: 100%;
}


.el-menu--horizontal {
  border-bottom: none !important;
}

.el-menu {
  border-right: none !important;
}

.el-menu--display,
.el-menu--display + .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown-menu__item--divided:before,
.el-menu,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent;
}

.el-message__icon,
.el-message__content {
  display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  height: auto;
  overflow: hidden;
}

.el-dialog__wrapper {
  z-index: 2048;
}


.el-col {
  margin-bottom: 8px;
}

.el-main {
  padding: 0 !important;
}

.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent !important;
}

.el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered{
  margin-left: 5px;
}


