/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Paleoecology Data Query DTO
 * Maps to Python Flask server parameters for data filtering
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@Schema(description = "Paleoecology Data Query Parameters")
public class PaleoecologyDataDTO {

    // Search terms
    @Schema(description = "Search terms")
    private List<String> searchTerms;

    @Schema(description = "Collection name")
    private String collectionName;

    // Geographic parameters
    @Schema(description = "Polygons coordinates (pipe-separated)")
    private String polygons;

    @Schema(description = "Bounding boxes coordinates (pipe-separated)")
    private String bboxes;

    @Schema(description = "Circle centers (pipe-separated)")
    private List<String> circleCenters;

    @Schema(description = "Circle radii (pipe-separated)")
    private List<String> circleRadii;

    // Taxonomic Rank
    @Schema(description = "Accepted rank")
    private String acceptedRank;

    // Taxa
    @Schema(description = "Phylum")
    private String phylum;

    @Schema(description = "Class")
    private String classField;

    @Schema(description = "Order")
    private String order;

    @Schema(description = "Family")
    private String family;

    @Schema(description = "Genus")
    private String genus;

    @Schema(description = "Species")
    private String species;

    @Schema(description = "Scientific name")
    private String scientificName;

    @Schema(description = "Original name")
    private String originalName;

    // Time
    @Schema(description = "Epoch")
    private String epoch;

    @Schema(description = "Stage")
    private String stage;

    @Schema(description = "Early interval")
    private String earlyInterval;

    @Schema(description = "Late interval")
    private String lateInterval;

    @Schema(description = "Time bin")
    private String timeBin;

    // Geological age
    @Schema(description = "Minimum age")
    private String ageMin;

    @Schema(description = "Maximum age")
    private String ageMax;

    // Location & Dating
    @Schema(description = "Country")
    private String country;

    @Schema(description = "South latitude", defaultValue = "-90")
    private String southLatitude = "-90";

    @Schema(description = "North latitude", defaultValue = "90")
    private String northLatitude = "90";

    @Schema(description = "West longitude", defaultValue = "-180")
    private String westLongitude = "-180";

    @Schema(description = "East longitude", defaultValue = "180")
    private String eastLongitude = "180";

    @Schema(description = "Dating method")
    private String datingMethod;

    @Schema(description = "Dating quality")
    private String datingQuality;

    // Additional Info
    @Schema(description = "Author")
    private String author;

    @Schema(description = "Publication year")
    private String pubyr;

    @Schema(description = "Fossil type")
    private String fossilType;

    @Schema(description = "Plant organ")
    private String plantOrgan;
}
