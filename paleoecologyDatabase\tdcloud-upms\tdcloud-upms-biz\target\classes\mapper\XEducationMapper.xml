<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.XEducationMapper">

  <resultMap id="xEducationMap" type="com.tdkj.tdcloud.admin.api.entity.XEducation">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="languageType" column="language_type"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
  </resultMap>

	<sql id="selectXEducationVo">
		select id, title, content, language_type, type, create_time from x_education
	</sql>

	<select id="selectXEducationList" parameterType="com.tdkj.tdcloud.admin.api.entity.XEducation" resultMap="xEducationMap">
		<include refid="selectXEducationVo"/>
		<where>
			<if test="title != null  and title != ''"> and title = #{title}</if>
			<if test="content != null  and content != ''"> and content = #{content}</if>
			<if test="languageType != null  and languageType != ''"> and language_type = #{languageType}</if>
			<if test="type != null  and type != ''"> and type = #{type}</if>
		</where>
	</select>

	<select id="selectXEducationById" parameterType="Long" resultMap="xEducationMap">
		<include refid="selectXEducationVo"/>
		where id = #{id}
	</select>

	<select id="getPublicationTotal"  resultType="int">
		select count(id) from x_education
		where language_type = #{languageType} and type = #{type}
	</select>

	<insert id="insertXEducation" parameterType="com.tdkj.tdcloud.admin.api.entity.XEducation" useGeneratedKeys="true" keyProperty="id">
		insert into x_education
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="title != null">title,</if>
			<if test="content != null">content,</if>
			<if test="languageType != null">language_type,</if>
			<if test="type != null">type,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="title != null">#{title},</if>
			<if test="content != null">#{content},</if>
			<if test="languageType != null">#{languageType},</if>
			<if test="type != null">#{type},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>

	<update id="updateXEducation" parameterType="com.tdkj.tdcloud.admin.api.entity.XEducation">
		update x_education
		<trim prefix="SET" suffixOverrides=",">
			<if test="title != null">title = #{title},</if>
			<if test="content != null">content = #{content},</if>
			<if test="languageType != null">language_type = #{languageType},</if>
			<if test="type != null">type = #{type},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteXEducationById" parameterType="Long">
		delete from x_education where id = #{id}
	</delete>

	<delete id="deleteXEducationByIds" parameterType="String">
		delete from x_education where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>
