<template>
	<view class="wrap">
		<u-form class="form" :model="model" :rules="rules" ref="uForm" label-position="left">
#foreach($column in $columns)
#if($column.columnName != $pk.columnName)
	<u-form-item label="${column.comments}" prop="${column.lowerAttrName}" label-width="180">
		<u-input v-model="model.${column.lowerAttrName}" placeholder="${column.comments}"></u-input>
	</u-form-item>
#end
#end
	</u-form>
	<view class="form-footer">
		<u-button class="btn" type="primary" @click="submit">提交</u-button>
		<u-button class="btn" type="default" @click="cancel">关闭</u-button>
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'',
				model: {
				#foreach($column in $columns)
				#if($column.columnName == $pk.columnName)
					${column.lowerAttrName}: 0,
				#else
					${column.lowerAttrName}: ''#if($velocityCount != $columns.size()),#end
				#end
				#end
			},
				rules: {
				#foreach($column in $columns)
				#if($column.columnName != $pk.columnName)
					${column.lowerAttrName}: [
					  { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
					]#if($velocityCount != $columns.size()),#end

				#end
				#end
				}
			}
		},
		onLoad(params) {
			if(params&&params.id!= "undefined"){
				this.type = 'edit'
				#[[this.$u.api]]#.tdcloud${className}.getObj(params).then(res => {
					this.model = res.data;
				});
			}else{
				this.type = 'add'
				this.model = {}
			}
		},
		methods: {
			submit() {
				#[[this.$refs.uForm.validate(async valid => {]]#
					if (valid) {
						if(this.type==='edit'){
							await #[[this.$u.api]]#.tdcloud${className}.putObj(this.model)
						}else{
							await #[[this.$u.api]]#.tdcloud${className}.addObj(this.model)
						}
						setTimeout(() => {
							uni.showModal({
								title: '提示',
								ccontent: this.type === 'edit' ? '修改成功' : '添加成功',
								showCancel: false,
								success: function() {
									uni.setStorageSync('refreshList', true);
									uni.navigateTo({
										url: '/pages/${moduleName}/${pathName}/${pathName}-index'
									})
								}
							})
						}, 200)
					} else {
					 #[[this.$u.toast('您填写的信息有误，请根据提示修正。')]]#
					}
				});
			},
			cancel() {
				uni.navigateBack();
			}
		}
	}
</script>

