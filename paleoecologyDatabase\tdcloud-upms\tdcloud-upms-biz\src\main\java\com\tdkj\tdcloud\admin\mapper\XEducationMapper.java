/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.XEducation;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教育
 *
 * <AUTHOR> code generator
 * @date 2025-01-07 13:57:12
 */
@Mapper
public interface XEducationMapper extends TdcloudBaseMapper<XEducation> {

	/**
	 * 查询教育
	 *
	 * @param id 教育主键
	 * @return 教育
	 */
	public XEducation selectXEducationById(Long id);
	public int getPublicationTotal(@Param("languageType")String languageType,@Param("type")String type);

	/**
	 * 查询教育列表
	 *
	 * @param xEducation 教育
	 * @return 教育集合
	 */
	public List<XEducation> selectXEducationList(XEducation xEducation);

	/**
	 * 新增教育
	 *
	 * @param xEducation 教育
	 * @return 结果
	 */
	public int insertXEducation(XEducation xEducation);

	/**
	 * 修改教育
	 *
	 * @param xEducation 教育
	 * @return 结果
	 */
	public int updateXEducation(XEducation xEducation);

	/**
	 * 删除教育
	 *
	 * @param id 教育主键
	 * @return 结果
	 */
	public int deleteXEducationById(Long id);

	/**
	 * 批量删除教育
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteXEducationByIds(Long[] ids);
}
