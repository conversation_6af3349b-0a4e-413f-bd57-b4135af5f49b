<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.codegen.mapper.GeneratorMysqlMapper">

	<select id="queryTable" resultType="map">
		SELECT
			'mysql' AS dbType,
			table_name AS tableName,
			ENGINE,
			table_comment AS tableComment,
			create_time AS createTime
		FROM
			information_schema.TABLES
		WHERE
			table_schema = (
			SELECT DATABASE
			())
			<if test="tableName != null and tableName.trim() != ''">
				and table_name = #{tableName}
			</if>
			order by create_time desc
	</select>


	<sql id="queryColumn">
		SELECT
			column_name columnName,
			data_type dataType,
			column_comment comments,
			column_key columnKey,
			extra,
			is_nullable AS isNullable,
			column_type AS columnType
		FROM
			information_schema.COLUMNS
		WHERE
			table_name = #{tableName} and table_schema = (select database()) order by ordinal_position
	</sql>

	<select id="selectTableColumn" resultType="com.tdkj.tdcloud.codegen.entity.ColumnEntity">
		<include refid="queryColumn"/>
	</select>

	<select id="selectMapTableColumn" resultType="map">
		<include refid="queryColumn"/>
	</select>

</mapper>
