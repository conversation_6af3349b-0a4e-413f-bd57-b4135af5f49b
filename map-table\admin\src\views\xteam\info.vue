<template>
    <el-drawer
      title="" size="70%"
      :visible.sync="visible"
      :direction="'rtl'">
      <div class="detail">
        <el-descriptions class="margin-top" title="详情" :column="3" size="medium"  border>
          <el-descriptions-item label="姓名" label-class-name="my-label" content-class-name="my-content">{{ infoContent.personName }}</el-descriptions-item>
          <el-descriptions-item label="职称" label-class-name="my-label" content-class-name="my-content">{{infoContent.academicTitle}}</el-descriptions-item>
          <el-descriptions-item label="团队类型" label-class-name="my-label" content-class-name="my-content">{{infoContent.type === 'academician' ? '研究团队' : '人才培养'}}</el-descriptions-item>
          <el-descriptions-item label="岗位" label-class-name="my-label" content-class-name="my-content">{{infoContent.title}}</el-descriptions-item>
          <el-descriptions-item label="邮箱" label-class-name="my-label" content-class-name="my-content">{{infoContent.email}}</el-descriptions-item>
<!--          <el-descriptions-item label="邮政编码" label-class-name="my-label" content-class-name="my-content">{{infoContent.postalCode}}</el-descriptions-item>-->
          <el-descriptions-item label="简介" label-class-name="my-label" content-class-name="my-content" :span="3">{{infoContent.mailingAddress}}</el-descriptions-item>
          <el-descriptions-item label="简历" label-class-name="my-label" content-class-name="my-content" :span="3"><div v-html="infoContent.resume"></div></el-descriptions-item>
          <el-descriptions-item label="研究成果" label-class-name="my-label" content-class-name="my-content" :span="3"><div v-html="infoContent.publications"></div></el-descriptions-item>
          <el-descriptions-item label="照片" label-class-name="my-label" content-class-name="my-content" :span="3"><el-image style="max-width: 70%;object-fit: cover" :src="infoContent.sysFile ? infoContent.sysFile.url : ''"></el-image></el-descriptions-item>
          <el-descriptions-item label="语言类型" label-class-name="my-label" content-class-name="my-content">{{ infoContent.languageType === 'zh' ? '中文' : '英文'}}</el-descriptions-item>
          <el-descriptions-item label="创建时间" label-class-name="my-label" content-class-name="my-content">{{infoContent.createTime}}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-drawer>
</template>

<script>
import { getObj } from '@/api/xteam/xteam'
export default {
  name: "info",
  data() {
    return {
      visible: false,
      infoContent: {}
    }
  },
  methods: {
    getDetailInfo(id) {
      this.visible = true
      getObj(id).then(res => {
        console.log(res)
        if(res.data.code === 0) {
          this.infoContent = res.data.data
        }
      })
    }
  }
}
</script>

<style scoped>
.detail {
  padding: 32px 16px;
}
::v-deep .my-label {
  background-color: #F3F3F3 !important;
  color: #929292 !important;
  border-color: #DCDCDC !important;
  width: 125px !important;
}
::v-deep .my-content {
  color: #525252 !important;
  font-size: 16px !important;
}
::v-deep .el-descriptions-item__label {
//font-weight: 700 !important;

}
</style>
