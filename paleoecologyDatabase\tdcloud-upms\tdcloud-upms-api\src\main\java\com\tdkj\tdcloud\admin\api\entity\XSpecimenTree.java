/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 物种信息表树形结构
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:46:01
 */
@Data
@TableName("x_specimen_tree")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物种信息表树形结构")
public class XSpecimenTree extends Model<XSpecimenTree> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Integer id;

    /**
     * 物种英文名
     */
    @Schema(description="物种英文名")
    private String nameEn;

    /**
     * 物种中文名
     */
    @Schema(description="物种中文名")
    private String nameCn;

    /**
     * 排序
     */
    @Schema(description="排序")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * delFlag
     */
    @TableLogic
    @Schema(description="delFlag")
    private String delFlag;

    /**
     * 父级英文名
     */
    @Schema(description="父级英文名")
    private String parentEn;

    /**
     * 级别
     */
    @Schema(description="级别")
    private String belongLevel;

    /**
     * 语言类型zh中文en英文
     */
    @Schema(description="语言类型zh中文en英文")
    private String languageType;

    @TableField(exist = false)
	private String nameCnNameEn;

	@TableField(exist = false)
	private List<XSpecimenTree> child = new ArrayList<>();

	@TableField(exist = false)
	private int childCount;

}
