<template>
    <div class="search-panel">
        <div class="search-header">Search</div>
        <el-collapse v-model="activeNames">
            <el-collapse-item title="Taxonomic Rank" name="1">
                <div class="input-group">
                    <label>AcceptedRank</label>
                    <select v-model="searchCriteria.acceptedRank" class="text-input">
                        <option value="">-- Select --</option>
                        <option value="genus">genus</option>
                        <option value="species">species</option>
                        <option value="variety">variety</option>
                        <option value="family">family</option>
                        <option value="subspecies">subspecies</option>
                        <option value="section">section</option>
                    </select>
                </div>
            </el-collapse-item>
            <el-collapse-item name="2">
                <template slot="title">
                    <span>Taxa</span>
                    <i v-if="taxaRefreshStatus === 'idle' || taxaRefreshStatus === 'error' || taxaRefreshStatus === 'loading'"
                        class="el-icon-refresh refresh-icon" @click.stop="refreshTaxaSuggestionData"
                        title="Refresh taxa suggestion data" :class="{
                            'is-loading': taxaRefreshStatus === 'loading',
                            'is-error': taxaRefreshStatus === 'error'
                        }"></i>
                </template>
                <div>
                    <div class="input-group">
                        <label>Family</label>
                        <el-autocomplete v-model="searchCriteria.family" :fetch-suggestions="queryFamilySuggestions"
                            placeholder="Input Family" popper-class="my-autocomplete" clearable
                            :loading="loadingFamilies"></el-autocomplete>
                    </div>

                    <div class="input-group">
                        <label>Genus</label>
                        <el-autocomplete v-model="searchCriteria.genus" :fetch-suggestions="queryGenusSuggestions"
                            placeholder="Input Genus" popper-class="my-autocomplete" clearable
                            :loading="loadingGenera"></el-autocomplete>
                    </div>

                    <div class="input-group">
                        <label>Species</label>
                        <el-autocomplete v-model="searchCriteria.species" :fetch-suggestions="querySpeciesSuggestions"
                            placeholder="Input Species" popper-class="my-autocomplete" clearable
                            :loading="loadingSpecies"></el-autocomplete>
                    </div>

                    <div class="input-group">
                        <label>ScientificName</label>
                        <el-autocomplete v-model="searchCriteria.scientificName"
                            :fetch-suggestions="queryScientificNameSuggestions" placeholder="Input Scientific Name"
                            popper-class="my-autocomplete" clearable
                            :loading="loadingScientificNames"></el-autocomplete>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="Country" name="3">
                <div class="input-group">
                    <label>Country</label>
                    <el-autocomplete v-model="searchCriteria.country" :fetch-suggestions="queryCountrySuggestions"
                        placeholder="Input Country" popper-class="my-autocomplete" clearable></el-autocomplete>
                </div>
            </el-collapse-item>
            <el-collapse-item name="4">
                <template slot="title">
                    <span>Dating</span>
                    <i v-if="datingRefreshStatus === 'idle' || datingRefreshStatus === 'error' || datingRefreshStatus === 'loading'"
                        class="el-icon-refresh refresh-icon" @click.stop="refreshSuggestionData"
                        title="Refresh suggestion data" :class="{
                            'is-loading': datingRefreshStatus === 'loading',
                            'is-error': datingRefreshStatus === 'error'
                        }"></i>
                </template>
                <div>
                    <div class="input-group">
                        <label>Dating Method</label>
                        <el-autocomplete v-model="searchCriteria.datingMethod"
                            :fetch-suggestions="queryDatingMethodSuggestions" placeholder="Input Dating Method"
                            popper-class="my-autocomplete" clearable :loading="loadingDatingMethods"></el-autocomplete>
                    </div>
                    <div class="input-group">
                        <label>Dating Quality</label>
                        <el-autocomplete v-model="searchCriteria.datingQuality"
                            :fetch-suggestions="queryDatingQualitySuggestions" placeholder="Input Dating Quality"
                            popper-class="my-autocomplete" clearable
                            :loading="loadingDatingQualities"></el-autocomplete>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="Geologic interval" name="5">
                <div>
                    <div class="input-group">
                        <label>Epoch</label>
                        <select v-model="searchCriteria.epoch" class="text-input">
                            <option value="">-- Select --</option>
                            <option value="Paleocene">Paleocene</option>
                            <option value="Eocene">Eocene</option>
                            <option value="Oligocene">Oligocene</option>
                            <option value="Miocene">Miocene</option>
                            <option value="Pliocene">Pliocene</option>
                            <option value="Pleistocene">Pleistocene</option>
                            <option value="Holocene">Holocene</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Stage</label>
                        <select v-model="searchCriteria.stage" class="text-input">
                            <option value="">-- Select --</option>
                            <option v-for="stage in filteredStages" :value="stage">{{ stage }}</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Early Interval</label>
                        <select v-model="searchCriteria.earlyInterval" class="text-input">
                            <option value="">-- Select --</option>
                            <option v-for="stage in filteredStages" :value="stage">{{ stage }}</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>Late Interval</label>
                        <select v-model="searchCriteria.lateInterval" class="text-input">
                            <option value="">-- Select --</option>
                            <option v-for="stage in filteredStages" :value="stage">{{ stage }}</option>
                        </select>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="Geological age" name="6">
                <div class="age-range-container">
                    <label class="age-label">Age range</label>
                    <div class="age-inputs">
                        <input type="text" v-model="searchCriteria.ageMax" class="text-input age-input"
                            placeholder="AgeMax" />
                        <span class="age-separator">to</span>
                        <input type="text" v-model="searchCriteria.ageMin" class="text-input age-input"
                            placeholder="AgeMin" />
                        <span class="age-unit">Ma</span>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="TimeBin" name="7">
                <div class="input-group">
                    <label>Time Bin</label>
                    <select v-model="searchCriteria.timeBin" class="text-input">
                        <option value="">-- Select --</option>
                        <option v-for="stage in allStages" :key="stage" :value="stage">{{ stage }}</option>
                    </select>
                </div>
            </el-collapse-item>
            <el-collapse-item title="Location" name="8">
                <div>
                    <div class="location-row">
                        <div class="location-label">South</div>
                        <div class="location-input-container">
                            <input type="text" v-model="searchCriteria.southLatitude" class="text-input location-input"
                                placeholder="-90" />
                        </div>
                        <div class="location-label">North</div>
                        <div class="location-input-container">
                            <input type="text" v-model="searchCriteria.northLatitude" class="text-input location-input"
                                placeholder="90" />
                        </div>
                    </div>
                    <div class="location-slider-container">
                        <input type="range" min="-90" max="90" v-model.number="latitudeSliderValue"
                            class="location-slider" />
                    </div>

                    <div class="location-row">
                        <div class="location-label">West</div>
                        <div class="location-input-container">
                            <input type="text" v-model="searchCriteria.westLongitude" class="text-input location-input"
                                placeholder="-180" />
                        </div>
                        <div class="location-label">East</div>
                        <div class="location-input-container">
                            <input type="text" v-model="searchCriteria.eastLongitude" class="text-input location-input"
                                placeholder="180" />
                        </div>
                    </div>
                    <div class="location-slider-container">
                        <input type="range" min="-180" max="180" v-model.number="longitudeSliderValue"
                            class="location-slider" />
                    </div>

                </div>
            </el-collapse-item>
            <el-collapse-item name="9">
                <template slot="title">
                    <span>FossilType</span>
                    <i v-if="additionalFieldsRefreshStatus === 'idle' || additionalFieldsRefreshStatus === 'error' || additionalFieldsRefreshStatus === 'loading'"
                        class="el-icon-refresh refresh-icon" @click.stop="refreshAdditionalFieldsData"
                        title="Refresh fossil type options" :class="{
                            'is-loading': additionalFieldsRefreshStatus === 'loading',
                            'is-error': additionalFieldsRefreshStatus === 'error'
                        }"></i>
                </template>
                <div class="input-group">
                    <label>Fossil Type</label>
                    <el-select v-model="searchCriteria.fossilType" placeholder="Select Fossil Type" clearable
                        :loading="loadingFossilTypes">
                        <el-option v-for="item in fossilTypeSuggestions" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </div>
            </el-collapse-item>
            <el-collapse-item title="OriginalName" name="10">
                <div class="input-group">
                    <label>Original Name</label>
                    <el-autocomplete v-model="searchCriteria.originalName"
                        :fetch-suggestions="queryOriginalNameSuggestions" placeholder="Input Original Name"
                        popper-class="my-autocomplete" clearable :loading="loadingOriginalNames"></el-autocomplete>
                </div>
            </el-collapse-item>
            <el-collapse-item title="PlantOrgan" name="11">
                <div class="input-group">
                    <label>Plant Organ</label>
                    <el-select v-model="searchCriteria.plantOrgan" placeholder="Select Plant Organ" clearable
                        :loading="loadingPlantOrgans">
                        <el-option v-for="item in plantOrganSuggestions" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </div>
            </el-collapse-item>
            <el-collapse-item title="Literature identification" name="12">
                <div>
                    <div class="input-group">
                        <label>Author</label>
                        <input type="text" v-model="searchCriteria.author" class="text-input" />
                    </div>
                    <div class="input-group">
                        <label>Publication Year</label>
                        <input type="text" v-model="searchCriteria.pubyr" class="text-input" />
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>
        <div class="search-footer">
            <div class="search-input-group">
                <button @click="performSearch">Search</button>
                <button @click="clearSearchCriteria" class="clear-button">Clear</button>
            </div>
        </div>
    </div>
</template>

<script>
import { getDatingMethods, getDatingQualities } from '@/api/dating';
import { getFamilies, getGenera, getSpecies, getScientificNames } from '@/api/taxa';
import { getOriginalNames, getFossilTypes, getPlantOrgans } from '@/api/additional-fields';

export default {
    props: ['boundingBoxes', 'polygons', 'circleCenters', 'circleRadii', 'searchHistory'],
    data() {
        return {
            activeNames: [], // All sections collapsed by default
            // Loading states
            loadingDatingMethods: false,
            loadingDatingQualities: false,
            loadingFamilies: false,
            loadingGenera: false,
            loadingSpecies: false,
            loadingScientificNames: false,
            loadingOriginalNames: false,
            loadingFossilTypes: false,
            loadingPlantOrgans: false,
            // Refresh status tracking
            taxaRefreshStatus: 'idle', // 'idle', 'loading', 'success', 'error'
            datingRefreshStatus: 'idle',
            additionalFieldsRefreshStatus: 'idle',
            // Suggestion data arrays
            datingMethodSuggestions: [],
            datingQualitySuggestions: [],
            familySuggestions: [],
            genusSuggestions: [],
            speciesSuggestions: [],
            scientificNameSuggestions: [],
            originalNameSuggestions: [],
            fossilTypeSuggestions: [],
            plantOrganSuggestions: [],
            searchCriteria: {
                collectionName: '',
                spaceType: '',
                // Taxonomy
                acceptedRank: '',
                phylum: '',
                class: '',
                order: '',
                family: '',
                genus: '',
                species: '',
                scientificName: '',
                originalName: '',
                // Taxa
                taxa: '',
                // Time
                epoch: '',
                stage: '',
                earlyInterval: '',
                lateInterval: '',
                timeBin: '',
                // Geological age
                ageMin: '',
                ageMax: '',
                // Location & Dating
                country: '',
                latitude: '',
                longitude: '',
                southLatitude: '-90',
                northLatitude: '90',
                westLongitude: '-180',
                eastLongitude: '180',
                datingMethod: '',
                datingQuality: '',
                // Additional Info
                author: '',
                pubyr: '',
                fossilType: '',
                plantOrgan: ''
            },
            // Define the epoch-stage relationship
            epochStageMap: {
                'Paleocene': ['Danian', 'Selandian', 'Thanetian'],
                'Eocene': ['Ypresian', 'Lutetian', 'Bartonian', 'Priabonian'],
                'Oligocene': ['Rupelian', 'Chattian'],
                'Miocene': ['Aquitanian', 'Burdigalian', 'Langhian', 'Serravallian', 'Tortonian', 'Messinian'],
                'Pliocene': ['Zanclean', 'Piacenzian'],
                'Pleistocene': ['Gelasian', 'Calabrian', 'Chibanian', 'Upper'],
                'Holocene': ['Greenlandian', 'Northgrippian', 'Meghalayan']
            },
            // All stages for when no epoch is selected
            allStages: [
                'Danian', 'Selandian', 'Thanetian', 'Ypresian', 'Lutetian', 'Bartonian', 'Priabonian',
                'Rupelian', 'Chattian', 'Aquitanian', 'Burdigalian', 'Langhian', 'Serravallian',
                'Tortonian', 'Messinian', 'Zanclean', 'Piacenzian', 'Gelasian', 'Calabrian',
                'Chibanian', 'Upper', 'Greenlandian', 'Northgrippian', 'Meghalayan'
            ],
            selectedSearch: '',
            savedSearches: [],
            // Add suggestion data for autocomplete
            familySuggestions: [],
            genusSuggestions: [],
            speciesSuggestions: [],
            scientificNameSuggestions: [],
            countrySuggestions: [
                'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda', 'Argentina',
                'Armenia', 'Australia', 'Austria', 'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh',
                'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan', 'Bolivia',
                'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso',
                'Burundi', 'Cabo Verde', 'Cambodia', 'Cameroon', 'Canada', 'Central African Republic',
                'Chad', 'Chile', 'China', 'Colombia', 'Comoros', 'Congo', 'Costa Rica', 'Croatia',
                'Cuba', 'Cyprus', 'Czech Republic', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic',
                'Ecuador', 'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', 'Eswatini',
                'Ethiopia', 'Fiji', 'Finland', 'France', 'Gabon', 'Gambia', 'Georgia', 'Germany',
                'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau', 'Guyana',
                'Haiti', 'Honduras', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq',
                'Ireland', 'Israel', 'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya',
                'Kiribati', 'Korea, North', 'Korea, South', 'Kosovo', 'Kuwait', 'Kyrgyzstan', 'Laos',
                'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta',
                'Marshall Islands', 'Mauritania', 'Mauritius', 'Mexico', 'Micronesia', 'Moldova',
                'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar', 'Namibia',
                'Nauru', 'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria',
                'North Macedonia', 'Norway', 'Oman', 'Pakistan', 'Palau', 'Palestine', 'Panama',
                'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal', 'Qatar',
                'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia',
                'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',
                'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore',
                'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia', 'South Africa', 'South Sudan',
                'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',
                'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga',
                'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan', 'Tuvalu', 'Uganda',
                'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay',
                'Uzbekistan', 'Vanuatu', 'Vatican City', 'Venezuela', 'Vietnam', 'Yemen', 'Zambia',
                'Zimbabwe'
            ]
        }
    },
    computed: {
        // Filtered stages based on selected epoch
        filteredStages() {
            if (!this.searchCriteria.epoch) return [];
            return this.epochStageMap[this.searchCriteria.epoch] || [];
        },
        // Computed property for latitude slider
        latitudeSliderValue: {
            get() {
                // Default to 0 if no value is set
                if (!this.searchCriteria.southLatitude && !this.searchCriteria.northLatitude) {
                    return 0;
                }
                // Return the average of south and north latitude
                const south = parseFloat(this.searchCriteria.southLatitude) || -90;
                const north = parseFloat(this.searchCriteria.northLatitude) || 90;
                return (south + north) / 2;
            },
            set(value) {
                // When slider moves, update both south and north latitude
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    // Set a range of ±10 degrees around the slider value
                    this.searchCriteria.southLatitude = Math.max(-90, numValue - 10).toFixed(2);
                    this.searchCriteria.northLatitude = Math.min(90, numValue + 10).toFixed(2);
                }
            }
        },
        // Computed property for longitude slider
        longitudeSliderValue: {
            get() {
                // Default to 0 if no value is set
                if (!this.searchCriteria.westLongitude && !this.searchCriteria.eastLongitude) {
                    return 0;
                }
                // Return the average of west and east longitude
                const west = parseFloat(this.searchCriteria.westLongitude) || -180;
                const east = parseFloat(this.searchCriteria.eastLongitude) || 180;
                return (west + east) / 2;
            },
            set(value) {
                // When slider moves, update both west and east longitude
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    // Set a range of ±20 degrees around the slider value
                    this.searchCriteria.westLongitude = Math.max(-180, numValue - 20).toFixed(2);
                    this.searchCriteria.eastLongitude = Math.min(180, numValue + 20).toFixed(2);
                }
            }
        }
    },
    watch: {
        // Watch for changes in epoch to reset stage if it's not valid for the new epoch
        'searchCriteria.epoch': function (newEpoch) {
            const validStages = this.epochStageMap[newEpoch] || [];
            if (this.searchCriteria.stage && !validStages.includes(this.searchCriteria.stage)) {
                this.searchCriteria.stage = '';
            }
            // Also reset early and late intervals if they're not valid
            if (this.searchCriteria.earlyInterval && !validStages.includes(this.searchCriteria.earlyInterval)) {
                this.searchCriteria.earlyInterval = '';
            }
            if (this.searchCriteria.lateInterval && !validStages.includes(this.searchCriteria.lateInterval)) {
                this.searchCriteria.lateInterval = '';
            }
        },
        searchHistory: {
            immediate: true,
            handler(newHistory) {
                if (newHistory && newHistory.length > 0) {
                    this.savedSearches = [...newHistory];
                    // If no selection yet, select the most recent one
                    if (!this.selectedSearch && newHistory.length > 0) {
                        this.selectedSearch = newHistory[newHistory.length - 1].id;
                    }
                }
            }
        },
        // Watch for direct changes to the south latitude input
        'searchCriteria.southLatitude': function (newValue) {
            const south = parseFloat(newValue);
            const north = parseFloat(this.searchCriteria.northLatitude) || 90;

            // Ensure south is less than north
            if (!isNaN(south) && south > north) {
                this.searchCriteria.northLatitude = Math.min(90, south + 1).toFixed(2);
            }
        },
        // Watch for direct changes to the north latitude input
        'searchCriteria.northLatitude': function (newValue) {
            const north = parseFloat(newValue);
            const south = parseFloat(this.searchCriteria.southLatitude) || -90;

            // Ensure north is greater than south
            if (!isNaN(north) && north < south) {
                this.searchCriteria.southLatitude = Math.max(-90, north - 1).toFixed(2);
            }
        },
        // Watch for direct changes to the west longitude input
        'searchCriteria.westLongitude': function (newValue) {
            const west = parseFloat(newValue);
            const east = parseFloat(this.searchCriteria.eastLongitude) || 180;

            // Ensure west is less than east
            if (!isNaN(west) && west > east) {
                this.searchCriteria.eastLongitude = Math.min(180, west + 1).toFixed(2);
            }
        },
        // Watch for direct changes to the east longitude input
        'searchCriteria.eastLongitude': function (newValue) {
            const east = parseFloat(newValue);
            const west = parseFloat(this.searchCriteria.westLongitude) || -180;

            // Ensure east is greater than west
            if (!isNaN(east) && east < west) {
                this.searchCriteria.westLongitude = Math.max(-180, east - 1).toFixed(2);
            }
        }
    },
    mounted() {
        // Initialize refresh status
        this.taxaRefreshStatus = 'idle';
        this.datingRefreshStatus = 'idle';
        this.additionalFieldsRefreshStatus = 'idle';

        // Fetch all suggestion data when component is mounted
        // Use the refresh methods to ensure proper status tracking
        this.refreshSuggestionData();
        this.refreshTaxaSuggestionData();
        this.refreshAdditionalFieldsData();

        // Set up a refresh interval to keep the suggestion data up-to-date
        // Refresh every 10 minutes (600000 ms)
        this.refreshInterval = setInterval(() => {
            // console.log('Refreshing all suggestion data...');
            // Use the refresh methods to update all data
            this.refreshSuggestionData();
            this.refreshTaxaSuggestionData();
            this.refreshAdditionalFieldsData();
        }, 600000);

        // Add keyboard shortcut for Search button
        document.addEventListener('keydown', this.handleKeyDown);
    },

    beforeDestroy() {
        // Clear the refresh interval when the component is destroyed
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // Remove keyboard event listener
        document.removeEventListener('keydown', this.handleKeyDown);
    },
    methods: {
        // Helper method to extract array data from API response
        extractArrayFromResponse(response) {
            if (response.data && response.data.data && Array.isArray(response.data.data)) {
                // Java Spring Boot R.ok(data) format
                return response.data.data;
            } else if (response.data && Array.isArray(response.data)) {
                // Direct array format
                return response.data;
            }
            return [];
        },

        // Fetch unique dating methods from the API with retry mechanism
        fetchDatingMethods(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingDatingMethods = true;

            return new Promise((resolve, reject) => {
                getDatingMethods()
                    .then(response => {
                        // Handle the R response wrapper structure
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the dating methods
                            const uniqueMethods = [...new Set(data.filter(method => method))];
                            this.datingMethodSuggestions = uniqueMethods.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching dating methods:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying dating methods fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchDatingMethods(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.datingMethodSuggestions = [];
                            // Set the refresh status to error to show the refresh button
                            if (retryCount === maxRetries) {
                                this.datingRefreshStatus = 'error';
                            }
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingDatingMethods = false;
                        }
                    });
            });
        },

        // Fetch unique dating qualities from the API with retry mechanism
        fetchDatingQualities(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingDatingQualities = true;

            return new Promise((resolve, reject) => {
                getDatingQualities()
                    .then(response => {
                        // Handle the R response wrapper structure
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the dating qualities
                            const uniqueQualities = [...new Set(data.filter(quality => quality))];
                            this.datingQualitySuggestions = uniqueQualities.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching dating qualities:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying dating qualities fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchDatingQualities(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.datingQualitySuggestions = [];
                            // Set the refresh status to error to show the refresh button
                            if (retryCount === maxRetries) {
                                this.datingRefreshStatus = 'error';
                            }
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingDatingQualities = false;
                        }
                    });
            });
        },

        isMapSelection(searchItem) {
            if (!searchItem || !searchItem.criteria) return false;

            // Check if this search includes map selection
            return (
                (searchItem.criteria.bboxes && searchItem.criteria.bboxes.length > 0) ||
                (searchItem.criteria.polygons && searchItem.criteria.polygons.length > 0) ||
                (searchItem.criteria.circle_centers && searchItem.criteria.circle_centers.length > 0)
            );
        },
        onSavedSearchChange(searchId) {
            // Find the selected search
            const selectedSearch = this.savedSearches.find(search => search.id === searchId);
            if (selectedSearch && selectedSearch.criteria) {
                // console.log('Selected search:', selectedSearch);

                // Update local search criteria to match the selected search
                this.updateLocalSearchCriteria(selectedSearch.criteria);

                // Emit the selected search criteria to the parent component
                this.$emit('search-result-selected', selectedSearch.criteria);
            }
        },
        updateLocalSearchCriteria(criteria) {
            // Update all the form fields to match the selected search criteria
            Object.keys(this.searchCriteria).forEach(key => {
                if (criteria[key] !== undefined) {
                    this.searchCriteria[key] = criteria[key];
                } else {
                    // Reset fields not in the criteria
                    if (typeof this.searchCriteria[key] === 'string') {
                        this.searchCriteria[key] = '';
                    } else if (Array.isArray(this.searchCriteria[key])) {
                        this.searchCriteria[key] = [];
                    }
                }
            });
        },
        performSearch() {
            const updatedCriteria = {
                ...this.searchCriteria,
                bboxes: this.boundingBoxes.join('|'),
                polygons: this.polygons.join('|'),
                circle_centers: this.circleCenters.join('|'),
                circle_radii: this.circleRadii.join('|')
            };
            this.$emit('search', updatedCriteria);
            this.$emit('update:searchCriteria', { ...this.searchCriteria });

            // After search, update the selected search to the most recent one
            this.$nextTick(() => {
                if (this.searchHistory && this.searchHistory.length > 0) {
                    this.selectedSearch = this.searchHistory[this.searchHistory.length - 1].id;
                }
            });
        },
        setBoundingBox(bbox) {
            this.searchCriteria.boundingBox = bbox;
        },
        clearSearchCriteria() {
            // Reset search criteria fields
            this.searchCriteria = {
                collectionName: '',
                spaceType: '',
                // Taxonomy
                acceptedRank: '',
                phylum: '',
                class: '',
                order: '',
                family: '',
                genus: '',
                species: '',
                scientificName: '',
                originalName: '',
                // Taxa
                taxa: '',
                // Time
                epoch: '',
                stage: '',
                earlyInterval: '',
                lateInterval: '',
                timeBin: '',
                // Geological age
                ageMin: '',
                ageMax: '',
                // Location & Dating
                country: '',
                latitude: '',
                longitude: '',
                southLatitude: '-90',
                northLatitude: '90',
                westLongitude: '-180',
                eastLongitude: '180',
                datingMethod: '',
                datingQuality: '',
                // Additional Info
                author: '',
                pubyr: '',
                fossilType: '',
                plantOrgan: ''
            };

            // Clear map selection data
            this.$emit('clear-map-selections');

            // Use a different event for clearing to avoid adding to search history
            const clearedCriteria = {
                ...this.searchCriteria,
                bboxes: '',
                polygons: '',
                circle_centers: '',
                circle_radii: ''
            };

            // Emit clear event instead of search event
            this.$emit('clear', clearedCriteria);
        },
        // Autocomplete suggestion methods
        queryFamilySuggestions(queryString, callback) {
            const results = queryString
                ? this.familySuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.familySuggestions;

            // Return results as objects with value property
            callback(results.map(item => ({ value: item })));
        },

        queryGenusSuggestions(queryString, callback) {
            const results = queryString
                ? this.genusSuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.genusSuggestions;

            callback(results.map(item => ({ value: item })));
        },

        querySpeciesSuggestions(queryString, callback) {
            const results = queryString
                ? this.speciesSuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.speciesSuggestions;

            callback(results.map(item => ({ value: item })));
        },

        queryScientificNameSuggestions(queryString, callback) {
            const results = queryString
                ? this.scientificNameSuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.scientificNameSuggestions;

            callback(results.map(item => ({ value: item })));
        },

        queryOriginalNameSuggestions(queryString, callback) {
            const results = queryString
                ? this.originalNameSuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.originalNameSuggestions;

            callback(results.map(item => ({ value: item })));
        },

        queryCountrySuggestions(queryString, callback) {
            const results = queryString
                ? this.countrySuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.countrySuggestions;

            callback(results.map(item => ({ value: item })));
        },

        queryDatingMethodSuggestions(queryString, callback) {
            const results = queryString
                ? this.datingMethodSuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.datingMethodSuggestions;

            callback(results.map(item => ({ value: item })));
        },

        queryDatingQualitySuggestions(queryString, callback) {
            const results = queryString
                ? this.datingQualitySuggestions.filter(item => {
                    return item.toLowerCase().includes(queryString.toLowerCase());
                })
                : this.datingQualitySuggestions;

            callback(results.map(item => ({ value: item })));
        },

        // Method to manually refresh the dating suggestion data
        refreshSuggestionData() {
            // console.log('Manually refreshing dating method and quality suggestions...');
            this.datingRefreshStatus = 'loading';

            // Create promises for both fetch operations
            const promises = [
                this.fetchDatingMethods(0, false),
                this.fetchDatingQualities(0, false)
            ];

            // Wait for all promises to resolve
            Promise.all(promises)
                .then(() => {
                    // Set status to success and keep it that way to hide the refresh button
                    this.datingRefreshStatus = 'success';
                })
                .catch(() => {
                    // Set status to error to show the refresh button
                    this.datingRefreshStatus = 'error';
                });
        },

        // Method to manually refresh the taxa suggestion data
        refreshTaxaSuggestionData() {
            // console.log('Manually refreshing taxa suggestions...');
            this.taxaRefreshStatus = 'loading';

            // Create promises for all fetch operations
            const promises = [
                this.fetchFamilies(0, false),
                this.fetchGenera(0, false),
                this.fetchSpecies(0, false),
                this.fetchScientificNames(0, false),
                this.fetchOriginalNames(0, false)
            ];

            // Wait for all promises to resolve
            Promise.all(promises)
                .then(() => {
                    // Set status to success and keep it that way to hide the refresh button
                    this.taxaRefreshStatus = 'success';
                })
                .catch(() => {
                    // Set status to error to show the refresh button
                    this.taxaRefreshStatus = 'error';
                });
        },

        // Method to manually refresh the additional fields suggestion data
        refreshAdditionalFieldsData() {
            // console.log('Manually refreshing additional fields suggestions...');
            this.additionalFieldsRefreshStatus = 'loading';

            // Create promises for both fetch operations
            const promises = [
                this.fetchFossilTypes(0, false),
                this.fetchPlantOrgans(0, false)
            ];

            // Wait for all promises to resolve
            Promise.all(promises)
                .then(() => {
                    // Set status to success and keep it that way to hide the refresh button
                    this.additionalFieldsRefreshStatus = 'success';
                })
                .catch(() => {
                    // Set status to error to show the refresh button
                    this.additionalFieldsRefreshStatus = 'error';
                });
        },

        // Handle keyboard shortcuts
        handleKeyDown(event) {
            // Check if Enter key is pressed and not in a textarea
            if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
                // Prevent default action to avoid form submission
                event.preventDefault();
                // Trigger search
                this.performSearch();
            }
        },

        // Fetch unique families from the API with retry mechanism
        fetchFamilies(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingFamilies = true;

            return new Promise((resolve, reject) => {
                getFamilies()
                    .then(response => {
                        // Handle the R response wrapper structure
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the families
                            const uniqueFamilies = [...new Set(data.filter(family => family))];
                            this.familySuggestions = uniqueFamilies.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching families:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying families fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchFamilies(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.familySuggestions = [];
                            // Set the refresh status to error to show the refresh button
                            if (retryCount === maxRetries) {
                                this.taxaRefreshStatus = 'error';
                            }
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingFamilies = false;
                        }
                    });
            });
        },

        // Fetch unique genera from the API with retry mechanism
        fetchGenera(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingGenera = true;

            return new Promise((resolve, reject) => {
                getGenera()
                    .then(response => {
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the genera
                            const uniqueGenera = [...new Set(data.filter(genus => genus))];
                            this.genusSuggestions = uniqueGenera.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching genera:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying genera fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchGenera(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.genusSuggestions = [];
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingGenera = false;
                        }
                    });
            });
        },

        // Fetch unique species from the API with retry mechanism
        fetchSpecies(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingSpecies = true;

            return new Promise((resolve, reject) => {
                getSpecies()
                    .then(response => {
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the species
                            const uniqueSpecies = [...new Set(data.filter(species => species))];
                            this.speciesSuggestions = uniqueSpecies.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching species:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying species fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchSpecies(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.speciesSuggestions = [];
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingSpecies = false;
                        }
                    });
            });
        },

        // Fetch unique scientific names from the API with retry mechanism
        fetchScientificNames(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingScientificNames = true;

            return new Promise((resolve, reject) => {
                getScientificNames()
                    .then(response => {
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the scientific names
                            const uniqueScientificNames = [...new Set(data.filter(name => name))];
                            this.scientificNameSuggestions = uniqueScientificNames.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching scientific names:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying scientific names fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchScientificNames(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.scientificNameSuggestions = [];
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingScientificNames = false;
                        }
                    });
            });
        },

        // Fetch unique original names from the API with retry mechanism
        fetchOriginalNames(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingOriginalNames = true;

            return new Promise((resolve, reject) => {
                getOriginalNames()
                    .then(response => {
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the original names
                            const uniqueOriginalNames = [...new Set(data.filter(name => name))];
                            this.originalNameSuggestions = uniqueOriginalNames.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching original names:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying original names fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchOriginalNames(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.originalNameSuggestions = [];
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingOriginalNames = false;
                        }
                    });
            });
        },

        // Fetch unique fossil types from the API with retry mechanism
        fetchFossilTypes(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingFossilTypes = true;

            return new Promise((resolve, reject) => {
                getFossilTypes()
                    .then(response => {
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the fossil types
                            const uniqueFossilTypes = [...new Set(data.filter(type => type))];
                            this.fossilTypeSuggestions = uniqueFossilTypes.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching fossil types:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying fossil types fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchFossilTypes(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.fossilTypeSuggestions = [];
                            // Set the refresh status to error to show the refresh button
                            if (retryCount === maxRetries) {
                                this.additionalFieldsRefreshStatus = 'error';
                            }
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingFossilTypes = false;
                        }
                    });
            });
        },

        // Fetch unique plant organs from the API with retry mechanism
        fetchPlantOrgans(retryCount = 0, showNotifications = true) {
            const maxRetries = 3;
            this.loadingPlantOrgans = true;

            return new Promise((resolve, reject) => {
                getPlantOrgans()
                    .then(response => {
                        // console.log('Plant organs API response:', response.data);
                        const data = this.extractArrayFromResponse(response);

                        if (Array.isArray(data)) {
                            // Deduplicate and sort the plant organs
                            const uniquePlantOrgans = [...new Set(data.filter(organ => organ))];
                            this.plantOrganSuggestions = uniquePlantOrgans.sort();
                            resolve();
                        } else {
                            throw new Error('Invalid response format');
                        }
                    })
                    .catch(error => {
                        // console.error('Error fetching plant organs:', error);

                        if (retryCount < maxRetries) {
                            // Retry the API call with exponential backoff
                            const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                            // console.log(`Retrying plant organs fetch in ${delay}ms...`);

                            setTimeout(() => {
                                this.fetchPlantOrgans(retryCount + 1, showNotifications)
                                    .then(resolve)
                                    .catch(reject);
                            }, delay);
                        } else {
                            // After max retries
                            this.plantOrganSuggestions = [];
                            // Set the refresh status to error to show the refresh button
                            if (retryCount === maxRetries) {
                                this.additionalFieldsRefreshStatus = 'error';
                            }
                            reject(error);
                        }
                    })
                    .finally(() => {
                        if (retryCount === 0 || retryCount >= maxRetries) {
                            this.loadingPlantOrgans = false;
                        }
                    });
            });
        },
    }
}
</script>

<style>
.search-panel {
    position: absolute;
    left: 0;
    top: 0px;
    bottom: 0;
    width: 275px !important;
    background-color: #F7F9FA;
    z-index: 1000;
    color: #344952;
    min-height: calc(100vh - 372px);
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    box-sizing: border-box;
    overflow-y: auto;
    height: 100%;
}

/* Custom scrollbar styling */
.search-panel::-webkit-scrollbar {
    width: 6px;
}

.search-panel::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 3px;
}

.search-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.search-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox scrollbar */
.search-panel {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f0f0f0;
}

.search-header {
    background-color: #344952;
    color: #ffffff;
    padding: 10px;
    text-align: center;
    font-weight: bold;
}

.el-collapse {
    width: 275px !important;
}

.el-collapse-item__header {
    background-color: #F7F9FA !important;
    color: #344952;
    padding-left: 20px !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    height: 40px !important;
    line-height: 40px !important;
}

.el-collapse-item__wrap {
    background-color: #f0f2f5 !important;
}

.el-collapse-item__content {
    padding: 10px 15px !important;
}

.input-group {
    margin-bottom: 15px;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 6px;
    color: #5a6268;
    font-size: 11px;
    font-weight: 400;
    text-align: left;
}

.text-input {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 5px;
    height: 36px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.search-footer {
    padding: 10px;
    background-color: #F7F9FA;
    border-top: 1px solid #ddd;
    margin-top: auto;
    width: 100%;
    box-sizing: border-box;
}

.search-input-group {
    display: flex;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
}

.search-input-group label {
    margin-right: 5px;
    white-space: nowrap;
    font-size: 11px;
}

.search-input-group input:not(.text-input) {
    flex: 1;
    margin-right: 5px;
    padding: 4px;
    border: 1px solid #ccc;
    border-radius: 3px;
    min-width: 0;
}

button {
    background-color: #344952;
    color: #ffffff;
    border: none;
    padding: 4px 10px;
    cursor: pointer;
    border-radius: 3px;
    white-space: nowrap;
    margin: 0 5px;
}

.clear-button {
    background-color: #f5f5f5;
    color: #344952;
    border: 1px solid #ddd;
}

.spatial-options {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
}

.spatial-options label {
    margin-bottom: 5px;
    font-size: 11px;
}

.age-range-container {
    margin-bottom: 15px;
}

.age-label {
    display: block;
    margin-bottom: 6px;
    color: #5a6268;
    font-size: 11px;
    font-weight: 400;
    text-align: left;
}

.age-inputs {
    display: flex;
    align-items: center;
}

.age-input {
    flex: 1;
    margin-right: 5px;
    margin-bottom: 0;
}

.age-separator,
.age-unit {
    margin: 0 5px;
    color: #5a6268;
    font-size: 12px;
}

.location-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.location-label {
    margin-right: 10px;
    font-size: 11px;
    color: #5a6268;
}

.location-input-container {
    margin-right: 10px;
}

.location-input {
    width: 60px;
    height: 36px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

.location-slider-container {
    margin-bottom: 20px;
}

.location-slider {
    width: 100%;
    height: 5px;
    margin: 0;
    padding: 0;
    border: none;
    border-radius: 5px;
    background-color: #ccc;
    -webkit-appearance: none;
    appearance: none;
}

.location-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #344952;
    cursor: pointer;
}

.location-slider::-moz-range-thumb {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #344952;
    cursor: pointer;
}

/* Make all inputs in the panel fit within the sidebar width, except those with text-input class */
input[type="text"]:not(.text-input) {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 5px;
    height: 36px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.saved-search-selector {
    padding: 10px 15px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #f7f7f7;
}

.el-select .el-input__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.map-selection {
    color: #409EFF;
    font-weight: bold;
}

/* Autocomplete dropdown styling */
.my-autocomplete {
    width: 100%;
}

.el-autocomplete {
    width: 100%;
    display: block;
}

.el-autocomplete .el-input__inner {
    height: 36px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
}

.el-autocomplete-suggestion__list {
    max-height: 200px;
    overflow-y: auto;
}

.el-autocomplete-suggestion li {
    padding: 8px 12px;
    line-height: 1.2;
    font-size: 14px;
}

.el-autocomplete-suggestion li:hover {
    background-color: #f5f7fa;
}

.el-autocomplete-suggestion li.highlighted {
    background-color: #f5f7fa;
}

/* El-select styling */
.el-select {
    width: 100%;
}

.el-select .el-input__inner {
    height: 36px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
}

/* Refresh icon styling */
.refresh-icon {
    margin-left: 8px;
    font-size: 14px;
    cursor: pointer;
    color: #909399;
    transition: all 0.3s;
}

.refresh-icon:hover {
    color: #409EFF;
    transform: rotate(90deg);
}

.refresh-icon.is-loading {
    animation: rotating 2s linear infinite;
    color: #909399;
}

.refresh-icon.is-success {
    color: #67C23A;
    /* Element UI success color */
    animation: none;
}

.refresh-icon.is-error {
    color: #F56C6C;
    /* Element UI error color */
    animation: none;
}

@keyframes rotating {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
