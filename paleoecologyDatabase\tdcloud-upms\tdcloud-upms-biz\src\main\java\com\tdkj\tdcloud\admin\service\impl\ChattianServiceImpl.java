/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.ChattianDTO;
import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XSpecimen;
import com.tdkj.tdcloud.admin.api.vo.ChattianExcelVO;
import com.tdkj.tdcloud.admin.mapper.ChattianMapper;
import com.tdkj.tdcloud.admin.service.ChattianService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.vo.ErrorMessage;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 13:42:23
 */
@Service
public class ChattianServiceImpl extends ServiceImpl<ChattianMapper, Chattian> implements ChattianService {

	@Resource
	private ChattianMapper chattianMapper;

	@Override
	public R importChattian(List<ChattianExcelVO> excelVOList, BindingResult bindingResult) throws Exception {
		// 通用校验获取失败的数据
		List<ErrorMessage> errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();

		try {
			// 执行数据插入操作 组装
			for (ChattianExcelVO excel : excelVOList) {

//				KibSpecimenVO specimenBySpeciesLatin = kibSpecimenMapper.getSpecimenBySpeciesLatin(excel.getSpeciesLatin());
//				if (specimenBySpeciesLatin==null){


				chattianMapper.insertChattianExcel(excel);

				}

		} catch (Exception e) {
			// 这里可以将具体的异常信息记录到日志文件中，以便后续排查问题
			log.error("导入数据时出错: ", e);
			throw new Exception("导入数据失败，请查看日志获取详细信息");
		}

		if (CollUtil.isNotEmpty(errorMessageList)) {
			return R.failed(errorMessageList);
		}
		return R.ok();
	}

	@Override
	public Page getChattianPage(Page page, ChattianDTO chattianDTO) {
		//查询条件
		QueryWrapper<Chattian> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}

		wrapper.eq(StringUtils.isNotBlank(chattianDTO.getId()), "ID", chattianDTO.getId());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getOriginalname()), "OriginalName", chattianDTO.getOriginalname());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getScientificname1()), "ScientificName1", chattianDTO.getScientificname1());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getScientificname2()), "ScientificName2", chattianDTO.getScientificname2());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getScientificname3()), "ScientificName3", chattianDTO.getScientificname3());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getAcceptedrank()), "AcceptedRank", chattianDTO.getAcceptedrank());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getPhylum()), "Phylum", chattianDTO.getPhylum());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getClassNew()), "class_new", chattianDTO.getClassNew());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getOrderNew()), "order_new", chattianDTO.getOrderNew());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getFamily()), "Family", chattianDTO.getFamily());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getGenus()), "Genus", chattianDTO.getGenus());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getSpecies1()), "Species1", chattianDTO.getSpecies1());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getSpecies2()), "Species2", chattianDTO.getSpecies2());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getSpecies3()), "Species3", chattianDTO.getSpecies3());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getPlantorgan1()), "PlantOrgan1", chattianDTO.getPlantorgan1());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getPlantorgan2()), "PlantOrgan2", chattianDTO.getPlantorgan2());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getAbundvalue()), "AbundValue", chattianDTO.getAbundvalue());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getAbundunit()), "AbundUnit", chattianDTO.getAbundunit());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getFossiltype()), "FossilType", chattianDTO.getFossiltype());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getPollendiagram()), "PollenDiagram", chattianDTO.getPollendiagram());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getAssemblage()), "Assemblage", chattianDTO.getAssemblage());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getSiteno()), "SiteNo", chattianDTO.getSiteno());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getSitename()), "SiteName", chattianDTO.getSitename());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getExtinct()), "Extinct", chattianDTO.getExtinct());
		wrapper.like(StringUtils.isNotBlank(chattianDTO.getTimecontext()), "TimeContext", chattianDTO.getTimecontext());

//		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}

	@Override
	public R getChattianById(Integer id) {
		Chattian chattian = chattianMapper.selectChattianById(id);
		return R.ok(chattian,"子表详情");
	}

	@Override
	public R getChattianByCId(Integer cId) {
		Chattian chattian = chattianMapper.selectChattianByCid(Long.valueOf(cId));
		return R.ok(chattian,"子表详情");
	}
}
