<template>
  <div class="register-panle">
    <div class="register-panle-title">{{$t('nav.paleoecologyNav')}}</div>
    <el-form ref="registerForm" class="registerForm" :model="registerForm" :rules="registerRules">
      <div class="form_item">
        <el-form-item prop="name">
          <el-input v-model="registerForm.name" :placeholder="$t('login.namePlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="form_item email-group">
        <el-form-item prop="username" class="email-group-item">
          <el-input  @blur="validateEmail"  v-model="registerForm.username" :placeholder="$t('login.emailPlaceholder')"></el-input>
        </el-form-item>
        <el-button class="email-group-btn" type="primary"  :disabled="isDisabled || !isEmailValid" @click="sendVerificationCode">
          <span v-if="countdown > 0">{{ countdown }} 秒</span>
          <span v-else>{{ $t('login.codeText') }}</span>
        </el-button>
      </div>
      <div class="form_item">
        <el-form-item prop="code">
          <el-input v-model="registerForm.code"  :placeholder="$t('login.codePlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="password">
          <el-input
              auto-complete="off"
              v-model="registerForm.password"
              :type="passwordType"
              :placeholder="$t('login.passPlaceholder')">
            <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
            <i slot="prefix" class="icon-mima icon"></i>
          </el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="repassword">
          <el-input v-model="registerForm.repassword" type="password"  :placeholder="$t('login.rePassPlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="unit">
          <el-input v-model="registerForm.unit" :placeholder="$t('login.utilPlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="phone">
          <el-input v-model="registerForm.phone" :placeholder="$t('login.phonePlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="register-btns">
        <div class="btn cancel" @click="cancelHandle">{{ $t('login.cancelBtn') }}</div>
        <div class="btn login" @click="handleRegister">{{ $t('login.createAccount') }}</div>
      </div>
      <div class="register_link">
        <span>{{ $t('login.registerNote') }}</span>
        <div  @click="goToLogin">{{ $t('login.toLogin') }}</div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { rule } from "@/util/validateRules";
import {mapMutations} from "vuex";
import {getEmailCode, userRegister} from "@/api/login";
import webiste from "@/util/website";
import {aesEncrypt} from "@/util/encryption";

export default {
  name: "RegisterPanle",
  data() {
    return {
      countdown: 0,      // 倒计时秒数
      isEmailValid: false, // 邮箱是否有效
      timer: null ,       // 计时器对象
      registerForm: {
        name: '',
        username: '',
        code: '',
        password: '',
        repassword: '',
        unit: '',
        phone: '',
      },
      registerRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'}
        ],
        username: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {validator: rule.validatorEmail,trigger: 'blur'}
        ],
        code: [
          { required: true, message: '验证码不能为空', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'}
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {validator: rule.validatorPassword,trigger: 'blur'}

        ],
        repassword: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          {validator: rule.checkSpace,trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value !== this.registerForm.password) {
                callback(new Error('两次输入的密码不一致!'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change'] // 在输入和字段变化时都触发验证
          }
        ],
      },
      passwordType: "password",
    }
  },
  computed: {
    isDisabled() {
      return this.countdown > 0
    }
  },
  methods: {
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    ...mapMutations(['SET_OPEN_REGISTER_BOX']),
    cancelHandle() {
      this.$refs.registerForm.resetFields();
      this.SET_OPEN_REGISTER_BOX(false);
    },
    showPassword() {
      this.passwordType === ""
          ? (this.passwordType = "password")
          : (this.passwordType = "");
    },
    validateName() {
      const pattern = /^[\u4e00-\u9fa5a-zA-Z]{2,}$/; // 2位以上中文或英文
      return pattern.test(this.registerForm.name.trim());
    },
    // 邮箱格式验证
    validateEmail() {
      const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      this.isEmailValid = pattern.test(this.registerForm.username)
      return this.isEmailValid
    },
    // 发送验证码
    sendVerificationCode() {
      if (!this.validateEmail()) {
        this.$message.error('请输入有效的邮箱地址')
        return
      }
      if (!this.validateName()) {
        this.$message.error('姓名需至少2个字符（仅支持中文或英文）');
        return;
      }
      this.startCountdown()
      // TODO 调用API发送验证码（示例）
      getEmailCode(this.registerForm.username,this.registerForm.name, 'register').then(res => {
        if(res.data.code === 0) {
          this.$message.success(res.data.data)
        }else {
          this.$message.warning(res.data.msg)
        }
      })

    },

    // 开始倒计时
    startCountdown(seconds = 60) {
      // 清除已有计时器
      if (this.timer) clearInterval(this.timer)
      // 设置初始值
      this.countdown = seconds
      // 启动计时器
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          // TODO 密码加密
          if (webiste.passwordEnc) {
            this.registerForm.password = aesEncrypt(this.registerForm.password, webiste.encPassword)
            this.registerForm.repassword = aesEncrypt(this.registerForm.repassword, webiste.encPassword)
          }
          //TODO 调用注册接口
          userRegister(this.registerForm).then(res =>{
            if(res.data.code === 0) {
              this.$message.success('注册成功！')
              this.SET_OPEN_LOGIN_BOX(true)
              this.SET_OPEN_REGISTER_BOX(false)
            }else {
              this.$message.warning(res.data.msg)
              this.registerForm.password = ''
              this.registerForm.repassword = ''
            }
          })

        } else {
          return false
        }
      })
    },
    goToLogin() {
      // 跳转到登录页面
      this.SET_OPEN_LOGIN_BOX(true)
      this.SET_OPEN_REGISTER_BOX(false)

    },
  },
  beforeDestroy() {
    // 组件销毁时清除计时器
    if (this.timer) clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
.register-panle {
  position: fixed;
  top: 150px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  //width: 404px;
  width: 480px;
  height: auto;
  background: #FFFFFF;
  border-radius: 6px ;
  .register-panle-title {
    margin-top: 40px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 32px;
    color: var(--primary-text-color);
    margin-bottom: 40px;
  }
  .registerForm {
    padding: 0 34px;
    .email-group {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 22px;
      .email-group-item {
        margin-bottom: 0;
        width: 68%;
        margin-right: 10px;
      }
      .email-group-btn {
        width: calc(100% - 68% - 10px);
        padding: 12px 8px !important;
      }

    }
    .register-btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .btn {
        width: calc((100% - 10px) / 2);
        height: 50px;
        line-height: 50px;
        text-align: center;
        background: #D5E2E5;
        border-radius: 4px;
        color: var(--primary-text-color);
        cursor: pointer;
      }
      .login {
        background-color: var(--primary-text-color);
        color: #ffffff;
      }
    }
    .register_link {
      text-align: center;
      font-size: 15px;
      color: #86909C;
      margin-bottom: 40px;

      div {
        display: inline-block;
        color: var(--primary-text-color);
        text-decoration: none;
        margin-left: 5px;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

}
::v-deep .el-input__inner {
  padding: 0 15px !important;
}
</style>