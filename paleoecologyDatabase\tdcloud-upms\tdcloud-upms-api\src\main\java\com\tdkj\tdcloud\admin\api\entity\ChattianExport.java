/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:20:56
 */
@Data
@TableName("chattian")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class ChattianExport extends Model<ChattianExport> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description="id")
    private String id;

    /**
     * originalname
     */
    @Schema(description="originalname")
    private String originalname;

    /**
     * scientificname1
     */
    @Schema(description="scientificname1")
    private String scientificname1;

    /**
     * scientificname2
     */
    @Schema(description="scientificname2")
    private String scientificname2;

    /**
     * scientificname3
     */
    @Schema(description="scientificname3")
    private String scientificname3;

    /**
     * acceptedrank
     */
    @Schema(description="acceptedrank")
    private String acceptedrank;

    /**
     * phylum
     */
    @Schema(description="phylum")
    private String phylum;

    /**
     * classNew
     */
    @Schema(description="classNew")
    private String classNew;

    /**
     * orderNew
     */
    @Schema(description="orderNew")
    private String orderNew;

    /**
     * family
     */
    @Schema(description="family")
    private String family;

    /**
     * genus
     */
    @Schema(description="genus")
    private String genus;

    /**
     * species1
     */
    @Schema(description="species1")
    private String species1;

    /**
     * species2
     */
    @Schema(description="species2")
    private String species2;

    /**
     * species3
     */
    @Schema(description="species3")
    private String species3;

    /**
     * plantorgan1
     */
    @Schema(description="plantorgan1")
    private String plantorgan1;

    /**
     * plantorgan2
     */
    @Schema(description="plantorgan2")
    private String plantorgan2;

    /**
     * abundvalue
     */
    @Schema(description="abundvalue")
    private String abundvalue;

    /**
     * abundunit
     */
    @Schema(description="abundunit")
    private String abundunit;

    /**
     * fossiltype
     */
    @Schema(description="fossiltype")
    private String fossiltype;

    /**
     * pollendiagram
     */
    @Schema(description="pollendiagram")
    private String pollendiagram;

    /**
     * assemblage
     */
//    @Schema(description="assemblage")
//    private String assemblage;

    /**
     * siteno
     */
    @Schema(description="siteno")
    private String siteno;

    /**
     * sitename
     */
    @Schema(description="sitename")
    private String sitename;

    /**
     * extinct
     */
    @Schema(description="extinct")
    private String extinct;

    /**
     * timecontext
     */
    @Schema(description="timecontext")
    private String timecontext;

    /**
     * cid
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="cid")
    private Integer cid;

}
