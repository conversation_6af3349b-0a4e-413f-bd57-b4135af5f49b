package com.tdkj.tdcloud.admin.controller;

import com.tdkj.tdcloud.admin.api.entity.EmailSender;
import com.tdkj.tdcloud.admin.service.MailSenderService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequiredArgsConstructor
@Tag(description  = "mailSender", name =  "发送邮箱验证码")
@RequestMapping("/mailSender")
public class MailSenderController {

	@Resource
	private MailSenderService mailSenderService;


	@Inner(value = false)
	@Operation(summary = "发送邮箱", description = "发送邮箱")
	@PostMapping("/getEmailCode")
	public R getEmailCode(@RequestBody EmailSender emailSender){

		return mailSenderService.sendSimpleMail(emailSender);
	}

}
