<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdkj.tdcloud.admin.mapper.XSpecimenTreeMapper">
    
    <resultMap type="com.tdkj.tdcloud.admin.api.entity.XSpecimenTree" id="XSpecimenTreeResult">
        <result property="id"    column="id"    />
        <result property="nameEn"    column="name_en"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createTime"    column="create_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="parentEn"    column="parent_en"    />
        <result property="belongLevel"    column="belong_level"    />
        <result property="languageType"    column="language_type"    />
    </resultMap>

    <sql id="selectXSpecimenTreeVo">
        select id, name_en, name_cn, sort_order, create_time, del_flag, parent_en, belong_level, language_type from x_specimen_tree
    </sql>

    <select id="selectXSpecimenTreeList" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimenTree" resultMap="XSpecimenTreeResult">
        <include refid="selectXSpecimenTreeVo"/>
        <where>  
            <if test="nameEn != null  and nameEn != ''"> and name_en = #{nameEn}</if>
            <if test="nameCn != null  and nameCn != ''"> and name_cn = #{nameCn}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="parentEn != null  and parentEn != ''"> and parent_en = #{parentEn}</if>
            <if test="belongLevel != null  and belongLevel != ''"> and belong_level = #{belongLevel}</if>
            <if test="languageType != null  and languageType != ''"> and language_type = #{languageType}</if>
        </where>
    </select>
    
    <select id="selectXSpecimenTreeById" parameterType="Long" resultMap="XSpecimenTreeResult">
        <include refid="selectXSpecimenTreeVo"/>
        where id = #{id}
    </select>

	<select id="getSpecimenTreeByNameEn" resultMap="XSpecimenTreeResult">
		<include refid="selectXSpecimenTreeVo"/>
		where name_en = #{nameEn} and parent_en =#{parentEn}
	</select>

	<select id="getSpecimenTreeByParentEn" resultMap="XSpecimenTreeResult">
		<include refid="selectXSpecimenTreeVo"/>
		where parent_en =#{parentEn}
		ORDER BY LEFT(name_en, 1) ASC
	</select>

    <insert id="insertXSpecimenTree" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimenTree" useGeneratedKeys="true" keyProperty="id">
        insert into x_specimen_tree
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nameEn != null">name_en,</if>
            <if test="nameCn != null">name_cn,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createTime != null">create_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="parentEn != null">parent_en,</if>
            <if test="belongLevel != null">belong_level,</if>
            <if test="languageType != null">language_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nameEn != null">#{nameEn},</if>
            <if test="nameCn != null">#{nameCn},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="parentEn != null">#{parentEn},</if>
            <if test="belongLevel != null">#{belongLevel},</if>
            <if test="languageType != null">#{languageType},</if>
         </trim>
    </insert>

	<insert id="batchInsertXSpecimenTree" parameterType="java.util.List">
		INSERT INTO x_specimen_tree (name_en, name_cn, create_time, parent_en, belong_level, language_type,sort_order,del_flag)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.nameEn}, #{item.nameCn}, #{item.createTime}, #{item.parentEn}, #{item.belongLevel}, 'en',0,'0')
		</foreach>
	</insert>

    <update id="updateXSpecimenTree" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimenTree">
        update x_specimen_tree
        <trim prefix="SET" suffixOverrides=",">
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="parentEn != null">parent_en = #{parentEn},</if>
            <if test="belongLevel != null">belong_level = #{belongLevel},</if>
            <if test="languageType != null">language_type = #{languageType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXSpecimenTreeById" parameterType="Long">
        delete from x_specimen_tree where id = #{id}
    </delete>

    <delete id="deleteXSpecimenTreeByIds" parameterType="String">
        delete from x_specimen_tree where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>