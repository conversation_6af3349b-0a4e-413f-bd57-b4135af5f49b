2025-06-11 14:27:47,761 [background-preinit] INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 14:27:47,765 [main] INFO  [com.tdkj.tdcloud.TdcloudAdminApplication] StartupInfoLogger.java:55 - Starting TdcloudAdminApplication using Java 1.8.0_362 on ink with PID 15640 (D:\ui\古生物\github\paleoecologyDatabase\tdcloud-upms\tdcloud-upms-biz\target\classes started by 18487 in D:\ui\古生物\github\paleoecologyDatabase)
2025-06-11 14:27:47,765 [main] INFO  [com.tdkj.tdcloud.TdcloudAdminApplication] SpringApplication.java:637 - The following 2 profiles are active: "dev", "job"
2025-06-11 14:27:48,481 [main] INFO  [o.s.context.annotation.AutoProxyRegistrar] AutoProxyRegistrar.java:83 - AutoProxyRegistrar was imported but no annotations were found having both 'mode' and 'proxyTargetClass' attributes of type AdviceMode and boolean respectively. This means that auto proxy creator registration and configuration may not have occurred as intended, and components may not be proxied as expected. Check to ensure that AutoProxyRegistrar has been @Import'ed on the same class where these annotations are declared; otherwise remove the import of AutoProxyRegistrar altogether.
2025-06-11 14:27:48,790 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 14:27:48,792 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 14:27:48,831 [main] INFO  [o.s.d.r.config.RepositoryConfigurationDelegate] RepositoryConfigurationDelegate.java:201 - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-11 14:27:49,039 [main] INFO  [o.springframework.cloud.context.scope.GenericScope] GenericScope.java:283 - BeanFactory id=2ff8eb41-0d28-3361-b0a5-9ceca3ebb9eb
2025-06-11 14:27:49,092 [main] INFO  [c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor] EnableEncryptablePropertiesBeanFactoryPostProcessor.java:40 - Post-processing PropertySource instances
2025-06-11 14:27:49,092 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:76 - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-11 14:27:49,093 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:76 - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-11 14:27:49,093 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:76 - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-11 14:27:49,094 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,094 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource Config resource 'class path resource [application-job.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,095 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource openapi-config.yaml [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:27:49,294 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-11 14:27:49,296 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-11 14:27:49,298 [main] INFO  [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] PostProcessorRegistrationDelegate.java:376 - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-11 14:27:49,337 [main] INFO  [c.u.j.filter.DefaultLazyPropertyFilter] DefaultLazyPropertyFilter.java:31 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-06-11 14:27:49,342 [main] INFO  [c.u.j.resolver.DefaultLazyPropertyResolver] DefaultLazyPropertyResolver.java:35 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-06-11 14:27:49,343 [main] INFO  [c.u.j.detector.DefaultLazyPropertyDetector] DefaultLazyPropertyDetector.java:35 - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-06-11 14:27:49,970 [main] WARN  [io.undertow.websockets.jsr] Bootstrap.java:68 - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-11 14:27:49,989 [main] INFO  [io.undertow.servlet] ServletContextImpl.java:388 - Initializing Spring embedded WebApplicationContext
2025-06-11 14:27:49,989 [main] INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:292 - Root WebApplicationContext: initialization completed in 2157 ms
2025-06-11 14:27:50,064 [main] INFO  [c.a.d.s.b.a.DruidDataSourceAutoConfigure] DruidDataSourceAutoConfigure.java:56 - Init DruidDataSource
2025-06-11 14:27:50,247 [main] INFO  [com.alibaba.druid.pool.DruidDataSource] DruidDataSource.java:995 - {dataSource-1} inited
2025-06-11 14:27:50,433 [main] INFO  [org.quartz.ee.servlet.QuartzInitializerListener] QuartzInitializerListener.java:147 - Quartz Initializer Servlet loaded, initializing Scheduler...
2025-06-11 14:27:50,443 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1220 - Using default implementation for ThreadExecutor
2025-06-11 14:27:50,444 [main] INFO  [org.quartz.simpl.SimpleThreadPool] SimpleThreadPool.java:268 - Job execution threads will use class loader of thread: main
2025-06-11 14:27:50,449 [main] INFO  [org.quartz.core.SchedulerSignalerImpl] SchedulerSignalerImpl.java:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-11 14:27:50,450 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:229 - Quartz Scheduler v.2.3.2 created.
2025-06-11 14:27:50,450 [main] INFO  [org.quartz.simpl.RAMJobStore] RAMJobStore.java:155 - RAMJobStore initialized.
2025-06-11 14:27:50,451 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-11 14:27:50,452 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1374 - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-06-11 14:27:50,452 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1378 - Quartz scheduler version: 2.3.2
2025-06-11 14:27:50,452 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:547 - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-06-11 14:27:50,452 [main] INFO  [org.quartz.ee.servlet.QuartzInitializerListener] QuartzInitializerListener.java:199 - Scheduler has been started...
2025-06-11 14:27:50,452 [main] INFO  [org.quartz.ee.servlet.QuartzInitializerListener] QuartzInitializerListener.java:217 - Storing the Quartz Scheduler Factory in the servlet context at key: org.quartz.impl.StdSchedulerFactory.KEY
2025-06-11 14:27:55,691 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:347 - Can not find table primary key in Class: "com.tdkj.tdcloud.admin.api.entity.SysDeptRelation".
2025-06-11 14:27:55,692 [main] WARN  [c.t.t.common.data.datascope.DataScopeSqlInjector] DefaultSqlInjector.java:56 - class com.tdkj.tdcloud.admin.api.entity.SysDeptRelation ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-11 14:27:55,768 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:347 - Can not find table primary key in Class: "com.tdkj.tdcloud.admin.api.entity.SysUserRole".
2025-06-11 14:27:55,768 [main] WARN  [c.t.t.common.data.datascope.DataScopeSqlInjector] DefaultSqlInjector.java:56 - class com.tdkj.tdcloud.admin.api.entity.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-11 14:27:56,645 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:347 - Can not find table primary key in Class: "com.tdkj.tdcloud.admin.api.entity.SysRoleMenu".
2025-06-11 14:27:56,645 [main] WARN  [c.t.t.common.data.datascope.DataScopeSqlInjector] DefaultSqlInjector.java:56 - class com.tdkj.tdcloud.admin.api.entity.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-11 14:27:56,801 [main] WARN  [c.b.mybatisplus.core.metadata.TableInfoHelper] TableInfoHelper.java:347 - Can not find table primary key in Class: "com.tdkj.tdcloud.admin.api.entity.SysUserPost".
2025-06-11 14:27:56,801 [main] WARN  [c.t.t.common.data.datascope.DataScopeSqlInjector] DefaultSqlInjector.java:56 - class com.tdkj.tdcloud.admin.api.entity.SysUserPost ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-11 14:27:57,359 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1220 - Using default implementation for ThreadExecutor
2025-06-11 14:27:57,359 [main] INFO  [org.quartz.simpl.SimpleThreadPool] SimpleThreadPool.java:268 - Job execution threads will use class loader of thread: main
2025-06-11 14:27:57,361 [main] INFO  [org.quartz.core.SchedulerSignalerImpl] SchedulerSignalerImpl.java:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-11 14:27:57,361 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:229 - Quartz Scheduler v.2.3.2 created.
2025-06-11 14:27:57,362 [main] INFO  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:672 - Using db table-based data access locking (synchronization).
2025-06-11 14:27:57,363 [main] INFO  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreCMT.java:145 - JobStoreCMT initialized.
2025-06-11 14:27:57,363 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'clusteredScheduler' with instanceId 'ink1749623277359'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 50 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-06-11 14:27:57,363 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1374 - Quartz scheduler 'clusteredScheduler' initialized from an externally provided properties instance.
2025-06-11 14:27:57,363 [main] INFO  [org.quartz.impl.StdSchedulerFactory] StdSchedulerFactory.java:1378 - Quartz scheduler version: 2.3.2
2025-06-11 14:27:57,364 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:2293 - JobFactory set to: com.tdkj.tdcloud.quartz.config.AutowireCapableBeanJobFactory@58afecfd
2025-06-11 14:27:57,435 [main] INFO  [c.a.c.config.AjCaptchaServiceAutoConfiguration] AjCaptchaServiceAutoConfiguration.java:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
2025-06-11 14:27:57,437 [main] INFO  [c.anji.captcha.service.impl.CaptchaServiceFactory] CaptchaServiceFactory.java:52 - supported-captchaCache-service:[redis, local]
2025-06-11 14:27:57,439 [main] INFO  [c.anji.captcha.service.impl.CaptchaServiceFactory] CaptchaServiceFactory.java:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
2025-06-11 14:27:57,452 [main] INFO  [com.anji.captcha.util.ImageUtils] ImageUtils.java:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@1abf951a, ORIGINAL=[Ljava.lang.String;@629600d7, PIC_CLICK=[Ljava.lang.String;@180d918a]
2025-06-11 14:27:57,452 [main] INFO  [c.a.c.service.impl.ClickWordCaptchaServiceImpl] AbstractCaptchaService.java:76 - --->>>初始化验证码底图<<<---clickWord
2025-06-11 14:27:57,550 [main] INFO  [c.a.c.service.impl.ClickWordCaptchaServiceImpl] AbstractCaptchaService.java:92 - 初始化local缓存...
2025-06-11 14:27:57,614 [main] INFO  [com.anji.captcha.util.ImageUtils] ImageUtils.java:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@78ea700f, ORIGINAL=[Ljava.lang.String;@cd93621, PIC_CLICK=[Ljava.lang.String;@3b1137b0]
2025-06-11 14:27:57,614 [main] INFO  [c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl] AbstractCaptchaService.java:76 - --->>>初始化验证码底图<<<---blockPuzzle
2025-06-11 14:27:57,674 [main] INFO  [c.a.c.service.impl.BlockPuzzleCaptchaServiceImpl] AbstractCaptchaService.java:92 - 初始化local缓存...
2025-06-11 14:27:57,856 [main] INFO  [o.s.security.web.DefaultSecurityFilterChain] DefaultSecurityFilterChain.java:55 - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1bba9862, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@451a4187, org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.AuthorizationServerContextFilter@6e12f38c, org.springframework.security.web.context.SecurityContextPersistenceFilter@49741274, org.springframework.security.web.header.HeaderWriterFilter@214e3185, org.springframework.security.web.authentication.logout.LogoutFilter@1abbc1d4, org.springframework.security.oauth2.server.authorization.web.OAuth2AuthorizationServerMetadataEndpointFilter@2a0ce342, org.springframework.security.oauth2.server.authorization.web.OAuth2AuthorizationEndpointFilter@121dac1a, org.springframework.security.oauth2.server.authorization.web.OAuth2ClientAuthenticationFilter@1e5e3147, com.tdkj.tdcloud.auth.support.handler.TdcloudLoginPreFilter@d969452, org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationFilter@49fb693d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68b3979d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@587400a9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@565c887e, org.springframework.security.web.session.SessionManagementFilter@25109d84, org.springframework.security.web.access.ExceptionTranslationFilter@2e847e71, org.springframework.security.oauth2.server.authorization.web.OAuth2TokenEndpointFilter@18df26a1, org.springframework.security.oauth2.server.authorization.web.OAuth2TokenIntrospectionEndpointFilter@a5d23c9, org.springframework.security.oauth2.server.authorization.web.OAuth2TokenRevocationEndpointFilter@1f1ffc18, org.springframework.security.web.access.intercept.AuthorizationFilter@5cba474f]
2025-06-11 14:27:57,868 [main] INFO  [o.s.security.web.DefaultSecurityFilterChain] DefaultSecurityFilterChain.java:55 - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@73c09a98, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6f2bf657, org.springframework.security.web.context.SecurityContextPersistenceFilter@f287a4e, org.springframework.security.web.header.HeaderWriterFilter@280c3dc0, org.springframework.security.web.authentication.logout.LogoutFilter@1fa796a4, org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationFilter@50f13494, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3879feec, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71d2261e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27d6467, org.springframework.security.web.session.SessionManagementFilter@6f89ad03, org.springframework.security.web.access.ExceptionTranslationFilter@497fd334, org.springframework.security.web.access.intercept.AuthorizationFilter@8dcacf1]
2025-06-11 14:27:59,759 [main] WARN  [o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger] LoadBalancerCacheAutoConfiguration.java:82 - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-11 14:27:59,766 [main] INFO  [o.s.b.actuate.endpoint.web.EndpointLinksResolver] EndpointLinksResolver.java:58 - Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-11 14:27:59,891 [main] INFO  [io.undertow] Undertow.java:120 - starting server: Undertow - 2.2.22.Final
2025-06-11 14:27:59,897 [main] INFO  [org.xnio] Xnio.java:95 - XNIO version 3.8.7.Final
2025-06-11 14:27:59,903 [main] INFO  [org.xnio.nio] NioXnio.java:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-06-11 14:27:59,939 [main] INFO  [org.jboss.threads] Version.java:52 - JBoss Threads version 3.1.0.Final
2025-06-11 14:28:00,037 [main] INFO  [o.s.boot.web.embedded.undertow.UndertowWebServer] UndertowWebServer.java:119 - Undertow started on port(s) 8888 (http) with context path '/admin'
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.caching.RefreshScopeRefreshedEventListener] RefreshScopeRefreshedEventListener.java:47 - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source systemProperties refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source systemEnvironment refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source random refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source cachedrandom refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source springCloudClientHostInfo refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source Config resource 'class path resource [application-job.yml]' via location 'optional:classpath:/' refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' refreshed
2025-06-11 14:28:00,038 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.c.CachingDelegateEncryptablePropertySource] CachingDelegateEncryptablePropertySource.java:55 - Property Source openapi-config.yaml refreshed
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:76 - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:76 - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-06-11 14:28:00,039 [main] INFO  [c.u.j.EncryptablePropertySourceConverter] EncryptablePropertySourceConverter.java:81 - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-06-11 14:28:00,782 [main] INFO  [o.s.scheduling.quartz.SchedulerFactoryBean] SchedulerFactoryBean.java:729 - Starting Quartz Scheduler now
2025-06-11 14:28:00,815 [main] INFO  [org.quartz.core.QuartzScheduler] QuartzScheduler.java:547 - Scheduler clusteredScheduler_$_ink1749623277359 started.
2025-06-11 14:28:00,817 [task-1] DEBUG [c.t.t.admin.mapper.SysTenantMapper.selectList] BaseJdbcLogger.java:137 - ==>  Preparing: SELECT id, name, code, tenant_domain, start_time, end_time, status, menu_id, create_by, update_by, del_flag, create_time, update_time FROM sys_tenant WHERE del_flag = '0'
2025-06-11 14:28:00,821 [task-1] DEBUG [c.t.t.admin.mapper.SysTenantMapper.selectList] BaseJdbcLogger.java:137 - ==> Parameters: 
2025-06-11 14:28:00,829 [task-1] DEBUG [c.t.t.admin.mapper.SysTenantMapper.selectList] BaseJdbcLogger.java:137 - <==      Total: 1
2025-06-11 14:28:00,838 [task-1] DEBUG [c.t.t.a.m.SysOauthClientDetailsMapper.selectList] BaseJdbcLogger.java:137 - ==>  Preparing: SELECT id, client_id, client_secret, resource_ids, scope, authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, refresh_token_validity, additional_information, autoapprove, del_flag, create_by, update_by, create_time, update_time FROM sys_oauth_client_details WHERE del_flag = '0' AND tenant_id = 1
2025-06-11 14:28:00,838 [task-1] DEBUG [c.t.t.a.m.SysOauthClientDetailsMapper.selectList] BaseJdbcLogger.java:137 - ==> Parameters: 
2025-06-11 14:28:00,860 [task-1] DEBUG [c.t.t.a.m.SysOauthClientDetailsMapper.selectList] BaseJdbcLogger.java:137 - <==      Total: 7
2025-06-11 14:28:01,663 [main] INFO  [com.tdkj.tdcloud.TdcloudAdminApplication] StartupInfoLogger.java:61 - Started TdcloudAdminApplication in 14.652 seconds (JVM running for 20.361)
2025-06-11 14:28:02,123 [RMI TCP Connection(2)-192.168.31.210] INFO  [io.undertow.servlet] ServletContextImpl.java:388 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 14:28:02,124 [RMI TCP Connection(2)-192.168.31.210] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:525 - Initializing Servlet 'dispatcherServlet'
2025-06-11 14:28:02,125 [RMI TCP Connection(2)-192.168.31.210] INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:547 - Completed initialization in 1 ms
2025-06-11 14:28:10,810 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:28:20,817 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:28:30,828 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:28:40,833 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:28:50,838 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:29:00,847 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:29:10,851 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:29:20,861 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:29:30,868 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
2025-06-11 14:29:40,872 [QuartzScheduler_clusteredScheduler-ink1749623277359_ClusterManager] WARN  [o.s.scheduling.quartz.LocalDataSourceJobStore] JobStoreSupport.java:3411 - This scheduler instance (ink1749623277359) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.
