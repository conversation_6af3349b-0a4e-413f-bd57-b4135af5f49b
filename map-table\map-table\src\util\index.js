import {request} from "../api/request";
import {Message} from "element-ui";
import * as CryptoJS from 'crypto-js'

/**
 *加密处理
 */
export const encryption = params => {
    let { key } = params
    const { param } = params
    const result = JSON.parse(JSON.stringify(params.data))
    if (params.type === 'Base64') {
        param.forEach(ele => {
            result[ele] = btoa(result[ele])
        })
    } else {
        param.forEach(ele => {
            var data = result[ele]
            key = CryptoJS.enc.Latin1.parse(key)
            var iv = key
            // 加密
            var encrypted = CryptoJS.AES.encrypt(data, key, {
                iv: iv,
                mode: CryptoJS.mode.CFB,
                padding: CryptoJS.pad.NoPadding
            })
            result[ele] = encrypted.toString()
        })
    }
    return result
}
export function downBlobFile(url, query, fileName) {
    return request({
        url: url,
        method: "get",
        responseType: "blob",
        params: query
    }).then(response => {
        // 处理返回的文件流
        const blob = response.data;
        if (blob && blob.size === 0) {
            Message.error("内容为空，无法下载");
            return;
        }
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
        }, 0);
    });
}
