package com.tdkj.tdcloud.admin.mapper;

import java.util.List;

import com.tdkj.tdcloud.admin.api.entity.XSpecimenTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 物种信息树形结构Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-11
 */
@Mapper
public interface XSpecimenTreeMapper 
{
    /**
     * 查询物种信息树形结构
     * 
     * @param id 物种信息树形结构主键
     * @return 物种信息树形结构
     */
    public XSpecimenTree selectXSpecimenTreeById(Long id);
    public XSpecimenTree getSpecimenTreeByNameEn(@Param("nameEn") String nameEn,@Param("parentEn") String parentEn);
    public List<XSpecimenTree> getSpecimenTreeByParentEn(@Param("parentEn") String parentEn);

    /**
     * 查询物种信息树形结构列表
     * 
     * @param xSpecimenTree 物种信息树形结构
     * @return 物种信息树形结构集合
     */
    public List<XSpecimenTree> selectXSpecimenTreeList(XSpecimenTree xSpecimenTree);

    /**
     * 新增物种信息树形结构
     * 
     * @param xSpecimenTree 物种信息树形结构
     * @return 结果
     */
    public int insertXSpecimenTree(XSpecimenTree xSpecimenTree);
    public int batchInsertXSpecimenTree(List<XSpecimenTree> list);

    /**
     * 修改物种信息树形结构
     * 
     * @param xSpecimenTree 物种信息树形结构
     * @return 结果
     */
    public int updateXSpecimenTree(XSpecimenTree xSpecimenTree);

    /**
     * 删除物种信息树形结构
     * 
     * @param id 物种信息树形结构主键
     * @return 结果
     */
    public int deleteXSpecimenTreeById(Long id);

    /**
     * 批量删除物种信息树形结构
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteXSpecimenTreeByIds(Long[] ids);
}
