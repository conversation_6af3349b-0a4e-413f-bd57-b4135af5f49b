import request from '@/router/axios'

export function editorUpload(form) {
  return request({
    url: '/admin/sys-file/upload',
    method: 'post',
    formData: form,
    data:form,
    headers:{
      'Content-Type': "multipart/form-data",
    }
  })
}
export function videoUpload(form) {
  return request({
    url: '/admin/sys-file/uploadVideo',
    method: 'post',
    formData: form,
    data:form,
    headers:{
      'Content-Type': "multipart/form-data",
    }
  })
}

//叶角质层数据导入/xspecimen/importSpecimen  导入 post@悲伤小肉肉 
export function importSpecimen(formData) {
  return request({
    url: '/admin/xspecimen/importSpecimen',
    method: 'post',
    formData: formData,
    data:formData,
    headers:{
      'Content-Type': "application/x-www-form-urlencoded",
    }
  })
}

//
export function importRupelian(formData) {
  return request({
    url: '/admin/rupelian/importRupelian',
    method: 'post',
    formData: formData,
    data:formData,
    headers:{
      'Content-Type': "application/x-www-form-urlencoded",
    }
  })
}
//采集地子表导入
export function importChattian(formData) {
  return request({
    url: '/admin/chattian/importChattian',
    method: 'post',
    formData: formData,
    data:formData,
    headers:{
      'Content-Type': "application/x-www-form-urlencoded",
    }
  })
}

//文件上传（）多文件上传
export function uploadSpecimenFiles(form) {
  return request({
    url: '/admin/sys-file/uploadSpecimenFiles',
    method: 'post',
    formData: form,
    data:form,
    headers:{
      'Content-Type': "multipart/form-data",
    }
  })
}

//首页图表
//数据展示模块
export function getSpecimenCount() {
  return request({
    url: '/admin/xspecimen/getSpecimenCount',
    method: 'get'
  })
}
