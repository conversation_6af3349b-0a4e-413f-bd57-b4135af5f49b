/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.Epoch;
import com.tdkj.tdcloud.admin.api.entity.Fossil;
import com.tdkj.tdcloud.admin.api.entity.Rupelian;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.vo.RupelianExcelVO;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 13:42:15
 */
@Mapper
public interface RupelianMapper extends TdcloudBaseMapper<Rupelian> {


	public int insertRupelianExcel(RupelianExcelVO rupelian);
	public int updateRupelianExcel(RupelianExcelVO rupelian);

	/**
	 * 查询【请填写功能名称】
	 *
	 * @param Pid 【请填写功能名称】主键
	 * @return 【请填写功能名称】
	 */
	public Rupelian selectRupelianByPid(Long Pid);
	public Rupelian selectRupelianById(Integer id);
	public List<SysFile> getPercentageList(@Param("xType")String xType,@Param("original")String original);
	public int getRupelianSiteNameTotal();
	public Epoch getRupelianEpochTotal();
	public List<Fossil> getRupelianFossilTotal();

	/**
	 * 查询【请填写功能名称】列表
	 *
	 * @param rupelian 【请填写功能名称】
	 * @return 【请填写功能名称】集合
	 */
	public List<Rupelian> selectRupelianList(Rupelian rupelian);

	/**
	 * 新增【请填写功能名称】
	 *
	 * @param rupelian 【请填写功能名称】
	 * @return 结果
	 */
	public int insertRupelian(Rupelian rupelian);

	/**
	 * 修改【请填写功能名称】
	 *
	 * @param rupelian 【请填写功能名称】
	 * @return 结果
	 */
	public int updateRupelian(Rupelian rupelian);

	/**
	 * 删除【请填写功能名称】
	 *
	 * @param Pid 【请填写功能名称】主键
	 * @return 结果
	 */
	public int deleteRupelianByPid(Long Pid);

	/**
	 * 批量删除【请填写功能名称】
	 *
	 * @param Pids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteRupelianByPids(Long[] Pids);
}
