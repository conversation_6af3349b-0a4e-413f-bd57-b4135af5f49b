<template>
  <div class="mod-config">
    <basic-container>
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-button v-if="permissions.admin_xteam_add" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button v-if="permissions.admin_xteam_export" icon="el-icon-download" type="primary" plain
                     @click="exportExcel()">导出
          </el-button>
        </el-form-item>
      </el-form>

      <div class="avue-crud">
        <el-table :data="dataList" border v-loading="dataListLoading">
          <el-table-column
            type="index"
            header-align="center"
            align="center"
            label="序号">
            <template slot-scope="scope">
              <span>{{ (pageIndex - 1) * pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>

            <el-table-column
                    prop="personName"
                    header-align="center"
                    align="center"
                    label="姓名">
            </el-table-column>
            <el-table-column
                    prop="academicTitle"
                    header-align="center"
                    align="center"
                    label="职称">
            </el-table-column>
            <el-table-column
                    prop="title"
                    header-align="center"
                    align="center"
                    label="岗位">
            </el-table-column>
            <el-table-column
                    prop="email"
                    header-align="center"
                    align="center"
                    label="邮箱">
            </el-table-column>
<!--            <el-table-column-->
<!--                    prop="postalCode"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="邮政编码">-->
<!--            </el-table-column>-->
<!--            <el-table-column-->
<!--                    prop="mailingAddress"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="简介">-->
<!--            </el-table-column>-->
            <el-table-column
                    prop="type"
                    header-align="center"
                    align="center"
                    label="团队类型">
              <template slot-scope="scope">
                <div v-if="scope.row.type === 'academician'">研究团队</div>
                <div v-else>人才培养</div>
              </template>
            </el-table-column>
          <el-table-column
            prop="createTime"
            header-align="center"
            align="center"
            label="创建时间">
          </el-table-column>
          <el-table-column
            prop="languageType"
            header-align="center"
            align="center"
            label="语言类型">
            <template slot-scope="scope">
              <div v-if="scope.row.languageType === 'zh'">中文</div>
              <div v-else>英文</div>
            </template>
          </el-table-column>
          <el-table-column width="200" fixed="right"
                  header-align="center"
                  align="center"
                  label="操作">
            <template slot-scope="scope">
              <el-button  type="success" size="small"  @click="openDetailHandle(scope.row.id)">查看</el-button>
              <el-button v-if="permissions.admin_xteam_edit" type="primary" size="small"  @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
              <el-button v-if="permissions.admin_xteam_del" type="danger" size="small"  @click="deleteHandle(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
      <detailInfo v-if="detailVisible" ref="detailCom" ></detailInfo>
    </basic-container>
  </div>
</template>

<script>
  import {fetchList, delObj} from '@/api/xteam/xteam'
  import TableForm from './xteam-form'
  import detailInfo from './info'
  import {mapGetters} from 'vuex'

  export default {
    data() {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false,
        detailVisible: false

      }
    },
    components: {
      TableForm,
      detailInfo
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        })).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      //查看详情
      openDetailHandle(id) {
        this.detailVisible = true
        this.$nextTick(() => {
          this.$refs.detailCom.getDetailInfo(id)
        })
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        this.$confirm('是否确认删除此条数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.$message.success('删除成功')
          this.getDataList()
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/admin/xteam/export', this.searchForm, 'xteam.xlsx')
      }
    }
  }
</script>
