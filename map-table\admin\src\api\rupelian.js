
import request from '@/router/axios'

export function fetchList(query) {
  return request({
    url: '/admin/rupelian/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/rupelian',
    method: 'post',
    data: obj
  })
}

// export function getObj(id) {
//   return request({
//     url: '/admin/rupelian/' + id,
//     method: 'get'
//   })
// }
// /rupelian/getByPId
export function getObj(pId) {
  return request({
    url: '/admin/rupelian/getByPId',
    method: 'get',
    params: {pId}
  })
}
export function delObj(id) {
  return request({
    url: '/admin/rupelian/' + id,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/rupelian',
    method: 'put',
    data: obj
  })
}
