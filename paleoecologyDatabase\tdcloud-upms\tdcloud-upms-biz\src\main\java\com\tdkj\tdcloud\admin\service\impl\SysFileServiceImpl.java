/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.s3.Headers;
import com.amazonaws.services.s3.model.S3Object;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.SysFileDTO;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.mapper.RupelianMapper;
import com.tdkj.tdcloud.admin.mapper.SysFileMapper;
import com.tdkj.tdcloud.admin.mapper.XSpecimenMapper;
import com.tdkj.tdcloud.admin.service.FfmpegService;
import com.tdkj.tdcloud.admin.service.SysFileService;
import com.tdkj.tdcloud.admin.utils.StringPatternUtil;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.file.core.FileProperties;
import com.tdkj.tdcloud.common.file.core.FileTemplate;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2019-06-18 17:18:42
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements SysFileService {

	private final FileTemplate fileTemplate;

	private final FileProperties properties;

	private final FfmpegService ffmpegService;

	@Resource
	private XSpecimenMapper xSpecimenMapper;

	/**
	 * 上传文件
	 * @param file
	 * @return
	 */
	@Override
	public R uploadFile(MultipartFile file) {
		String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
		Map<String, String> resultMap = new HashMap<>(9);
		resultMap.put("bucketName", properties.getBucketName());
		resultMap.put("fileName", fileName);
		String originalFilename = new String(
				Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
				StandardCharsets.UTF_8);
		resultMap.put("original", originalFilename);
		resultMap.put("name", originalFilename);
		resultMap.put("type", FileUtil.extName(file.getOriginalFilename()));
		resultMap.put("fileSize", String.valueOf(file.getSize()));
		resultMap.put("url", String.format("/admin/sys-file/%s/%s", properties.getBucketName(), fileName));

		try (InputStream inputStream = file.getInputStream()) {
			fileTemplate.putObject(properties.getBucketName(), fileName, inputStream, file.getContentType());
			// 文件管理数据记录,收集管理追踪文件
//			fileLog(file, fileName);
		}
		catch (Exception e) {
			log.error("上传失败", e);
			return R.failed(e.getLocalizedMessage());
		}
		return R.ok(resultMap);
	}

	@Override
	public R uploadFiles(MultipartFile[] files) {

		Map<String, String> resultMap = new HashMap<>(9);

		for (MultipartFile file : files) {

			String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
			resultMap.put("bucketName", properties.getBucketName());
			resultMap.put("fileName", fileName);
			String originalFilename = new String(
					Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
					StandardCharsets.UTF_8);
			resultMap.put("original", originalFilename);
			resultMap.put("name", originalFilename);
			resultMap.put("type", FileUtil.extName(file.getOriginalFilename()));
			resultMap.put("fileSize", String.valueOf(file.getSize()));
			String url = String.format("/admin/sys-file/%s/%s", properties.getBucketName(), fileName);
			resultMap.put("url", url);
			try (InputStream inputStream = file.getInputStream()) {
				fileTemplate.putObject(properties.getBucketName(), fileName, inputStream, file.getContentType());
				// 文件管理数据记录,收集管理追踪文件
//				fileLogSpecies(file, fileName,url,speciesId);


			} catch (Exception e) {
				log.error("上传失败", e);
				return R.failed(e.getLocalizedMessage());
			}

		}

		return R.ok(resultMap);
	}

	@Override
	public R uploadSpecimenFiles(MultipartFile[] files,String xType) {
		Map<String, String> resultMap = new HashMap<>(9);

		for (MultipartFile file : files) {

			String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
			resultMap.put("bucketName", properties.getBucketName());
			resultMap.put("fileName", fileName);
			String originalFilename = new String(
					Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
					StandardCharsets.UTF_8);
			resultMap.put("original", originalFilename);
			resultMap.put("name", originalFilename);
			resultMap.put("type", FileUtil.extName(file.getOriginalFilename()));
			resultMap.put("fileSize", String.valueOf(file.getSize()));
			String url = String.format("/admin/sys-file/%s/%s", properties.getBucketName(), fileName);
			resultMap.put("url", url);
			try (InputStream inputStream = file.getInputStream()) {
				fileTemplate.putObject(properties.getBucketName(), fileName, inputStream, file.getContentType());
				// 文件管理数据记录,收集管理追踪文件
				fileLog(file, fileName,url,xType);


			} catch (Exception e) {
				log.error("上传失败", e);
				return R.failed(e.getLocalizedMessage());
			}

		}

		return R.ok(resultMap);
	}

	/**
	 * 视频上传
	 * @param file
	 * @return
	 */
	@Override
	public R uploadVideo(MultipartFile file) {
		Map<String, String> resultMap = new HashMap<>(8);
		try {
			String folderName = IdUtil.simpleUUID(); // 生成一个随机的文件夹名称
			String originalFileName = file.getOriginalFilename();
			String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
			String m3u8FileName = IdUtil.simpleUUID() + ".m3u8";
			String inputFilePath = System.getProperty("java.io.tmpdir") + File.separator + folderName  + fileName;
			String outputFilePath = System.getProperty("java.io.tmpdir") + File.separator + folderName  + m3u8FileName;

			// 获取文件大小和扩展名
			long fileSize = file.getSize();
			String fileType = FileUtil.extName(originalFileName);
			// 创建文件夹
			File folder = new File(System.getProperty("java.io.tmpdir") + File.separator + folderName);
			if (!folder.exists()) {
				folder.mkdir();
			}

			// 将视频文件转成 m3u8 格式
			file.transferTo(new File(inputFilePath));
			ffmpegService.convertToM3u8(inputFilePath, outputFilePath);

			// 上传 m3u8 文件到本地
			try (InputStream inputStream = new FileInputStream(outputFilePath)) {
//					String objectName = String.format("admin/sys-file/%s/%s", properties.getBucketName(), m3u8FileName);
//					String objectName = String.format("admin/sys-file/%s/%s", properties.getBucketName(), m3u8FileName);
				fileTemplate.putObject(folderName,m3u8FileName, inputStream, "application/vnd.apple.mpegurl");
			}

			// 上传本地
			File[] tsFiles = new File(outputFilePath).getParentFile().listFiles((dir, name) -> name.endsWith(".ts"));
			for (File tsFile : tsFiles) {
				try (InputStream inputStream = new FileInputStream(tsFile)) {
					String tsFileName = tsFile.getName();
//						String objectName = String.format("admin/sys-file/%s/%s", properties.getBucketName(), tsFileName);
//						String objectName = String.format("admin/sys-file/%s/%s", properties.getBucketName(), tsFileName);
					fileTemplate.putObject(folderName, tsFileName, inputStream, "video/mp2t");
				}
			}

			// 文件管理数据记录，收集管理追踪文件
			String m3u8Url = String.format("/admin/sys-file/%s/%s", folderName, m3u8FileName);
			String originalFilename = new String(
					Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
					StandardCharsets.UTF_8);
			resultMap.put("bucketName", properties.getBucketName());
			resultMap.put("fileName", fileName);
			resultMap.put("original", originalFilename);
			resultMap.put("name", originalFilename);
			resultMap.put("fileSize", String.valueOf(fileSize));
			resultMap.put("type", fileType);
			resultMap.put("url", m3u8Url);


		} catch (Exception e) {
			e.printStackTrace();
			log.error("上传失败", e);
			return R.failed(e.getLocalizedMessage());
		}

		return R.ok(resultMap);
	}

	@Override
	public void getVideoFile(String bucket, String fileName, HttpServletResponse response) {
		try (S3Object s3Object = fileTemplate.getObject(bucket, fileName)) {
			String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
			if (StringPatternUtil.stringMatchIgnoreCase(fileSuffix,
					"(.flv|.swf|.mkv|.avi|.rm|.rmvb|.mpeg|.mpg|.ogg|.ogv|.mov|.wmv|.mp4|.webm|.wav|.mid|.mp3|.aac)")) {
				String contentRange = (String) s3Object.getObjectMetadata().getRawMetadataValue(Headers.CONTENT_RANGE);
				long contentLength = s3Object.getObjectMetadata().getContentLength();
				response.setHeader("Content-Length", String.valueOf(contentLength));
				response.setHeader("Content-Range", contentRange);
//				response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
			}
			response.setHeader("Cache-Control", "no-cache");
			response.setContentType("application/octet-stream; charset=UTF-8");

			IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
		} catch (Exception e) {
			e.printStackTrace();
			log.error("文件读取异常: {}", e.getLocalizedMessage());
		}
	}

	/**
	 * 读取文件
	 * @param bucket
	 * @param fileName
	 * @param response
	 */
	@Override
	public void getFile(String bucket, String fileName, HttpServletResponse response) {
		try (S3Object s3Object = fileTemplate.getObject(bucket, fileName)) {
			response.setContentType("application/octet-stream; charset=UTF-8");
			IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
		}
		catch (Exception e) {
			log.error("文件读取异常: {}", e.getLocalizedMessage());
		}
	}

	/**
	 * 删除文件
	 * @param id
	 * @return
	 */
	@Override
	@SneakyThrows
	@Transactional(rollbackFor = Exception.class)
	public Boolean deleteFile(Long id) {
		SysFile file = this.getById(id);
		fileTemplate.removeObject(properties.getBucketName(), file.getFileName());
		return this.removeById(id);
	}

	@Override
	public Page getSysFilePage(Page page, SysFileDTO sysFileDTO) {
		//查询条件
		QueryWrapper<SysFile> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}
//		sysFileDTO.setXType("specimen");
		wrapper.eq(StringUtils.isNotBlank(sysFileDTO.getXType()), "x_type", sysFileDTO.getXType());
		wrapper.like(StringUtils.isNotBlank(sysFileDTO.getOriginal()), "original", sysFileDTO.getOriginal());

		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}

	@Override
	public R getCarouselImage() {
		List<SysFile> banner = xSpecimenMapper.getSpecimenImageList("banner");
		return R.ok(banner,"首页轮播图");
	}

	/**
	 * 文件管理数据记录,收集管理追踪文件
	 * @param file 上传文件格式
	 * @param fileName 文件名
	 */
	private void fileLog(MultipartFile file, String fileName,String url,String xType) {
		SysFile sysFile = new SysFile();
		sysFile.setFileName(fileName);
		sysFile.setUrl(url);
		sysFile.setXType(xType);

		String originalFilename = new String(
				Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
				StandardCharsets.UTF_8);
		sysFile.setOriginal(originalFilename);
		sysFile.setFileSize(file.getSize());
		sysFile.setType(FileUtil.extName(file.getOriginalFilename()));
		sysFile.setBucketName(properties.getBucketName());
		sysFile.setName(originalFilename);
		this.save(sysFile);
	}

}
