<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.tdkj</groupId>
		<artifactId>tdcloud-upms</artifactId>
		<version>4.6.0</version>
	</parent>

	<artifactId>tdcloud-upms-api</artifactId>
	<packaging>jar</packaging>

	<description>tdcloud 通用用户权限管理系统公共api模块</description>


	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>6.1.3</version> <!-- 根据实际情况选择合适的版本 -->
		</dependency>
		<!--core 工具类-->
		<dependency>
			<groupId>com.tdkj</groupId>
			<artifactId>tdcloud-common-core</artifactId>
		</dependency>
		<!--mybatis plus extension,包含了mybatis plus core-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<!--feign 工具类-->
		<dependency>
			<groupId>com.tdkj</groupId>
			<artifactId>tdcloud-common-feign</artifactId>
		</dependency>
		<!-- excel 导入导出 -->
		<dependency>
			<groupId>com.tdkj</groupId>
			<artifactId>tdcloud-common-excel</artifactId>
		</dependency>
	</dependencies>
</project>
