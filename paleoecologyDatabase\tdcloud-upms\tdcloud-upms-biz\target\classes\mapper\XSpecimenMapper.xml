<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.XSpecimenMapper">

  <resultMap id="xSpecimenMap" type="com.tdkj.tdcloud.admin.api.entity.XSpecimen">
        <id property="id" column="id"/>
        <result property="specimenNo" column="specimen_no"/>
        <result property="speciesEn" column="species_en"/>
        <result property="speciesCn" column="species_cn"/>
        <result property="genusEn" column="genus_en"/>
        <result property="genusCn" column="genus_cn"/>
        <result property="familyEn" column="family_en"/>
        <result property="familyCn" column="family_cn"/>
        <result property="collectionPlace" column="collection_place"/>
        <result property="placeEn" column="place_en"/>
        <result property="collectionTime" column="collection_time"/>
        <result property="collector" column="collector"/>
        <result property="importBatch" column="import_batch"/>
        <result property="languageType" column="language_type"/>
        <result property="collectorEn" column="collector_en"/>
        <result property="createTime" column="create_time"/>

  </resultMap>

	<sql id="selectXSpecimenVo">
		select id, specimen_no, species_en,create_time,collector_en, species_cn, genus_en, genus_cn, family_en, family_cn, collection_place, place_en, collection_time, collector, import_batch, language_type from x_specimen
	</sql>

	<select id="selectXSpecimenList" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimen" resultMap="xSpecimenMap">
		<include refid="selectXSpecimenVo"/>
		<where>
			<if test="specimenNo != null  and specimenNo != ''"> and specimen_no = #{specimenNo}</if>
			<if test="speciesEn != null  and speciesEn != ''"> and species_en = #{speciesEn}</if>
			<if test="speciesCn != null  and speciesCn != ''"> and species_cn = #{speciesCn}</if>
			<if test="genusEn != null  and genusEn != ''"> and genus_en = #{genusEn}</if>
			<if test="genusCn != null  and genusCn != ''"> and genus_cn = #{genusCn}</if>
			<if test="familyEn != null  and familyEn != ''"> and family_en = #{familyEn}</if>
			<if test="familyCn != null  and familyCn != ''"> and family_cn = #{familyCn}</if>
			<if test="collectionPlace != null  and collectionPlace != ''"> and collection_place = #{collectionPlace}</if>
			<if test="placeEn != null  and placeEn != ''"> and place_en = #{placeEn}</if>
			<if test="collectionTime != null  and collectionTime != ''"> and collection_time = #{collectionTime}</if>
			<if test="collector != null  and collector != ''"> and collector = #{collector}</if>
			<if test="importBatch != null  and importBatch != ''"> and import_batch = #{importBatch}</if>
			<if test="languageType != null  and languageType != ''"> and language_type = #{languageType}</if>
		</where>
	</select>

	<select id="selectXSpecimenTotal"  resultType="int">
		select count(id) from x_specimen
	</select>

	<select id="getSpecimenCollectionPlaceTotal"  resultType="int">
		SELECT COUNT(DISTINCT collection_place) AS total
		FROM x_specimen;
	</select>

	<select id="selectXSpecimenById" parameterType="Long" resultMap="xSpecimenMap">
		<include refid="selectXSpecimenVo"/>
		where id = #{id}
	</select>

	<select id="selectXSpecimenBySpeciesEn" parameterType="string" resultMap="xSpecimenMap">
		<include refid="selectXSpecimenVo"/>
		where species_en = #{speciesEn}
	</select>

	<select id="getSpecimenImageList"  resultType="com.tdkj.tdcloud.admin.api.entity.SysFile">
		SELECT id,file_name,bucket_name,original,url,
			   `name` FROM sys_file WHERE x_type = #{xType} AND del_flag = '0'
	</select>

	<insert id="insertXSpecimen" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimen" useGeneratedKeys="true" keyProperty="id">
		insert into x_specimen
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="specimenNo != null">specimen_no,</if>
			<if test="speciesEn != null">species_en,</if>
			<if test="speciesCn != null">species_cn,</if>
			<if test="genusEn != null">genus_en,</if>
			<if test="genusCn != null">genus_cn,</if>
			<if test="familyEn != null">family_en,</if>
			<if test="familyCn != null">family_cn,</if>
			<if test="collectionPlace != null">collection_place,</if>
			<if test="placeEn != null">place_en,</if>
			<if test="collectionTime != null">collection_time,</if>
			<if test="collector != null">collector,</if>
			<if test="importBatch != null">import_batch,</if>
			<if test="languageType != null">language_type,</if>
			<if test="collectorEn != null">collector_en,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="specimenNo != null">#{specimenNo},</if>
			<if test="speciesEn != null">#{speciesEn},</if>
			<if test="speciesCn != null">#{speciesCn},</if>
			<if test="genusEn != null">#{genusEn},</if>
			<if test="genusCn != null">#{genusCn},</if>
			<if test="familyEn != null">#{familyEn},</if>
			<if test="familyCn != null">#{familyCn},</if>
			<if test="collectionPlace != null">#{collectionPlace},</if>
			<if test="placeEn != null">#{placeEn},</if>
			<if test="collectionTime != null">#{collectionTime},</if>
			<if test="collector != null">#{collector},</if>
			<if test="importBatch != null">#{importBatch},</if>
			<if test="languageType != null">#{languageType},</if>
			<if test="collectorEn != null">#{collectorEn},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>

	<update id="updateXSpecimen" parameterType="com.tdkj.tdcloud.admin.api.entity.XSpecimen">
		update x_specimen
		<trim prefix="SET" suffixOverrides=",">
			<if test="specimenNo != null">specimen_no = #{specimenNo},</if>
			<if test="speciesEn != null">species_en = #{speciesEn},</if>
			<if test="speciesCn != null">species_cn = #{speciesCn},</if>
			<if test="genusEn != null">genus_en = #{genusEn},</if>
			<if test="genusCn != null">genus_cn = #{genusCn},</if>
			<if test="familyEn != null">family_en = #{familyEn},</if>
			<if test="familyCn != null">family_cn = #{familyCn},</if>
			<if test="collectionPlace != null">collection_place = #{collectionPlace},</if>
			<if test="placeEn != null">place_en = #{placeEn},</if>
			<if test="collectionTime != null">collection_time = #{collectionTime},</if>
			<if test="collector != null">collector = #{collector},</if>
			<if test="importBatch != null">import_batch = #{importBatch},</if>
			<if test="languageType != null">language_type = #{languageType},</if>
			<if test="collectorEn != null">collector_en = #{collectorEn},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
		</trim>
		where id = #{id}
	</update>


	<insert id="insertXSpecimenExcel" parameterType="com.tdkj.tdcloud.admin.api.vo.XSpecimenExcelVO" useGeneratedKeys="true" keyProperty="id">
		insert into x_specimen
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="specimenNo != null">specimen_no,</if>
			<if test="speciesEn != null">species_en,</if>
			<if test="speciesCn != null">species_cn,</if>
			<if test="genusEn != null">genus_en,</if>
			<if test="genusCn != null">genus_cn,</if>
			<if test="familyEn != null">family_en,</if>
			<if test="familyCn != null">family_cn,</if>
			<if test="collectionPlace != null">collection_place,</if>
			<if test="placeEn != null">place_en,</if>
			<if test="collectionTime != null">collection_time,</if>
			<if test="collector != null">collector,</if>
			<if test="importBatch != null">import_batch,</if>
			<if test="collectorEn != null">collector_en,</if>
			<if test="createTime != null">create_time,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="specimenNo != null">#{specimenNo},</if>
			<if test="speciesEn != null">#{speciesEn},</if>
			<if test="speciesCn != null">#{speciesCn},</if>
			<if test="genusEn != null">#{genusEn},</if>
			<if test="genusCn != null">#{genusCn},</if>
			<if test="familyEn != null">#{familyEn},</if>
			<if test="familyCn != null">#{familyCn},</if>
			<if test="collectionPlace != null">#{collectionPlace},</if>
			<if test="placeEn != null">#{placeEn},</if>
			<if test="collectionTime != null">#{collectionTime},</if>
			<if test="collector != null">#{collector},</if>
			<if test="importBatch != null">#{importBatch},</if>
			<if test="collectorEn != null">#{collectorEn},</if>
			<if test="createTime != null">#{createTime},</if>
		</trim>
	</insert>

	<insert id="batchInsertXSpecimenExcel" parameterType="java.util.List">
		INSERT INTO x_specimen (specimen_no, species_cn,species_en, genus_en, genus_cn, family_en, family_cn, collection_place, place_en, collection_time, collector, import_batch, collector_en, create_time,language_type)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.specimenNo}, #{item.speciesCn},#{item.speciesEn}, #{item.genusEn}, #{item.genusCn}, #{item.familyEn}, #{item.familyCn}, #{item.collectionPlace}, #{item.placeEn}, #{item.collectionTime}, #{item.collector}, #{item.importBatch}, #{item.collectorEn}, #{item.createTime},#{item.languageType})
		</foreach>
	</insert>

	<update id="updateXSpecimenExcel" parameterType="com.tdkj.tdcloud.admin.api.vo.XSpecimenExcelVO">
		update x_specimen
		<trim prefix="SET" suffixOverrides=",">
			<if test="specimenNo != null">specimen_no = #{specimenNo},</if>
			<if test="speciesEn != null">species_en = #{speciesEn},</if>
			<if test="speciesCn != null">species_cn = #{speciesCn},</if>
			<if test="genusEn != null">genus_en = #{genusEn},</if>
			<if test="genusCn != null">genus_cn = #{genusCn},</if>
			<if test="familyEn != null">family_en = #{familyEn},</if>
			<if test="familyCn != null">family_cn = #{familyCn},</if>
			<if test="collectionPlace != null">collection_place = #{collectionPlace},</if>
			<if test="placeEn != null">place_en = #{placeEn},</if>
			<if test="collectionTime != null">collection_time = #{collectionTime},</if>
			<if test="collector != null">collector = #{collector},</if>
			<if test="importBatch != null">import_batch = #{importBatch},</if>
			<if test="collectorEn != null">collector_en = #{collectorEn},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
		</trim>
		where id = #{id}
	</update>

	<update id="updateVisits">
		update x_visits
		set visit_count = visit_count + 1
		where id = 1
	</update>
	<select id="selectVisits" resultType="int">
		select visit_count from x_visits
		where id = 1
	</select>

	<delete id="deleteXSpecimenById" parameterType="Long">
		delete from x_specimen where id = #{id}
	</delete>

	<delete id="deleteXSpecimenByIds" parameterType="String">
		delete from x_specimen where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>
