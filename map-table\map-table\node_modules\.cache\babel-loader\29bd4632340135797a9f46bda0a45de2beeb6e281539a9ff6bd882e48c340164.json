{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array-buffer.detached.js\";\nimport \"core-js/modules/es.array-buffer.transfer.js\";\nimport \"core-js/modules/es.array-buffer.transfer-to-fixed-length.js\";\nimport \"core-js/modules/es.typed-array.to-reversed.js\";\nimport \"core-js/modules/es.typed-array.to-sorted.js\";\nimport \"core-js/modules/es.typed-array.with.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.find.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport { getDetailData } from '@/api/paleoecology';\nimport * as XLSX from \"xlsx\";\nimport { saveAs } from \"file-saver\";\nimport { mapMutations } from \"vuex\";\nexport default {\n  props: {\n    tableData: {\n      type: Array,\n      default: () => []\n    },\n    searchCriteria: {\n      type: Object,\n      default: () => {}\n    },\n    searchHistory: {\n      type: Array,\n      default: () => []\n    },\n    selectedSearchId: {\n      type: String,\n      default: \"\"\n    }\n  },\n  watch: {\n    searchHistory: {\n      immediate: true,\n      handler(newHistory) {\n        if (newHistory && newHistory.length > 0) {\n          this.results = [...newHistory];\n          if (this.searchCriteria && Object.keys(this.searchCriteria).length > 0) {\n            // Try to find a matching search result based on criteria\n            this.updateSelectedResultFromCriteria(this.searchCriteria);\n          } else if (newHistory.length > 0) {\n            // If no criteria or can't find match, default to most recent\n            this.localSelectedResult = newHistory[newHistory.length - 1].id;\n          }\n        }\n      }\n    },\n    selectedSearchId: {\n      immediate: true,\n      handler(newId) {\n        if (newId) {\n          this.localSelectedResult = newId;\n        }\n      }\n    },\n    searchCriteria: {\n      handler(newCriteria) {\n        // Reset to first page when search criteria changes\n        this.currentPage = 1;\n\n        // Update the selected result in dropdown based on criteria\n        if (newCriteria && Object.keys(newCriteria).length > 0) {\n          this.updateSelectedResultFromCriteria(newCriteria);\n        }\n      },\n      deep: true\n    },\n    tableData: {\n      handler() {\n        // Reset to first page when tableData changes\n        this.currentPage = 1;\n        // Clear expanded rows when table data changes\n        this.expandedRowKeys = [];\n        this.childTableData = [];\n      }\n    }\n  },\n  data() {\n    return {\n      currentPage: 1,\n      pageSize: 10,\n      localSelectedResult: \"\",\n      results: [],\n      debounceTimeout: null,\n      expandedRowKeys: [],\n      // 存储当前展开的行的keys\n      childTableData: [],\n      // 子表数据\n      childTableLoading: false,\n      // 子表加载状态\n      currentExpandedRow: null // 当前展开的行\n    };\n  },\n  created() {\n    // Initialize localSelectedResult from props if available\n    if (this.selectedSearchId) {\n      this.localSelectedResult = this.selectedSearchId;\n    } else if (this.searchHistory && this.searchHistory.length > 0) {\n      // Try to find a matching search result based on criteria\n      if (this.searchCriteria && Object.keys(this.searchCriteria).length > 0) {\n        this.updateSelectedResultFromCriteria(this.searchCriteria);\n      } else {\n        // Default to most recent search\n        this.localSelectedResult = this.searchHistory[this.searchHistory.length - 1].id;\n      }\n    }\n  },\n  computed: {\n    paginatedData() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      // Use processed table data to include combined fields\n      return this.processedTableData.slice(start, end);\n    },\n    processedTableData() {\n      if (!this.tableData || this.tableData.length === 0) {\n        return [];\n      }\n\n      // Process each item in the tableData array\n      return this.tableData.map(item => {\n        const processedItem = {\n          ...item\n        }; // Create a copy of the original item\n\n        // Combine Reference fields\n        processedItem.Reference = this.combineFields(item, \"Reference\", 3);\n\n        // Add OtherReferences to Reference if it exists\n        if (item.OtherReferences && item.OtherReferences.trim() !== \"\") {\n          if (processedItem.Reference) {\n            processedItem.Reference += \", \" + item.OtherReferences;\n          } else {\n            processedItem.Reference = item.OtherReferences;\n          }\n        }\n        return processedItem;\n      });\n    },\n    processedChildTableData() {\n      if (!this.childTableData || this.childTableData.length === 0) {\n        return [];\n      }\n\n      // Process each item in the childTableData array\n      return this.childTableData.map(item => {\n        const processedItem = {\n          ...item\n        }; // Create a copy of the original item\n\n        // Combine ScientificName fields\n        processedItem.ScientificName = this.combineFields(item, \"ScientificName\", 3);\n\n        // Combine Species fields\n        processedItem.Species = this.combineFields(item, \"Species\", 3);\n\n        // Combine PlantOrgan fields\n        processedItem.PlantOrgan = this.combineFields(item, \"PlantOrgan\", 2);\n        return processedItem;\n      });\n    }\n  },\n  methods: {\n    ...mapMutations(['SET_OPEN_LOGIN_BOX']),\n    // Helper method to combine numbered fields\n    combineFields(item, baseFieldName, count) {\n      const values = [];\n      for (let i = 1; i <= count; i++) {\n        const fieldName = `${baseFieldName}${i}`;\n        if (item[fieldName] && item[fieldName].trim() !== \"\") {\n          values.push(item[fieldName]);\n        }\n      }\n      return values.join(\", \");\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      // 切换页面时清空展开的行\n      this.expandedRowKeys = [];\n      this.childTableData = [];\n    },\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.currentPage = 1; // Reset to first page when changing page size\n      // 切换页面大小时清空展开的行\n      this.expandedRowKeys = [];\n      this.childTableData = [];\n    },\n    applySelectedSearch(value) {\n      // Update the local selected result\n      this.localSelectedResult = value;\n\n      // Find the selected search result\n      const selectedSearch = this.results.find(result => result.id === value);\n      if (selectedSearch && selectedSearch.criteria) {\n        // Emit event to parent component to update search criteria\n        this.$emit(\"search-result-selected\", JSON.parse(JSON.stringify(selectedSearch.criteria)));\n\n        // Also emit the selected search ID to the parent\n        this.$emit(\"update:selected-search-id\", value);\n      }\n    },\n    downloadData() {\n      if (this.$store.state.userInfo && this.$store.state.userInfo.username && this.$store.state.userInfo.username !== '') {\n        // Get current date in YYYYMMDD format\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = String(now.getMonth() + 1).padStart(2, \"0\");\n        const day = String(now.getDate()).padStart(2, \"0\");\n        const dateStr = `${year}${month}${day}`;\n\n        // Get the selected search name or use a default\n        let searchName = \"Search\";\n        if (this.localSelectedResult) {\n          const selectedSearch = this.results.find(result => result.id === this.localSelectedResult);\n          if (selectedSearch) {\n            searchName = selectedSearch.id;\n          }\n        }\n\n        // Create filename in format Search[number]+[date]\n        const filename = `${searchName}_${dateStr}.xlsx`;\n\n        // Get the table columns in the exact order they appear in the UI\n        const tableColumns = [\"ID\", \"SiteNo\", \"SiteName\", \"Country\", \"DatingMethod\", \"DatingQuality\", \"Epoch\", \"Stage\", \"EarlyInterval\", \"LateInterval\", \"AgeMax\", \"AgeMin\", \"AgeMiddle\", \"Author\", \"Pubyr\", \"Longitude\", \"Latitude\", \"TimeBin\", \"FossilType\", \"Reference\"];\n\n        // Use processedTableData but filter to include only the fields shown in the table\n        const dataToExport = this.processedTableData.map(item => {\n          const filteredItem = {};\n          tableColumns.forEach(column => {\n            filteredItem[column] = item[column];\n          });\n          return filteredItem;\n        });\n\n        // Create a worksheet with columns in the same order as the table\n        const ws = XLSX.utils.json_to_sheet(dataToExport, {\n          header: tableColumns\n        });\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\n        const wbout = XLSX.write(wb, {\n          bookType: \"xlsx\",\n          type: \"binary\"\n        });\n        const blob = new Blob([this.s2ab(wbout)], {\n          type: \"application/octet-stream\"\n        });\n        saveAs(blob, filename);\n      } else {\n        // console.log('未登录')\n        this.$message.error('您还未登录，登录成功后才可下载数据！');\n        this.SET_OPEN_LOGIN_BOX(true);\n      }\n    },\n    s2ab(s) {\n      const buf = new ArrayBuffer(s.length);\n      const view = new Uint8Array(buf);\n      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;\n      return buf;\n    },\n    handleRowClick(row) {\n      // Navigate to detail page on row click\n      // Note: Row expansion should be handled by clicking the expand icon, not the row itself\n      this.$router.push({\n        path: `/mapDetail/${row.ID}`\n      });\n    },\n    handleExpandChange(row, expanded) {\n      // 当手动点击展开图标时触发\n      console.log('handleExpandChange called:', {\n        rowID: row.ID,\n        expanded,\n        currentChildData: this.childTableData.length\n      });\n      if (expanded) {\n        this.currentExpandedRow = row;\n        this.fetchChildTableData(row.ID);\n      } else {\n        this.childTableData = [];\n        this.currentExpandedRow = null;\n      }\n    },\n    fetchChildTableData(id) {\n      this.childTableLoading = true;\n      this.childTableData = [];\n\n      // 使用新添加的/detail接口获取子表数据\n      getDetailData(id).then(response => {\n        console.log('Detail API response for ID', id, ':', response.data);\n\n        // Handle the R response wrapper structure\n        let detailData = [];\n        if (response.data && response.data.data && response.data.data.detail) {\n          // Java Spring Boot R.ok({detail: [...]}) format\n          detailData = response.data.data.detail;\n        } else if (response.data && response.data.detail) {\n          // Direct {detail: [...]} format\n          detailData = response.data.detail;\n        } else if (Array.isArray(response.data)) {\n          // Direct array format\n          detailData = response.data;\n        } else if (response.data && Array.isArray(response.data.data)) {\n          // R.ok([...]) format (direct array in data)\n          detailData = response.data.data;\n        }\n        console.log('Extracted detail data:', detailData);\n        this.childTableData = detailData;\n\n        // 格式化日期时间字段\n        this.childTableData.forEach(item => {\n          if (item.TimeContext) {\n            // 将ISO格式的日期转换为本地日期时间格式\n            const date = new Date(item.TimeContext);\n            item.TimeContext = date.toLocaleString();\n          }\n        });\n        this.childTableLoading = false;\n      }).catch(error => {\n        // console.error(\"获取子表数据失败:\", error);\n        this.childTableData = [];\n        this.childTableLoading = false;\n        this.$message.error(\"获取子表数据失败\");\n      });\n    },\n    updateSelectedResultFromCriteria(criteria) {\n      // Find a search result that matches the current criteria\n      if (!this.results || this.results.length === 0) return;\n\n      // Convert criteria to string for comparison\n      const criteriaStr = JSON.stringify(criteria);\n\n      // Try to find an exact match\n      for (const result of this.results) {\n        if (result.criteria && JSON.stringify(result.criteria) === criteriaStr) {\n          this.localSelectedResult = result.id;\n          return;\n        }\n      }\n\n      // If no exact match found, we could either:\n      // 1. Leave the current selection as is\n      // 2. Clear the selection\n      // 3. Set to the most recent result\n\n      // Option 3: Set to most recent if no match found\n      // this.localSelectedResult = this.results[this.results.length - 1].id;\n\n      // Option 1: Leave as is (do nothing)\n    },\n    isMapSelection(searchItem) {\n      if (!searchItem || !searchItem.criteria) return false;\n\n      // Check if this search includes map selection\n      return searchItem.criteria.bboxes && searchItem.criteria.bboxes.length > 0 || searchItem.criteria.polygons && searchItem.criteria.polygons.length > 0 || searchItem.criteria.circle_centers && searchItem.criteria.circle_centers.length > 0;\n    }\n  }\n};", "map": {"version": 3, "names": ["getDetailData", "XLSX", "saveAs", "mapMutations", "props", "tableData", "type", "Array", "default", "searchCriteria", "Object", "searchHistory", "selectedSearchId", "String", "watch", "immediate", "handler", "newHistory", "length", "results", "keys", "updateSelectedResultFromCriteria", "localSelectedResult", "id", "newId", "newCriteria", "currentPage", "deep", "expandedRowKeys", "childTableData", "data", "pageSize", "debounceTimeout", "childTableLoading", "currentExpandedRow", "created", "computed", "paginatedData", "start", "end", "processedTableData", "slice", "map", "item", "processedItem", "Reference", "combineFields", "OtherReferences", "trim", "processedChildTableData", "ScientificName", "Species", "PlantOrgan", "methods", "baseFieldName", "count", "values", "i", "fieldName", "push", "join", "handlePageChange", "page", "handleSizeChange", "size", "applySelectedSearch", "value", "<PERSON><PERSON><PERSON><PERSON>", "find", "result", "criteria", "$emit", "JSON", "parse", "stringify", "downloadData", "$store", "state", "userInfo", "username", "now", "Date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "dateStr", "searchName", "filename", "tableColumns", "dataToExport", "filteredItem", "for<PERSON>ach", "column", "ws", "utils", "json_to_sheet", "header", "wb", "book_new", "book_append_sheet", "wbout", "write", "bookType", "blob", "Blob", "s2ab", "$message", "error", "SET_OPEN_LOGIN_BOX", "s", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "Uint8Array", "charCodeAt", "handleRowClick", "row", "$router", "path", "ID", "handleExpandChange", "expanded", "console", "log", "rowID", "currentChildData", "fetchChildTableData", "then", "response", "detailData", "detail", "isArray", "TimeContext", "date", "toLocaleString", "catch", "criteriaStr", "isMapSelection", "searchItem", "bboxes", "polygons", "circle_centers"], "sources": ["src/components/TableView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"table-view\">\r\n    <div class=\"table-header\">\r\n      <el-select v-model=\"localSelectedResult\" placeholder=\"Select saved search\" @change=\"applySelectedSearch\">\r\n        <el-option v-for=\"result in results\" :key=\"result.id\" :label=\"result.name\" :value=\"result.id\">\r\n          <span :class=\"{ 'map-selection': isMapSelection(result) }\">{{ result.name }}</span>\r\n        </el-option>\r\n      </el-select>\r\n      <el-button type=\"success\" size=\"small\" @click=\"downloadData\">Download</el-button>\r\n    </div>\r\n    <!--      :max-height=\"520\"-->\r\n    <!--      height=\"520px\"-->\r\n\r\n    <el-table :data=\"paginatedData\" :header-cell-style=\"{ background: '#E9E9E9' }\" style=\"width: 100%\" border\r\n      :highlight-current-row=\"true\" :header-row-style=\"{ position: 'sticky', top: 0 }\" @row-click=\"handleRowClick\"\r\n      row-key=\"ID\" :expand-row-keys=\"expandedRowKeys\" @expand-change=\"handleExpandChange\">\r\n      <el-table-column type=\"expand\">\r\n        <template slot-scope=\"props\">\r\n          <div v-loading=\"childTableLoading\" class=\"child-table-loading\" element-loading-text=\"Loading...\">\r\n            <!-- Debug info -->\r\n            <div style=\"background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;\">\r\n              <strong>Debug Info:</strong><br>\r\n              Loading: {{ childTableLoading }}<br>\r\n              Child Data Length: {{ childTableData.length }}<br>\r\n              Current Expanded Row: {{ currentExpandedRow ? currentExpandedRow.ID : 'None' }}<br>\r\n              Props Row ID: {{ props.row.ID }}\r\n            </div>\r\n\r\n            <div v-if=\"!childTableLoading && childTableData.length === 0\" class=\"no-child-data\">\r\n              No related data found for ID: {{ props.row.ID }}\r\n            </div>\r\n            <div v-else-if=\"!childTableLoading\" class=\"child-table-container\">\r\n              <el-table :data=\"processedChildTableData\" border size=\"mini\" style=\"width: 100%\"\r\n                :header-cell-style=\"{ background: '#F5F7FA' }\">\r\n                <!--                                    <el-table-column prop=\"Cid\" label=\"Cid\" width=\"80\"></el-table-column>-->\r\n                <!--                                    <el-table-column prop=\"ID\" label=\"ID\" width=\"80\"></el-table-column>-->\r\n                <el-table-column fixed=\"left\" prop=\"OriginalName\" label=\"OriginalName\"\r\n                  show-overflow-tooltip></el-table-column>\r\n                <el-table-column prop=\"ScientificName\" label=\"ScientificName\" show-overflow-tooltip></el-table-column>\r\n                <el-table-column prop=\"AcceptedRank\" label=\"AcceptedRank\"></el-table-column>\r\n                <el-table-column prop=\"Phylum\" label=\"Phylum\"></el-table-column>\r\n                <el-table-column prop=\"Class\" label=\"Class\"></el-table-column>\r\n                <el-table-column prop=\"Order\" label=\"Order\"></el-table-column>\r\n                <el-table-column prop=\"Family\" label=\"Family\"></el-table-column>\r\n                <el-table-column prop=\"Genus\" label=\"Genus\"></el-table-column>\r\n                <el-table-column prop=\"Species\" label=\"Species\" show-overflow-tooltip></el-table-column>\r\n                <el-table-column prop=\"PlantOrgan\" label=\"PlantOrgan\"></el-table-column>\r\n                <el-table-column prop=\"AbundValue\" label=\"AbundValue\"></el-table-column>\r\n                <el-table-column prop=\"AbundUnit\" label=\"AbundUnit\"></el-table-column>\r\n                <el-table-column prop=\"FossilType\" label=\"FossilType\"></el-table-column>\r\n                <!--                <el-table-column-->\r\n                <!--                  prop=\"PollenDiagram\"-->\r\n                <!--                  label=\"PollenDiagram\"-->\r\n                <!--                ></el-table-column>-->\r\n                <el-table-column prop=\"Assemblage\" label=\"Assemblage\"></el-table-column>\r\n                <el-table-column prop=\"SiteNo\" label=\"SiteNo\"></el-table-column>\r\n                <el-table-column prop=\"SiteName\" label=\"SiteName\" show-overflow-tooltip></el-table-column>\r\n                <el-table-column prop=\"Extinct\" label=\"Extinct\"></el-table-column>\r\n                <el-table-column fixed=\"right\" prop=\"TimeContext\" label=\"TimeContext\"></el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"ID\" label=\"ID\" width=\"70px\"></el-table-column>\r\n      <el-table-column prop=\"SiteNo\" label=\"SiteNo\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"SiteName\" label=\"SiteName\" width=\"150px\" show-overflow-tooltip></el-table-column>\r\n      <el-table-column prop=\"Country\" label=\"Country\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"DatingMethod\" label=\"DatingMethod\" width=\"150px\" show-overflow-tooltip></el-table-column>\r\n      <el-table-column prop=\"DatingQuality\" label=\"DatingQuality\" width=\"120px\"></el-table-column>\r\n      <el-table-column prop=\"Epoch\" label=\"Epoch\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"Stage\" label=\"Stage\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"EarlyInterval\" label=\"EarlyInterval\" width=\"120px\" show-overflow-tooltip></el-table-column>\r\n      <el-table-column prop=\"LateInterval\" label=\"LateInterval\" width=\"120px\" show-overflow-tooltip></el-table-column>\r\n      <el-table-column prop=\"AgeMax\" label=\"AgeMax\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"AgeMin\" label=\"AgeMin\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"AgeMiddle\" label=\"AgeMiddle\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"Author\" label=\"Author\" width=\"120px\" show-overflow-tooltip></el-table-column>\r\n      <el-table-column prop=\"Pubyr\" label=\"Pubyr\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"Longitude\" label=\"Longitude\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"Latitude\" label=\"Latitude\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"TimeBin\" label=\"TimeBin\" width=\"100px\"></el-table-column>\r\n      <el-table-column prop=\"FossilType\" label=\"FossilType\" width=\"120px\"></el-table-column>\r\n      <el-table-column prop=\"pollendiagram\" label=\"PollenDiagram\" width=\"120px\"></el-table-column>\r\n      <el-table-column prop=\"Reference\" label=\"Reference\" width=\"300px\" show-overflow-tooltip></el-table-column>\r\n    </el-table>\r\n    <div class=\"table-footer\">\r\n      <el-pagination background layout=\"sizes, prev, pager, next, total\" :total=\"tableData.length\" :page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100]\" :current-page.sync=\"currentPage\" @current-change=\"handlePageChange\"\r\n        @size-change=\"handleSizeChange\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDetailData } from '@/api/paleoecology';\r\nimport * as XLSX from \"xlsx\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { mapMutations } from \"vuex\";\r\n\r\nexport default {\r\n  props: {\r\n    tableData: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    searchCriteria: {\r\n      type: Object,\r\n      default: () => { },\r\n    },\r\n    searchHistory: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    selectedSearchId: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n  },\r\n  watch: {\r\n    searchHistory: {\r\n      immediate: true,\r\n      handler(newHistory) {\r\n        if (newHistory && newHistory.length > 0) {\r\n          this.results = [...newHistory];\r\n          if (\r\n            this.searchCriteria &&\r\n            Object.keys(this.searchCriteria).length > 0\r\n          ) {\r\n            // Try to find a matching search result based on criteria\r\n            this.updateSelectedResultFromCriteria(this.searchCriteria);\r\n          } else if (newHistory.length > 0) {\r\n            // If no criteria or can't find match, default to most recent\r\n            this.localSelectedResult = newHistory[newHistory.length - 1].id;\r\n          }\r\n        }\r\n      },\r\n    },\r\n    selectedSearchId: {\r\n      immediate: true,\r\n      handler(newId) {\r\n        if (newId) {\r\n          this.localSelectedResult = newId;\r\n        }\r\n      },\r\n    },\r\n    searchCriteria: {\r\n      handler(newCriteria) {\r\n        // Reset to first page when search criteria changes\r\n        this.currentPage = 1;\r\n\r\n        // Update the selected result in dropdown based on criteria\r\n        if (newCriteria && Object.keys(newCriteria).length > 0) {\r\n          this.updateSelectedResultFromCriteria(newCriteria);\r\n        }\r\n      },\r\n      deep: true,\r\n    },\r\n    tableData: {\r\n      handler() {\r\n        // Reset to first page when tableData changes\r\n        this.currentPage = 1;\r\n        // Clear expanded rows when table data changes\r\n        this.expandedRowKeys = [];\r\n        this.childTableData = [];\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      localSelectedResult: \"\",\r\n      results: [],\r\n      debounceTimeout: null,\r\n      expandedRowKeys: [], // 存储当前展开的行的keys\r\n      childTableData: [], // 子表数据\r\n      childTableLoading: false, // 子表加载状态\r\n      currentExpandedRow: null, // 当前展开的行\r\n    };\r\n  },\r\n  created() {\r\n    // Initialize localSelectedResult from props if available\r\n    if (this.selectedSearchId) {\r\n      this.localSelectedResult = this.selectedSearchId;\r\n    } else if (this.searchHistory && this.searchHistory.length > 0) {\r\n      // Try to find a matching search result based on criteria\r\n      if (this.searchCriteria && Object.keys(this.searchCriteria).length > 0) {\r\n        this.updateSelectedResultFromCriteria(this.searchCriteria);\r\n      } else {\r\n        // Default to most recent search\r\n        this.localSelectedResult =\r\n          this.searchHistory[this.searchHistory.length - 1].id;\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    paginatedData() {\r\n      const start = (this.currentPage - 1) * this.pageSize;\r\n      const end = start + this.pageSize;\r\n      // Use processed table data to include combined fields\r\n      return this.processedTableData.slice(start, end);\r\n    },\r\n    processedTableData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [];\r\n      }\r\n\r\n      // Process each item in the tableData array\r\n      return this.tableData.map((item) => {\r\n        const processedItem = { ...item }; // Create a copy of the original item\r\n\r\n        // Combine Reference fields\r\n        processedItem.Reference = this.combineFields(item, \"Reference\", 3);\r\n\r\n        // Add OtherReferences to Reference if it exists\r\n        if (item.OtherReferences && item.OtherReferences.trim() !== \"\") {\r\n          if (processedItem.Reference) {\r\n            processedItem.Reference += \", \" + item.OtherReferences;\r\n          } else {\r\n            processedItem.Reference = item.OtherReferences;\r\n          }\r\n        }\r\n\r\n        return processedItem;\r\n      });\r\n    },\r\n    processedChildTableData() {\r\n      if (!this.childTableData || this.childTableData.length === 0) {\r\n        return [];\r\n      }\r\n\r\n      // Process each item in the childTableData array\r\n      return this.childTableData.map((item) => {\r\n        const processedItem = { ...item }; // Create a copy of the original item\r\n\r\n        // Combine ScientificName fields\r\n        processedItem.ScientificName = this.combineFields(\r\n          item,\r\n          \"ScientificName\",\r\n          3\r\n        );\r\n\r\n        // Combine Species fields\r\n        processedItem.Species = this.combineFields(item, \"Species\", 3);\r\n\r\n        // Combine PlantOrgan fields\r\n        processedItem.PlantOrgan = this.combineFields(item, \"PlantOrgan\", 2);\r\n\r\n        return processedItem;\r\n      });\r\n    },\r\n  },\r\n  methods: {\r\n    ...mapMutations(['SET_OPEN_LOGIN_BOX']),\r\n    // Helper method to combine numbered fields\r\n    combineFields(item, baseFieldName, count) {\r\n      const values = [];\r\n\r\n      for (let i = 1; i <= count; i++) {\r\n        const fieldName = `${baseFieldName}${i}`;\r\n        if (item[fieldName] && item[fieldName].trim() !== \"\") {\r\n          values.push(item[fieldName]);\r\n        }\r\n      }\r\n\r\n      return values.join(\", \");\r\n    },\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      // 切换页面时清空展开的行\r\n      this.expandedRowKeys = [];\r\n      this.childTableData = [];\r\n    },\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1; // Reset to first page when changing page size\r\n      // 切换页面大小时清空展开的行\r\n      this.expandedRowKeys = [];\r\n      this.childTableData = [];\r\n    },\r\n    applySelectedSearch(value) {\r\n      // Update the local selected result\r\n      this.localSelectedResult = value;\r\n\r\n      // Find the selected search result\r\n      const selectedSearch = this.results.find((result) => result.id === value);\r\n      if (selectedSearch && selectedSearch.criteria) {\r\n        // Emit event to parent component to update search criteria\r\n        this.$emit(\r\n          \"search-result-selected\",\r\n          JSON.parse(JSON.stringify(selectedSearch.criteria))\r\n        );\r\n\r\n        // Also emit the selected search ID to the parent\r\n        this.$emit(\"update:selected-search-id\", value);\r\n      }\r\n\r\n    },\r\n    downloadData() {\r\n      if (this.$store.state.userInfo && this.$store.state.userInfo.username && this.$store.state.userInfo.username !== '') {\r\n        // Get current date in YYYYMMDD format\r\n        const now = new Date();\r\n        const year = now.getFullYear();\r\n        const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n        const day = String(now.getDate()).padStart(2, \"0\");\r\n        const dateStr = `${year}${month}${day}`;\r\n\r\n        // Get the selected search name or use a default\r\n        let searchName = \"Search\";\r\n        if (this.localSelectedResult) {\r\n          const selectedSearch = this.results.find(\r\n            (result) => result.id === this.localSelectedResult\r\n          );\r\n          if (selectedSearch) {\r\n            searchName = selectedSearch.id;\r\n          }\r\n        }\r\n\r\n        // Create filename in format Search[number]+[date]\r\n        const filename = `${searchName}_${dateStr}.xlsx`;\r\n\r\n        // Get the table columns in the exact order they appear in the UI\r\n        const tableColumns = [\r\n          \"ID\", \"SiteNo\", \"SiteName\", \"Country\", \"DatingMethod\", \"DatingQuality\",\r\n          \"Epoch\", \"Stage\", \"EarlyInterval\", \"LateInterval\", \"AgeMax\",\r\n          \"AgeMin\", \"AgeMiddle\", \"Author\", \"Pubyr\", \"Longitude\",\r\n          \"Latitude\", \"TimeBin\", \"FossilType\", \"Reference\"\r\n        ];\r\n\r\n        // Use processedTableData but filter to include only the fields shown in the table\r\n        const dataToExport = this.processedTableData.map(item => {\r\n          const filteredItem = {};\r\n          tableColumns.forEach(column => {\r\n            filteredItem[column] = item[column];\r\n          });\r\n          return filteredItem;\r\n        });\r\n\r\n        // Create a worksheet with columns in the same order as the table\r\n        const ws = XLSX.utils.json_to_sheet(dataToExport, {\r\n          header: tableColumns\r\n        });\r\n\r\n        const wb = XLSX.utils.book_new();\r\n        XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\r\n        const wbout = XLSX.write(wb, { bookType: \"xlsx\", type: \"binary\" });\r\n        const blob = new Blob([this.s2ab(wbout)], {\r\n          type: \"application/octet-stream\",\r\n        });\r\n        saveAs(blob, filename);\r\n\r\n\r\n      } else {\r\n        // console.log('未登录')\r\n        this.$message.error('您还未登录，登录成功后才可下载数据！')\r\n        this.SET_OPEN_LOGIN_BOX(true)\r\n      }\r\n\r\n\r\n    },\r\n    s2ab(s) {\r\n      const buf = new ArrayBuffer(s.length);\r\n      const view = new Uint8Array(buf);\r\n      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;\r\n      return buf;\r\n    },\r\n    handleRowClick(row) {\r\n      // Navigate to detail page on row click\r\n      // Note: Row expansion should be handled by clicking the expand icon, not the row itself\r\n      this.$router.push({\r\n        path: `/mapDetail/${row.ID}`,\r\n      });\r\n    },\r\n    handleExpandChange(row, expanded) {\r\n      // 当手动点击展开图标时触发\r\n      console.log('handleExpandChange called:', { rowID: row.ID, expanded, currentChildData: this.childTableData.length });\r\n\r\n      if (expanded) {\r\n        this.currentExpandedRow = row;\r\n        this.fetchChildTableData(row.ID);\r\n      } else {\r\n        this.childTableData = [];\r\n        this.currentExpandedRow = null;\r\n      }\r\n    },\r\n    fetchChildTableData(id) {\r\n      this.childTableLoading = true;\r\n      this.childTableData = [];\r\n\r\n      // 使用新添加的/detail接口获取子表数据\r\n      getDetailData(id)\r\n        .then((response) => {\r\n          console.log('Detail API response for ID', id, ':', response.data);\r\n\r\n          // Handle the R response wrapper structure\r\n          let detailData = [];\r\n          if (response.data && response.data.data && response.data.data.detail) {\r\n            // Java Spring Boot R.ok({detail: [...]}) format\r\n            detailData = response.data.data.detail;\r\n          } else if (response.data && response.data.detail) {\r\n            // Direct {detail: [...]} format\r\n            detailData = response.data.detail;\r\n          } else if (Array.isArray(response.data)) {\r\n            // Direct array format\r\n            detailData = response.data;\r\n          } else if (response.data && Array.isArray(response.data.data)) {\r\n            // R.ok([...]) format (direct array in data)\r\n            detailData = response.data.data;\r\n          }\r\n\r\n          console.log('Extracted detail data:', detailData);\r\n          this.childTableData = detailData;\r\n\r\n          // 格式化日期时间字段\r\n          this.childTableData.forEach((item) => {\r\n            if (item.TimeContext) {\r\n              // 将ISO格式的日期转换为本地日期时间格式\r\n              const date = new Date(item.TimeContext);\r\n              item.TimeContext = date.toLocaleString();\r\n            }\r\n          });\r\n\r\n          this.childTableLoading = false;\r\n        })\r\n        .catch((error) => {\r\n          // console.error(\"获取子表数据失败:\", error);\r\n          this.childTableData = [];\r\n          this.childTableLoading = false;\r\n          this.$message.error(\"获取子表数据失败\");\r\n        });\r\n    },\r\n    updateSelectedResultFromCriteria(criteria) {\r\n      // Find a search result that matches the current criteria\r\n      if (!this.results || this.results.length === 0) return;\r\n\r\n      // Convert criteria to string for comparison\r\n      const criteriaStr = JSON.stringify(criteria);\r\n\r\n      // Try to find an exact match\r\n      for (const result of this.results) {\r\n        if (\r\n          result.criteria &&\r\n          JSON.stringify(result.criteria) === criteriaStr\r\n        ) {\r\n          this.localSelectedResult = result.id;\r\n          return;\r\n        }\r\n      }\r\n\r\n      // If no exact match found, we could either:\r\n      // 1. Leave the current selection as is\r\n      // 2. Clear the selection\r\n      // 3. Set to the most recent result\r\n\r\n      // Option 3: Set to most recent if no match found\r\n      // this.localSelectedResult = this.results[this.results.length - 1].id;\r\n\r\n      // Option 1: Leave as is (do nothing)\r\n    },\r\n    isMapSelection(searchItem) {\r\n      if (!searchItem || !searchItem.criteria) return false;\r\n\r\n      // Check if this search includes map selection\r\n      return (\r\n        (searchItem.criteria.bboxes && searchItem.criteria.bboxes.length > 0) ||\r\n        (searchItem.criteria.polygons && searchItem.criteria.polygons.length > 0) ||\r\n        (searchItem.criteria.circle_centers && searchItem.criteria.circle_centers.length > 0)\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.table-view {\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px - 272px);\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.map-selection {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.table-container {\r\n  overflow: auto;\r\n}\r\n\r\n.table-footer {\r\n  margin-top: 10px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.el-table {\r\n  overflow: auto !important;\r\n}\r\n\r\n.el-table--border {\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.el-table .el-table__header-wrapper th {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 2;\r\n}\r\n\r\n.el-table .el-table__fixed-header-wrapper th {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 3;\r\n}\r\n\r\n/* 子表样式 */\r\n.child-table-container {\r\n  padding: 10px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.child-table-loading {\r\n  min-height: 300px;\r\n  /*display: flex;*/\r\n  /*align-items: center;*/\r\n  /*justify-content: center;*/\r\n  max-width: 100% !important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.no-child-data {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-pagination .el-pager .active {\r\n  background-color: #304f56 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AA+FA,SAAAA,aAAA;AACA,YAAAC,IAAA;AACA,SAAAC,MAAA;AACA,SAAAC,YAAA;AAEA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAC,cAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,EAAAA,CAAA;IACA;IACAG,aAAA;MACAL,IAAA,EAAAC,KAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACAI,gBAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;EACA;EACAM,KAAA;IACAH,aAAA;MACAI,SAAA;MACAC,QAAAC,UAAA;QACA,IAAAA,UAAA,IAAAA,UAAA,CAAAC,MAAA;UACA,KAAAC,OAAA,OAAAF,UAAA;UACA,IACA,KAAAR,cAAA,IACAC,MAAA,CAAAU,IAAA,MAAAX,cAAA,EAAAS,MAAA,MACA;YACA;YACA,KAAAG,gCAAA,MAAAZ,cAAA;UACA,WAAAQ,UAAA,CAAAC,MAAA;YACA;YACA,KAAAI,mBAAA,GAAAL,UAAA,CAAAA,UAAA,CAAAC,MAAA,MAAAK,EAAA;UACA;QACA;MACA;IACA;IACAX,gBAAA;MACAG,SAAA;MACAC,QAAAQ,KAAA;QACA,IAAAA,KAAA;UACA,KAAAF,mBAAA,GAAAE,KAAA;QACA;MACA;IACA;IACAf,cAAA;MACAO,QAAAS,WAAA;QACA;QACA,KAAAC,WAAA;;QAEA;QACA,IAAAD,WAAA,IAAAf,MAAA,CAAAU,IAAA,CAAAK,WAAA,EAAAP,MAAA;UACA,KAAAG,gCAAA,CAAAI,WAAA;QACA;MACA;MACAE,IAAA;IACA;IACAtB,SAAA;MACAW,QAAA;QACA;QACA,KAAAU,WAAA;QACA;QACA,KAAAE,eAAA;QACA,KAAAC,cAAA;MACA;IACA;EACA;EACAC,KAAA;IACA;MACAJ,WAAA;MACAK,QAAA;MACAT,mBAAA;MACAH,OAAA;MACAa,eAAA;MACAJ,eAAA;MAAA;MACAC,cAAA;MAAA;MACAI,iBAAA;MAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,SAAAvB,gBAAA;MACA,KAAAU,mBAAA,QAAAV,gBAAA;IACA,gBAAAD,aAAA,SAAAA,aAAA,CAAAO,MAAA;MACA;MACA,SAAAT,cAAA,IAAAC,MAAA,CAAAU,IAAA,MAAAX,cAAA,EAAAS,MAAA;QACA,KAAAG,gCAAA,MAAAZ,cAAA;MACA;QACA;QACA,KAAAa,mBAAA,GACA,KAAAX,aAAA,MAAAA,aAAA,CAAAO,MAAA,MAAAK,EAAA;MACA;IACA;EACA;EACAa,QAAA;IACAC,cAAA;MACA,MAAAC,KAAA,SAAAZ,WAAA,aAAAK,QAAA;MACA,MAAAQ,GAAA,GAAAD,KAAA,QAAAP,QAAA;MACA;MACA,YAAAS,kBAAA,CAAAC,KAAA,CAAAH,KAAA,EAAAC,GAAA;IACA;IACAC,mBAAA;MACA,UAAAnC,SAAA,SAAAA,SAAA,CAAAa,MAAA;QACA;MACA;;MAEA;MACA,YAAAb,SAAA,CAAAqC,GAAA,CAAAC,IAAA;QACA,MAAAC,aAAA;UAAA,GAAAD;QAAA;;QAEA;QACAC,aAAA,CAAAC,SAAA,QAAAC,aAAA,CAAAH,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAAI,eAAA,IAAAJ,IAAA,CAAAI,eAAA,CAAAC,IAAA;UACA,IAAAJ,aAAA,CAAAC,SAAA;YACAD,aAAA,CAAAC,SAAA,WAAAF,IAAA,CAAAI,eAAA;UACA;YACAH,aAAA,CAAAC,SAAA,GAAAF,IAAA,CAAAI,eAAA;UACA;QACA;QAEA,OAAAH,aAAA;MACA;IACA;IACAK,wBAAA;MACA,UAAApB,cAAA,SAAAA,cAAA,CAAAX,MAAA;QACA;MACA;;MAEA;MACA,YAAAW,cAAA,CAAAa,GAAA,CAAAC,IAAA;QACA,MAAAC,aAAA;UAAA,GAAAD;QAAA;;QAEA;QACAC,aAAA,CAAAM,cAAA,QAAAJ,aAAA,CACAH,IAAA,EACA,kBACA,CACA;;QAEA;QACAC,aAAA,CAAAO,OAAA,QAAAL,aAAA,CAAAH,IAAA;;QAEA;QACAC,aAAA,CAAAQ,UAAA,QAAAN,aAAA,CAAAH,IAAA;QAEA,OAAAC,aAAA;MACA;IACA;EACA;EACAS,OAAA;IACA,GAAAlD,YAAA;IACA;IACA2C,cAAAH,IAAA,EAAAW,aAAA,EAAAC,KAAA;MACA,MAAAC,MAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,IAAAF,KAAA,EAAAE,CAAA;QACA,MAAAC,SAAA,MAAAJ,aAAA,GAAAG,CAAA;QACA,IAAAd,IAAA,CAAAe,SAAA,KAAAf,IAAA,CAAAe,SAAA,EAAAV,IAAA;UACAQ,MAAA,CAAAG,IAAA,CAAAhB,IAAA,CAAAe,SAAA;QACA;MACA;MAEA,OAAAF,MAAA,CAAAI,IAAA;IACA;IACAC,iBAAAC,IAAA;MACA,KAAApC,WAAA,GAAAoC,IAAA;MACA;MACA,KAAAlC,eAAA;MACA,KAAAC,cAAA;IACA;IACAkC,iBAAAC,IAAA;MACA,KAAAjC,QAAA,GAAAiC,IAAA;MACA,KAAAtC,WAAA;MACA;MACA,KAAAE,eAAA;MACA,KAAAC,cAAA;IACA;IACAoC,oBAAAC,KAAA;MACA;MACA,KAAA5C,mBAAA,GAAA4C,KAAA;;MAEA;MACA,MAAAC,cAAA,QAAAhD,OAAA,CAAAiD,IAAA,CAAAC,MAAA,IAAAA,MAAA,CAAA9C,EAAA,KAAA2C,KAAA;MACA,IAAAC,cAAA,IAAAA,cAAA,CAAAG,QAAA;QACA;QACA,KAAAC,KAAA,CACA,0BACAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAP,cAAA,CAAAG,QAAA,EACA;;QAEA;QACA,KAAAC,KAAA,8BAAAL,KAAA;MACA;IAEA;IACAS,aAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,QAAA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,QAAA;QACA;QACA,MAAAC,GAAA,OAAAC,IAAA;QACA,MAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;QACA,MAAAC,KAAA,GAAAvE,MAAA,CAAAmE,GAAA,CAAAK,QAAA,QAAAC,QAAA;QACA,MAAAC,GAAA,GAAA1E,MAAA,CAAAmE,GAAA,CAAAQ,OAAA,IAAAF,QAAA;QACA,MAAAG,OAAA,MAAAP,IAAA,GAAAE,KAAA,GAAAG,GAAA;;QAEA;QACA,IAAAG,UAAA;QACA,SAAApE,mBAAA;UACA,MAAA6C,cAAA,QAAAhD,OAAA,CAAAiD,IAAA,CACAC,MAAA,IAAAA,MAAA,CAAA9C,EAAA,UAAAD,mBACA;UACA,IAAA6C,cAAA;YACAuB,UAAA,GAAAvB,cAAA,CAAA5C,EAAA;UACA;QACA;;QAEA;QACA,MAAAoE,QAAA,MAAAD,UAAA,IAAAD,OAAA;;QAEA;QACA,MAAAG,YAAA,IACA,wEACA,6DACA,uDACA,iDACA;;QAEA;QACA,MAAAC,YAAA,QAAArD,kBAAA,CAAAE,GAAA,CAAAC,IAAA;UACA,MAAAmD,YAAA;UACAF,YAAA,CAAAG,OAAA,CAAAC,MAAA;YACAF,YAAA,CAAAE,MAAA,IAAArD,IAAA,CAAAqD,MAAA;UACA;UACA,OAAAF,YAAA;QACA;;QAEA;QACA,MAAAG,EAAA,GAAAhG,IAAA,CAAAiG,KAAA,CAAAC,aAAA,CAAAN,YAAA;UACAO,MAAA,EAAAR;QACA;QAEA,MAAAS,EAAA,GAAApG,IAAA,CAAAiG,KAAA,CAAAI,QAAA;QACArG,IAAA,CAAAiG,KAAA,CAAAK,iBAAA,CAAAF,EAAA,EAAAJ,EAAA;QACA,MAAAO,KAAA,GAAAvG,IAAA,CAAAwG,KAAA,CAAAJ,EAAA;UAAAK,QAAA;UAAApG,IAAA;QAAA;QACA,MAAAqG,IAAA,OAAAC,IAAA,OAAAC,IAAA,CAAAL,KAAA;UACAlG,IAAA;QACA;QACAJ,MAAA,CAAAyG,IAAA,EAAAhB,QAAA;MAGA;QACA;QACA,KAAAmB,QAAA,CAAAC,KAAA;QACA,KAAAC,kBAAA;MACA;IAGA;IACAH,KAAAI,CAAA;MACA,MAAAC,GAAA,OAAAC,WAAA,CAAAF,CAAA,CAAA/F,MAAA;MACA,MAAAkG,IAAA,OAAAC,UAAA,CAAAH,GAAA;MACA,SAAAzD,CAAA,MAAAA,CAAA,KAAAwD,CAAA,CAAA/F,MAAA,IAAAuC,CAAA,EAAA2D,IAAA,CAAA3D,CAAA,IAAAwD,CAAA,CAAAK,UAAA,CAAA7D,CAAA;MACA,OAAAyD,GAAA;IACA;IACAK,eAAAC,GAAA;MACA;MACA;MACA,KAAAC,OAAA,CAAA9D,IAAA;QACA+D,IAAA,gBAAAF,GAAA,CAAAG,EAAA;MACA;IACA;IACAC,mBAAAJ,GAAA,EAAAK,QAAA;MACA;MACAC,OAAA,CAAAC,GAAA;QAAAC,KAAA,EAAAR,GAAA,CAAAG,EAAA;QAAAE,QAAA;QAAAI,gBAAA,OAAApG,cAAA,CAAAX;MAAA;MAEA,IAAA2G,QAAA;QACA,KAAA3F,kBAAA,GAAAsF,GAAA;QACA,KAAAU,mBAAA,CAAAV,GAAA,CAAAG,EAAA;MACA;QACA,KAAA9F,cAAA;QACA,KAAAK,kBAAA;MACA;IACA;IACAgG,oBAAA3G,EAAA;MACA,KAAAU,iBAAA;MACA,KAAAJ,cAAA;;MAEA;MACA7B,aAAA,CAAAuB,EAAA,EACA4G,IAAA,CAAAC,QAAA;QACAN,OAAA,CAAAC,GAAA,+BAAAxG,EAAA,OAAA6G,QAAA,CAAAtG,IAAA;;QAEA;QACA,IAAAuG,UAAA;QACA,IAAAD,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAAA,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAAA,IAAA,CAAAwG,MAAA;UACA;UACAD,UAAA,GAAAD,QAAA,CAAAtG,IAAA,CAAAA,IAAA,CAAAwG,MAAA;QACA,WAAAF,QAAA,CAAAtG,IAAA,IAAAsG,QAAA,CAAAtG,IAAA,CAAAwG,MAAA;UACA;UACAD,UAAA,GAAAD,QAAA,CAAAtG,IAAA,CAAAwG,MAAA;QACA,WAAA/H,KAAA,CAAAgI,OAAA,CAAAH,QAAA,CAAAtG,IAAA;UACA;UACAuG,UAAA,GAAAD,QAAA,CAAAtG,IAAA;QACA,WAAAsG,QAAA,CAAAtG,IAAA,IAAAvB,KAAA,CAAAgI,OAAA,CAAAH,QAAA,CAAAtG,IAAA,CAAAA,IAAA;UACA;UACAuG,UAAA,GAAAD,QAAA,CAAAtG,IAAA,CAAAA,IAAA;QACA;QAEAgG,OAAA,CAAAC,GAAA,2BAAAM,UAAA;QACA,KAAAxG,cAAA,GAAAwG,UAAA;;QAEA;QACA,KAAAxG,cAAA,CAAAkE,OAAA,CAAApD,IAAA;UACA,IAAAA,IAAA,CAAA6F,WAAA;YACA;YACA,MAAAC,IAAA,OAAAxD,IAAA,CAAAtC,IAAA,CAAA6F,WAAA;YACA7F,IAAA,CAAA6F,WAAA,GAAAC,IAAA,CAAAC,cAAA;UACA;QACA;QAEA,KAAAzG,iBAAA;MACA,GACA0G,KAAA,CAAA5B,KAAA;QACA;QACA,KAAAlF,cAAA;QACA,KAAAI,iBAAA;QACA,KAAA6E,QAAA,CAAAC,KAAA;MACA;IACA;IACA1F,iCAAAiD,QAAA;MACA;MACA,UAAAnD,OAAA,SAAAA,OAAA,CAAAD,MAAA;;MAEA;MACA,MAAA0H,WAAA,GAAApE,IAAA,CAAAE,SAAA,CAAAJ,QAAA;;MAEA;MACA,WAAAD,MAAA,SAAAlD,OAAA;QACA,IACAkD,MAAA,CAAAC,QAAA,IACAE,IAAA,CAAAE,SAAA,CAAAL,MAAA,CAAAC,QAAA,MAAAsE,WAAA,EACA;UACA,KAAAtH,mBAAA,GAAA+C,MAAA,CAAA9C,EAAA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;IACA;IACAsH,eAAAC,UAAA;MACA,KAAAA,UAAA,KAAAA,UAAA,CAAAxE,QAAA;;MAEA;MACA,OACAwE,UAAA,CAAAxE,QAAA,CAAAyE,MAAA,IAAAD,UAAA,CAAAxE,QAAA,CAAAyE,MAAA,CAAA7H,MAAA,QACA4H,UAAA,CAAAxE,QAAA,CAAA0E,QAAA,IAAAF,UAAA,CAAAxE,QAAA,CAAA0E,QAAA,CAAA9H,MAAA,QACA4H,UAAA,CAAAxE,QAAA,CAAA2E,cAAA,IAAAH,UAAA,CAAAxE,QAAA,CAAA2E,cAAA,CAAA/H,MAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}