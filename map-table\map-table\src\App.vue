<template>
    <div id="app">
      <header class="header">
        <div class="header-left">
          <div class="logo" @click="goHome">
            <img  class="logo-img" src="./assets/top-logo.png" alt="">
          </div>
          <ul class="nav">
            <li @click="clickRouter('/')">{{ $t('nav.homeNav') }}</li>
            <li @click="clickRouter('/map')">{{$t('nav.paleoecologyNav')}}</li>
            <li @click="clickRouter('/leafCuticle')"> {{$t('nav.leafCuticlNav')}}</li>
            <li @click="clickRouter('/education')"> {{$t('nav.educationNav')}}</li>
            <li @click="clickRouter('/team')">{{$t('nav.teamNav')}}</li>
            <li  @click="clickRouter('/about')">{{$t('nav.aboutNav')}}</li>
          </ul>
        </div>
        <div class="header-right">
          <div class="lng">
            <div class="en" @click="toggleLang('en')">English</div>
            <span class="line">/</span>
            <div class="zh" @click="toggleLang('zh')">中</div>
          </div>
          <template>
            <div class="user-box" v-if="$store.state.access_token !== ''" >
<!--            <div class="user-box">-->
              <div class="user-avatar" @mouseenter="isShowUserBox = true"><span class="user-icon iconfont icon-wode"></span></div>
              <div class="user-info" v-if="isShowUserBox" @mouseleave="isShowUserBox = false">
                <div class="user-info-item" @click="goUserInfoPage"><span class="info-icon iconfont icon-wode" ></span>{{ $t('login.userInfo') }}</div>
                <div class="user-info-item" @click="logout"><span class="info-icon iconfont icon-tuichu"></span>{{ $t('login.logout') }}</div>
              </div>
            </div>
            <div v-else class="login-btn" @click="goLoginPanle">{{$t('login.loginButton')}}</div>

          </template>
        </div>

      </header>

      <section class="cover" v-if="!$route.meta.isHidden"><div class="cover-name">{{$t(getNameText)}}</div></section>
      <section class="cover customization" v-if="$route.meta.customization"><div class="customization-name">{{$t(getNameText)}}</div></section>
      <template>
        <div class="main" v-if="$route.path !== '/userInfo'">
          <router-view></router-view>
        </div>
        <div class="main-other" v-else>
          <router-view></router-view>
        </div>
      </template>

      <FooterPanle></FooterPanle>
      <LoginPanle v-if="$store.state.openLoginBox"></LoginPanle>
      <RegisterPanle v-if="$store.state.openRegisterBox"></RegisterPanle>
      <ForgotPanle v-if="$store.state.openForgotBox"></ForgotPanle>
    </div>
</template>
<script>
import { mapActions , mapMutations} from 'vuex';
import FooterPanle from "./components/FooterPanle";
import LoginPanle from "@/components/LoginPanle";
import RegisterPanle from "@/components/RegisterPanle";
import ForgotPanle from "@/components/ForgotPanle";
import {checkToken} from "@/api/login";
export default {
  components: {
    FooterPanle,
    LoginPanle,
    RegisterPanle,
    ForgotPanle
  },
  data() {
    return {
      isShowUserBox: false,
      // 刷新token锁
      refreshLock: false,
      // 刷新token的时间
      refreshTime: '',
      // 计时器
      timer: ''
    };
  },
  computed: {
    getNameText() {
      let name = ''
      if(this.$route.path === '/about') {
        name = 'nav.aboutNav'
        return name
      } else if(this.$route.path === '/team' || this.$route.name === 'teamDetail') {
        name = 'nav.teamNav'
        return name
      } else if(this.$route.path === '/education' || this.$route.name === 'educationDetail') {
        name = 'nav.educationNav'
        return name
      } else if(this.$route.path === '/leafCuticle' || this.$route.name === 'leafCuticleDetail' || this.$route.path === '/declaration') {
        name = 'nav.leafCuticlNav'
        return name
      }else if(this.$route.path === '/map' || this.$route.name === 'mapDetail') {
        name = 'nav.paleoecologyNav'
        return name
      }
    }
  },

  created() {
    // 实时检测刷新token
    this.refreshToken()
  },
  destroyed() {
    clearInterval(this.refreshTime)
    clearInterval(this.timer)
  },
  methods: {
    ...mapActions(['changeLanguage']),
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    // 实时检测刷新token
    refreshToken() {
      this.refreshTime = setInterval(() => {
        checkToken(this.refreshTime, this.refreshLock, this.$store)
      }, 60000)
    },
    //跳转到首页
    goHome() {
      this.$router.push('/')
    },
    clickRouter(path) {
      this.$router.push(path)
    },
    toggleLang(lng) {
      this.$i18n.locale = lng
      // this.$store.commit('setLocale', lng);
      this.changeLanguage(lng);
    },
    goLoginPanle() {
      this.SET_OPEN_LOGIN_BOX(true);
    },
    //跳转用户信息页面
    goUserInfoPage() {
      this.$router.push({path: '/userInfo'})
    },
    //退出登录
    logout() {
      //TODO  退出登录接口
      this.$confirm('即将退出登录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push({ path: '/' })
        })
      })
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  position: relative;
  width: 100%;
  height: 100%;
}
header {
  background: rgba(210,197,165,0.7);
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--header-text-color);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
}
.header-left {
  display: flex;
  align-items: center;
}
.header-right {
  display: flex;
  align-items: center;
  position: relative;

}
.login-btn {
  margin-right: 80px;
  border: 1px solid var(--primary-text-color);
  padding: 8px 16px;
  border-radius: 2px;
  cursor: pointer;
}
.user-box {
  margin-right: 80px;
  display: flex;
  align-items: center;
}
.user-avatar {
  width: 48px;
  height: 48px;
  border: 1px solid #304F56;
  border-radius: 50%;
  line-height: 48px;
  margin-right: 8px;
}
.user-avatar .user-icon {
  text-align: center;
  font-size: 30px;
}
.user-info {
  position: absolute;
  top: 80px;
  right: 20px;
  background-color: #fff;
  padding: 20px;
  width: 180px;
  text-align: left;
}
.user-info-item {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  font-size: 16px;
  color: #666666;
}
.user-info-item:hover {
  color: var(--primary-text-color);
}
.info-icon {
  margin-right: 12px;
  font-size: 18px;
}
.logo {
  width: 70px;
  height: 70px;
  margin-left: 80px;
  margin-right: 20px;
  cursor: pointer;

}
.logo-img {
  width: 100%;
  height: 100%;
}
.nav {
  display: flex;
  padding: 0;

}
.nav li {
  margin: 0 20px;
  font-size: 18px;
  cursor: pointer;
}
.lng {
  display: flex;
  margin-right: 16px;
  font-size: 16px;
  color: var(--primary-text-color);
  line-height: 24px;
}
.lng .line {
  margin: 0 5px;
}
.zh ,.en {
  cursor: pointer;
}
.home-bg {
  position: relative;
  top: -100px;
  left: 0;
  z-index: 90;
  width: 100%;
  height: 380px;
  line-height: 380px;
  font-size: 36px;
  color: var(--light-text-color);
  background: url("./assets/top-banner.png") no-repeat ;
  background-size: cover;
}
.cover {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 90;
  width: 100%;
  height: 380px;
  line-height: 380px;
  font-size: 36px;
  color: var(--light-text-color);
  background: url("./assets/top-banner.png") no-repeat ;
  background-size: cover;

}
.customization {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 90;
  width: 100%;
  height: 380px;
  line-height: 380px;
  font-size: 36px;
  color: var(--light-text-color);
  background-size: cover;
  background: url("./assets/top-banner-customization.png") no-repeat  100%;
  background-size: 100%;
}
.cover-name, .customization-name {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-34%);
}
.main {
  padding-top: 280px;
}
.main-other {
  padding-top: 1px;
}
</style>
