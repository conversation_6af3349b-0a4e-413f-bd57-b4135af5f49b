<template>
  <div class="mod-config">
    <basic-container>
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-button v-if="permissions.${moduleName}_${pathName}_add" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button v-if="permissions.${moduleName}_${pathName}_export" icon="el-icon-download" type="primary" plain
                     @click="exportExcel()">导出
          </el-button>
        </el-form-item>
      </el-form>

      <div class="avue-crud">
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading">
          #foreach($column in $columns)
            <el-table-column
                    prop="${column.lowerAttrName}"
                    header-align="center"
                    align="center"
                    label="${column.comments}">
            </el-table-column>
          #end
          <el-table-column
                  header-align="center"
                  align="center"
                  label="操作">
            <template slot-scope="scope">
              <el-button v-if="permissions.${moduleName}_${pathName}_edit" type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.${pk.lowerAttrName})">修改</el-button>
              <el-button v-if="permissions.${moduleName}_${pathName}_del" type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.${pk.lowerAttrName})">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
    </basic-container>
  </div>
</template>

<script>
  import {fetchList, delObj} from '@/api/${pathName}'
  import TableForm from './${pathName}-form'
  import {mapGetters} from 'vuex'

  export default {
    data() {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false
      }
    },
    components: {
      TableForm
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {
      this.getDataList()
    },
    methods: {
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        })).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
      #
        [[this.$nextTick(() => {]]#
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        this.$confirm('是否确认删除ID为' + id, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.$message.success('删除成功')
          this.getDataList()
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/${moduleName}/${pathName}/export', this.searchForm, '${pathName}.xlsx')
      }
    }
  }
</script>
