/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.tdkj.tdcloud.admin.api.dto.XSpecimenDTO;
import com.tdkj.tdcloud.admin.api.entity.*;
import com.tdkj.tdcloud.admin.api.vo.XSpecimenExcelVO;
import com.tdkj.tdcloud.admin.mapper.RupelianMapper;
import com.tdkj.tdcloud.admin.mapper.XEducationMapper;
import com.tdkj.tdcloud.admin.mapper.XSpecimenMapper;
import com.tdkj.tdcloud.admin.mapper.XSpecimenTreeMapper;
import com.tdkj.tdcloud.admin.service.XSpecimenService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.vo.ErrorMessage;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 标本信息表，存储标本的基本信息
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:35:18
 */
@Service
public class XSpecimenServiceImpl extends ServiceImpl<XSpecimenMapper, XSpecimen> implements XSpecimenService {

	@Resource
	private XSpecimenMapper xSpecimenMapper;
	@Resource
	private XSpecimenTreeMapper xSpecimenTreeMapper;
	@Resource
	private XEducationMapper xEducationMapper;
	@Resource
	private RupelianMapper rupelianMapper;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	private java.util.logging.Logger log;

	// 定义一个特殊的 XSpecimen 实例表示未找到
	public static final XSpecimen NOT_FOUND_SPECIMEN = new XSpecimen();
	public static final XSpecimenTree NOT_FOUND_SPECIMEN_TREE = new XSpecimenTree();
	// 缓存查询结果
	private Cache<String, XSpecimen> specimenCache = CacheBuilder.newBuilder()
			.maximumSize(1000)
			.build();
	private Cache<String, XSpecimenTree> treeCache = CacheBuilder.newBuilder()
			.maximumSize(1000)
			.build();

	/**
	 * 导入
	 * @param excelVOList
	 * @param bindingResult
	 * @return
	 * @throws Exception
	 */
	@Transactional
	@Override
	public R importSpecimen(List<XSpecimenExcelVO> excelVOList, BindingResult bindingResult) throws Exception {

		stringRedisTemplate.delete("xSpecimenTreeList");
		// 通用校验获取失败的数据
		List<ErrorMessage> errorMessageList = new ArrayList<>();

		List<XSpecimenExcelVO> insertSpecimenList = new ArrayList<>();
		List<XSpecimenExcelVO> updateSpecimenList = new ArrayList<>();
		List<XSpecimenTree> insertTreeList = new ArrayList<>();


		// 遍历数据，区分插入和更新操作
		for (XSpecimenExcelVO excel : excelVOList) {
			String speciesEn = excel.getSpeciesEn();
			if (speciesEn == null) {
				log.info("speciesEn is null, skipping this entry.");
				continue;
			}

			try {
				XSpecimen specimen = specimenCache.get(speciesEn, () -> {
					if (xSpecimenMapper != null) {
						XSpecimen result = xSpecimenMapper.selectXSpecimenBySpeciesEn(speciesEn);
						if (result == null) {
							return NOT_FOUND_SPECIMEN;
						}
						return result;
					} else {
						throw new RuntimeException("xSpecimenMapper is null");
					}
				});
				if (specimen == NOT_FOUND_SPECIMEN) {
					excel.setCreateTime(LocalDateTime.now());
					excel.setLanguageType("en");
					insertSpecimenList.add(excel);

					// 处理科
					processTreeLevel(excel.getFamilyEn(), excel.getFamilyCn(), "root", "科", insertTreeList);
					// 处理属
					processTreeLevel(excel.getGenusEn(), excel.getGenusCn(), excel.getFamilyEn(), "属", insertTreeList);
					// 处理种
					processTreeLevel(excel.getSpeciesEn(), excel.getSpeciesCn(), excel.getGenusEn(), "种", insertTreeList);
				} else {
					excel.setId(specimen.getId());
					updateSpecimenList.add(excel);

					// 处理科
					processTreeLevel(excel.getFamilyEn(), excel.getFamilyCn(), "root", "科", insertTreeList);
					// 处理属
					processTreeLevel(excel.getGenusEn(), excel.getGenusCn(), excel.getFamilyEn(), "属", insertTreeList);
					// 处理种
					processTreeLevel(excel.getSpeciesEn(), excel.getSpeciesCn(), excel.getGenusEn(), "种", insertTreeList);
				}
			} catch (Exception e) {
				// 处理异常
				System.err.println("Error getting specimen from cache: " + e.getMessage());
			}

		}

		// 批量插入标本数据
		if (!insertSpecimenList.isEmpty()) {
			if (xSpecimenMapper == null) {
				log.info("xSpecimenMapper is null, cannot perform batch insert.");
				return R.failed("xSpecimenMapper is null");
			}
			xSpecimenMapper.batchInsertXSpecimenExcel(insertSpecimenList);
		}

		// 批量更新标本数据
		for (XSpecimenExcelVO excel : updateSpecimenList) {
			if (xSpecimenMapper == null) {
				log.info("xSpecimenMapper is null, cannot perform update.");
				return R.failed("xSpecimenMapper is null");
			}
			xSpecimenMapper.updateXSpecimenExcel(excel);
		}

		// 批量插入树数据
		if (!insertTreeList.isEmpty()) {

			if (xSpecimenTreeMapper == null) {
				log.info("xSpecimenTreeMapper is null, cannot perform batch insert.");
				return R.failed("xSpecimenTreeMapper is null");
			}
			xSpecimenTreeMapper.batchInsertXSpecimenTree(insertTreeList);
		}

		if (!errorMessageList.isEmpty()) {
			return R.failed(errorMessageList);
		}
		return R.ok();
	}

	@Override
	public Page getXSpecimenPage(Page page, XSpecimenDTO xSpecimenDTO) {
		//查询条件
		QueryWrapper<XSpecimen> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}

		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getSpecimenNo()), "specimen_no", xSpecimenDTO.getSpecimenNo());
		//wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getChineseName()), "chinese_name", xSpecimenDTO.getOriginal());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getSpeciesEn()), "species_en", xSpecimenDTO.getSpeciesEn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getSpeciesCn()), "species_cn", xSpecimenDTO.getSpeciesCn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getGenusEn()), "genus_en", xSpecimenDTO.getGenusEn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getGenusCn()), "genus_cn", xSpecimenDTO.getGenusCn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getFamilyEn()), "family_en", xSpecimenDTO.getFamilyEn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getFamilyCn()), "family_cn", xSpecimenDTO.getFamilyCn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getCollectionPlace()), "collection_place", xSpecimenDTO.getCollectionPlace());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getPlaceEn()), "place_en", xSpecimenDTO.getPlaceEn());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getCollectionTime()), "collection_time", xSpecimenDTO.getCollectionTime());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getImportBatch()), "import_batch", xSpecimenDTO.getImportBatch());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getCollector()), "collector", xSpecimenDTO.getCollector());
		wrapper.like(StringUtils.isNotBlank(xSpecimenDTO.getCollectorEn()), "collector_en", xSpecimenDTO.getCollectorEn());
		wrapper.eq(StringUtils.isNotBlank(xSpecimenDTO.getLanguageType()), "language_type", xSpecimenDTO.getLanguageType());

		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);
		List<XSpecimen> xSpecimenList = page1.getRecords();
		//图片列表
		List<SysFile> imageList = xSpecimenMapper.getSpecimenImageList("specimen");
		if (xSpecimenList!=null && xSpecimenList.size()>0){
			for (XSpecimen xSpecimen : xSpecimenList){
				if (imageList!=null && imageList.size()>0){
					for (SysFile sf : imageList){
						if (sf.getOriginal().contains(xSpecimen.getSpecimenNo() + "_" + "up" + "_")){
							xSpecimen.getUpSysFileList().add(sf);
						}
						if (sf.getOriginal().contains(xSpecimen.getSpecimenNo() + "_" + "lw" + "_")){
							xSpecimen.getUpSysFileList().add(sf);
						}
					}
				}
			}
		}


		return page1;
	}

	@Override
	public R getXSpecimenTree(String parentEn) {
		ObjectMapper objectMapper = new ObjectMapper();

		// 添加这行代码，注册JavaTimeModule来处理Java 8日期时间类型
		objectMapper.registerModule(new JavaTimeModule());

		String speciesTreesJson = stringRedisTemplate.opsForValue().get("xSpecimenTreeList");

		if (speciesTreesJson == null || "".equals(speciesTreesJson) || "[]".equals(speciesTreesJson)) {
			List<XSpecimenTree> tree = xSpecimenTreeMapper.getSpecimenTreeByParentEn(parentEn);
			if (tree != null && !tree.isEmpty()) {
				buildTreeRecursively(tree);
			}
			try {
				String json = objectMapper.writeValueAsString(tree);
				stringRedisTemplate.opsForValue().set("xSpecimenTreeList", json);
			} catch (JsonProcessingException e) {
				// 处理JSON序列化异常，比如记录日志等
				e.printStackTrace();
			}
			return R.ok(tree, "数据库树数据");
		} else {
			List<XSpecimenTree> speciesTreesRedis = null;
			try {
				speciesTreesRedis = objectMapper.readValue(speciesTreesJson, objectMapper.getTypeFactory().constructCollectionType(List.class, XSpecimenTree.class));
			} catch (JsonProcessingException e) {
				// 处理JSON反序列化异常，比如记录日志等
				e.printStackTrace();
			}
			return R.ok(speciesTreesRedis, "redis数据");

		}

	}

	private void buildTreeRecursively(List<XSpecimenTree> nodes) {
		for (XSpecimenTree node : nodes) {
			// 处理节点名称
			if (node.getNameCn() == null) {
				node.setNameCn("");
			}
			node.setNameCnNameEn(node.getNameCn() + " " + node.getNameEn());

			// 递归处理子节点
			List<XSpecimenTree> children = xSpecimenTreeMapper.getSpecimenTreeByParentEn(node.getNameEn());
			node.setChild(children);

			// 统计子节点数量
			int childCount = (children != null) ? children.size() : 0;
			node.setChildCount(childCount); // 假设XSpecimenTree类有setChildCount方法

			if (children != null && !children.isEmpty()) {
				buildTreeRecursively(children);
			}
		}
	}

	@Override
	public R getSpecimenCount() {
		Map<String,Object> map = new HashMap<>();

		int specimenNum = xSpecimenMapper.selectXSpecimenTotal();
		int collectionPlaceTotal = xSpecimenMapper.getSpecimenCollectionPlaceTotal();
		int publicationTotal = xEducationMapper.getPublicationTotal("zh", "publication");
		int distributionTotal = rupelianMapper.getRupelianSiteNameTotal();
		map.put("specimenNum",specimenNum);
		map.put("collectionPlaceTotal",collectionPlaceTotal);
		map.put("publicationTotal",publicationTotal);
		map.put("distributionTotal",distributionTotal);
		//柱状图
		Epoch rupelianEpochTotal = rupelianMapper.getRupelianEpochTotal();
		map.put("rupelianEpochTotal",rupelianEpochTotal);
		List<Fossil> rupelianFossilTotal = rupelianMapper.getRupelianFossilTotal();
		map.put("rupelianFossilTotal",rupelianFossilTotal);

		return R.ok(map,"数据统计");
	}

	@Override
	public R getSpecimenVisits() {
		xSpecimenMapper.updateVisits();
		int visits = xSpecimenMapper.selectVisits();
		return R.ok(visits,"访问量");
	}

	private void processTreeLevel(String nameEn, String nameCn, String parentEn, String belongLevel, List<XSpecimenTree> insertTreeList) throws ExecutionException {
		if (nameEn == null || parentEn == null) {
			log.info("nameEn or parentEn is null, skipping this entry.");
			return;
		}
		String cacheKey = nameEn + "-" + parentEn;
		XSpecimenTree tree = treeCache.get(cacheKey, () -> {
			if (xSpecimenTreeMapper == null) {
				log.info("xSpecimenTreeMapper is null, cannot query database.");
				return null;
			}
			XSpecimenTree result = xSpecimenTreeMapper.getSpecimenTreeByNameEn(nameEn, parentEn);
			if (result == null) {
				// 处理 null 值，这里可以抛出异常或者返回一个特殊对象
//				throw new RuntimeException("No tree found for nameEn: " + nameEn + " and parentEn: " + parentEn);
				return NOT_FOUND_SPECIMEN_TREE;
			}
			return result;
		});
		if (tree == NOT_FOUND_SPECIMEN_TREE) {
			XSpecimenTree newTree = new XSpecimenTree();
			newTree.setCreateTime(LocalDateTime.now());
			newTree.setNameEn(nameEn);
			newTree.setNameCn(nameCn);
			newTree.setParentEn(parentEn);
			newTree.setBelongLevel(belongLevel);

			boolean isDuplicate = false;
			for (XSpecimenTree existingTree : insertTreeList) {
				if (existingTree.getNameEn().equals(newTree.getNameEn()) && existingTree.getParentEn().equals(newTree.getParentEn())) {
					isDuplicate = true;
					break;
				}
			}

			if (!isDuplicate) {
				insertTreeList.add(newTree);
			}
		} else {
			tree.setNameEn(nameEn);
			tree.setNameCn(nameCn);
			tree.setParentEn(parentEn);
			if (xSpecimenTreeMapper == null) {
				log.info("xSpecimenTreeMapper is null, cannot perform update.");
				return;
			}
			xSpecimenTreeMapper.updateXSpecimenTree(tree);
		}
	}
}
