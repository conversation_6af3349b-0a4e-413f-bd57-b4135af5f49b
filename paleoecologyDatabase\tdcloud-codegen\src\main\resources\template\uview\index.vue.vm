<template>
  <view class="wrap">
    <scroll-view class="scroll-list" scroll-y="true" @scrolltolower="loadMore">
      <u-cell-group class="list" :border="false">
        <u-card :title="item.${pk.lowerAttrName}"  v-for="(item, index) in list" :key="item.${pk.lowerAttrName}"
                :index="item.${pk.lowerAttrName}" @click="cardClick(item.${pk.lowerAttrName})">
          <view class="" slot="body">
            #foreach($column in $columns)
              <view class="row-list">
                <span class="span-lable">${column.comments}: </span>{{item.${column.lowerAttrName}}}
              </view>
            #end
          </view>
          <view class="card-foot" slot="foot">
            <u-button type="primary" size="medium" @click="cardClick(item.${pk.lowerAttrName})">编辑</u-button>
            <u-button size="medium" @click="del(item.${pk.lowerAttrName})">删除</u-button>
          </view>
        </u-card>
      </u-cell-group>
      <view class="loadmore" @click="loadMore">
        <u-loadmore :status="loadStatus"></u-loadmore>
      </view>
    </scroll-view>
    <view class="btn-plus" @click="cardClick()">
      <u-icon name="plus-circle-fill" size="90" color="#3d87ff"></u-icon>
    </view>
  </view>
</template>
<script>
  export default {
    data() {
      return {
        keywords: '',
        query: {
          current: 1,
          size: 20
        },
        list: [],
        count: 0,
        loadStatus: 'loadmore',
        options: [{
          text: '删除',
          style: {
            background: '#dd524d'
          }
        }]
      };
    },
    onLoad() {
      this.loadList();
    },
    onShow() {
      if (uni.getStorageSync('refreshList') === true) {
        uni.removeStorageSync('refreshList');
        this.search('');
      }
    },
    methods: {
      cardClick(id) {
        uni.navigateTo({
          url: '/pages/${moduleName}/${pathName}/${pathName}-form?id='+id
        })
      },
      loadMore() {
        this.loadStatus = "loading";
        setTimeout(() => {
          this.query.current += 1;
          this.loadList();
        }, 100);
      },
      loadList() {
      #[[this.$u.api]]#.tdcloud${className}.fetchList(this.query).then(res => {
          if (!res.data.records || res.data.records.length == 0) {
            this.loadStatus = "nomore";
            return;
          }
          this.list = this.list.concat(res.data.records);
          this.total = res.data.total;
          this.query.current = res.data.current;
          this.query.size = res.data.size;
          this.loadStatus = "loadmore";
        });
      },
      del(id) {
        let self = this;
        uni.showModal({
          title: '提示',
          content: '确认要删除该数据吗？',
          showCancel: true,
          success: function(res2) {
            if (res2.confirm) {
            #[[self.$u.api]]#.tdcloud${className}.delObj({id:id}).then(res => {
              #[[self.$u.toast('删除成功');]]#
                self.query.current = 1
                self.list = []
                self.loadList()
              });
            }
          }
        });
      }
    }
  };
</script>
