// const { defineConfig } = require('@vue/cli-service')
// module.exports = defineConfig({
//   transpileDependencies: true,
//   lintOnSave: false,
//
// })
module.exports = {
  configureWebpack:{
    resolve:{
      alias:{
        'assets':'@/assets',
        'common':'@/common',
        'components':'@/components',
        'api':'@/api',
        'views':'@/views',
        'styles':'@/styles'
      }
    },
  },
  publicPath:process.env.NODE_ENV === "production" ? "/" : "/",

  outputDir: "dist",

  assetsDir: "static",

  indexPath: "index.html",

  filenameHashing: false,
  lintOnSave: false,
  //如果你想要在生产构建时禁用 eslint-loader，你可以用如下配置
  // lintOnSave: process.env.NODE_ENV !== 'production',

  //是否使用包含运行时编译器的 Vue 构建版本。设置为 true 后你就可以在 Vue 组件中使用 template 选项了，但是这会让你的应用额外增加 10kb 左右。(默认false)
  // runtimeCompiler: false,

  /**
   * 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
   *  打包之后
   *  发现map文件过大，项目文件体积很大，设置为false就可以不输出map文件
   *  map文件的作用在于：项目打包后，代码都是经过压缩加密的，如果运行时报错，输出的错误信息无法准确得知是哪里的代码报错。
   *  有了map就可以像未加密的代码一样，准确的输出是哪一行哪一列有错。
   * */
  productionSourceMap: false,

  // 它支持webPack-dev-server的所有选项
  devServer: {
    // disableHostCheck: true,
    host: "0.0.0.0",
    port: 8181,
    https: false, //
    open: true, //配置自动启动浏览器
    webSocketServer: false, // 禁用 WebSocket 服务
    proxy:{
      '/': {
        target: 'http://localhost:8888',//Java Spring Boot backend for paleoecology data
        // target: 'http://**************:8888',//此处可以换成自己需要的地址
        // target: 'http://************:8888',//此处可以换成自己需要的地址
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/'
        }
      },
    },

  }
}
