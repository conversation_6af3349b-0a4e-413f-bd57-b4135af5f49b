<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.XTeamMapper">

  <resultMap id="xTeamMap" type="com.tdkj.tdcloud.admin.api.entity.XTeam">
        <id property="id" column="id"/>
        <result property="personName" column="person_name"/>
        <result property="academicTitle" column="academic_title"/>
        <result property="title" column="title"/>
        <result property="email" column="email"/>
        <result property="postalCode" column="postal_code"/>
        <result property="mailingAddress" column="mailing_address"/>
        <result property="resume" column="resume"/>
        <result property="publications" column="publications"/>
        <result property="languageType" column="language_type"/>
        <result property="createTime" column="create_time"/>
        <result property="url" column="url"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="sort" column="sort"/>
  </resultMap>

	<sql id="selectXTeamVo">
		select id, person_name, academic_title,sort, title, email, postal_code, mailing_address, resume, publications, language_type, create_time, url, name, type from x_team
	</sql>

	<select id="selectXTeamList" parameterType="com.tdkj.tdcloud.admin.api.entity.XTeam" resultMap="xTeamMap">
		<include refid="selectXTeamVo"/>
		<where>
			<if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
			<if test="academicTitle != null  and academicTitle != ''"> and academic_title = #{academicTitle}</if>
			<if test="title != null  and title != ''"> and title = #{title}</if>
			<if test="email != null  and email != ''"> and email = #{email}</if>
			<if test="postalCode != null  and postalCode != ''"> and postal_code = #{postalCode}</if>
			<if test="mailingAddress != null  and mailingAddress != ''"> and mailing_address = #{mailingAddress}</if>
			<if test="resume != null  and resume != ''"> and resume = #{resume}</if>
			<if test="publications != null  and publications != ''"> and publications = #{publications}</if>
			<if test="languageType != null  and languageType != ''"> and language_type = #{languageType}</if>
			<if test="url != null  and url != ''"> and url = #{url}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
			<if test="type != null  and type != ''"> and type = #{type}</if>
			<if test="sort != null  and sort != ''"> and sort = #{sort}</if>
		</where>
	</select>

	<select id="selectXTeamById" parameterType="Long" resultMap="xTeamMap">
		<include refid="selectXTeamVo"/>
		where id = #{id}
	</select>

	<select id="getFileByXId" resultType="com.tdkj.tdcloud.admin.api.entity.SysFile">
		SELECT id,file_name,name,x_type,x_id,bucket_name,original,url,type FROM sys_file
		where x_id = #{xId} and x_type = #{xType} and del_flag = '0'
	</select>

	<insert id="insertXTeam" parameterType="com.tdkj.tdcloud.admin.api.entity.XTeam" useGeneratedKeys="true" keyProperty="id">
		insert into x_team
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="personName != null">person_name,</if>
			<if test="academicTitle != null">academic_title,</if>
			<if test="title != null">title,</if>
			<if test="email != null">email,</if>
			<if test="postalCode != null">postal_code,</if>
			<if test="mailingAddress != null">mailing_address,</if>
			<if test="resume != null">resume,</if>
			<if test="publications != null">publications,</if>
			<if test="languageType != null">language_type,</if>
			<if test="createTime != null">create_time,</if>
			<if test="url != null">url,</if>
			<if test="name != null">name,</if>
			<if test="type != null">type,</if>
			<if test="sort != null">sort,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="personName != null">#{personName},</if>
			<if test="academicTitle != null">#{academicTitle},</if>
			<if test="title != null">#{title},</if>
			<if test="email != null">#{email},</if>
			<if test="postalCode != null">#{postalCode},</if>
			<if test="mailingAddress != null">#{mailingAddress},</if>
			<if test="resume != null">#{resume},</if>
			<if test="publications != null">#{publications},</if>
			<if test="languageType != null">#{languageType},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="url != null">#{url},</if>
			<if test="name != null">#{name},</if>
			<if test="type != null">#{type},</if>
			<if test="sort != null">#{sort},</if>
		</trim>
	</insert>

	<update id="updateXTeam" parameterType="com.tdkj.tdcloud.admin.api.entity.XTeam">
		update x_team
		<trim prefix="SET" suffixOverrides=",">
			<if test="personName != null">person_name = #{personName},</if>
			<if test="academicTitle != null">academic_title = #{academicTitle},</if>
			<if test="title != null">title = #{title},</if>
			<if test="email != null">email = #{email},</if>
			<if test="postalCode != null">postal_code = #{postalCode},</if>
			<if test="mailingAddress != null">mailing_address = #{mailingAddress},</if>
			<if test="resume != null">resume = #{resume},</if>
			<if test="publications != null">publications = #{publications},</if>
			<if test="languageType != null">language_type = #{languageType},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="url != null">url = #{url},</if>
			<if test="name != null">name = #{name},</if>
			<if test="type != null">type = #{type},</if>
			<if test="sort != null">sort = #{sort},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteXTeamById" parameterType="Long">
		delete from x_team where id = #{id}
	</delete>

	<delete id="deleteXTeamByIds" parameterType="String">
		delete from x_team where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>
