import Vue from 'vue'
import App from './App.vue'
import router from './router'
import './assets/css/setting.css'
import './assets/css/normal.css'
import './assets/css/iconfont.scss'
import axios from "axios";
import i18n from './i18n/index'
import ElementUI from 'element-ui';
import './assets/css/theme/theme/index.css'
Vue.use(ElementUI);
import store from './store'
//富文本编辑器
import VueQuillEditor from 'vue-quill-editor'
// 引入样式
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
Vue.use(VueQuillEditor, /* { 默认全局 } */)
Vue.config.productionTip = false

new Vue({
  router,
  store,
  i18n,
  axios,
  render: h => h(App),
}).$mount('#app')
