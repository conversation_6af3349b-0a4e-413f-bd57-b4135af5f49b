/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.util;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Geographic utility class for spatial calculations
 * Ported from Python newDB.py geographic functions
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
public class GeographicUtils {

    /**
     * Point class to represent longitude/latitude coordinates
     */
    public static class Point {
        public double lng;
        public double lat;

        public Point(double lng, double lat) {
            this.lng = lng;
            this.lat = lat;
        }

        @Override
        public String toString() {
            return "[" + lng + ", " + lat + "]";
        }
    }

    /**
     * Check if a point is inside a polygon using ray casting algorithm
     * Ported from Python point_in_polygon function
     *
     * @param point   Point to check [lng, lat]
     * @param polygon List of polygon vertices [lng, lat]
     * @return true if point is inside polygon
     */
    public static boolean pointInPolygon(Point point, List<Point> polygon) {
        // Polygon must have at least 3 points
        if (polygon.size() < 3) {
            log.debug("Polygon has less than 3 points: {}", polygon.size());
            return false;
        }

        // Check if point is within polygon bounding box
        double minLng = polygon.stream().mapToDouble(p -> p.lng).min().orElse(Double.MAX_VALUE);
        double maxLng = polygon.stream().mapToDouble(p -> p.lng).max().orElse(Double.MIN_VALUE);
        double minLat = polygon.stream().mapToDouble(p -> p.lat).min().orElse(Double.MAX_VALUE);
        double maxLat = polygon.stream().mapToDouble(p -> p.lat).max().orElse(Double.MIN_VALUE);

        if (point.lng < minLng || point.lng > maxLng || point.lat < minLat || point.lat > maxLat) {
            log.debug("Point {} is outside polygon bounding box [{}, {}, {}, {}]", 
                     point, minLng, minLat, maxLng, maxLat);
            return false;
        }

        // Ray casting algorithm
        boolean inside = false;
        double x = point.lng;
        double y = point.lat;

        for (int i = 0; i < polygon.size(); i++) {
            int j = (i - 1 + polygon.size()) % polygon.size();
            Point pi = polygon.get(i);
            Point pj = polygon.get(j);

            // Check if point is on polygon vertex
            if ((pi.lat == y && pi.lng == x) || (pj.lat == y && pj.lng == x)) {
                log.debug("Point {} is on polygon vertex", point);
                return true;
            }

            // Check if point is on polygon edge
            if (pi.lat == pj.lat && y == pi.lat && 
                x >= Math.min(pi.lng, pj.lng) && x <= Math.max(pi.lng, pj.lng)) {
                log.debug("Point {} is on polygon edge", point);
                return true;
            }

            // Check if ray intersects with edge
            if (((pi.lat > y) != (pj.lat > y)) && 
                (x < (pj.lng - pi.lng) * (y - pi.lat) / (pj.lat - pi.lat) + pi.lng)) {
                inside = !inside;
            }
        }

        log.debug("Point {} is {} polygon with {} points", 
                 point, inside ? "inside" : "outside", polygon.size());
        return inside;
    }

    /**
     * Normalize polygon coordinates to ensure it's closed and has correct orientation
     * Ported from Python normalize_polygon function
     *
     * @param polygon List of polygon vertices
     * @return Normalized polygon
     */
    public static List<Point> normalizePolygon(List<Point> polygon) {
        // Ensure polygon has at least 3 points
        if (polygon.size() < 3) {
            log.warn("Polygon has less than 3 points: {}", polygon.size());
            return polygon;
        }

        // Ensure polygon is closed (first point equals last point)
        Point first = polygon.get(0);
        Point last = polygon.get(polygon.size() - 1);
        if (first.lng != last.lng || first.lat != last.lat) {
            polygon.add(new Point(first.lng, first.lat));
            log.debug("Closed polygon by adding first point to end");
        }

        // Calculate polygon area to determine vertex order
        double area = 0.0;
        for (int i = 0; i < polygon.size() - 1; i++) {
            Point p1 = polygon.get(i);
            Point p2 = polygon.get(i + 1);
            area += (p1.lng * p2.lat - p2.lng * p1.lat);
        }

        // If area is negative, reverse vertex order
        if (area < 0) {
            // Reverse the list except the last point (which is duplicate of first)
            for (int i = 0, j = polygon.size() - 2; i < j; i++, j--) {
                Point temp = polygon.get(i);
                polygon.set(i, polygon.get(j));
                polygon.set(j, temp);
            }
            log.debug("Reversed polygon vertices to ensure correct orientation");
        }

        return polygon;
    }

    /**
     * Calculate distance between two points using Haversine formula
     * Ported from Python haversine_distance function
     *
     * @param lat1 Latitude of first point
     * @param lon1 Longitude of first point
     * @param lat2 Latitude of second point
     * @param lon2 Longitude of second point
     * @return Distance in meters
     */
    public static double haversineDistance(double lat1, double lon1, double lat2, double lon2) {
        // Convert degrees to radians
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // Haversine formula
        double dlon = lon2Rad - lon1Rad;
        double dlat = lat2Rad - lat1Rad;
        double a = Math.sin(dlat / 2) * Math.sin(dlat / 2) + 
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) * 
                   Math.sin(dlon / 2) * Math.sin(dlon / 2);
        double c = 2 * Math.asin(Math.sqrt(a));
        double r = 6371000; // Earth radius in meters
        return c * r;
    }

    /**
     * Convert meters to approximate degrees (for bounding box calculations)
     * 1 degree ≈ 111km at equator
     *
     * @param meters Distance in meters
     * @return Approximate degrees
     */
    public static double metersToDegrees(double meters) {
        return meters / 111000.0;
    }
}
