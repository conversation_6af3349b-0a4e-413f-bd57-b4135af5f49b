<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.XAboutUsMapper">

  <resultMap id="xAboutUsMap" type="com.tdkj.tdcloud.admin.api.entity.XAboutUs">
        <id property="id" column="id"/>
        <result property="languageType" column="language_type"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="postalCode" column="postal_code"/>
        <result property="createTime" column="create_time"/>
        <result property="name" column="name"/>
        <result property="url" column="url"/>
  </resultMap>

	<sql id="selectXAboutUsVo">
		select id, language_type, type, content, phone, address, postal_code, create_time, name, url from x_about_us
	</sql>

	<select id="selectXAboutUsList" parameterType="com.tdkj.tdcloud.admin.api.entity.XAboutUs" resultMap="xAboutUsMap">
		<include refid="selectXAboutUsVo"/>
		<where>
			<if test="languageType != null  and languageType != ''"> and language_type = #{languageType}</if>
			<if test="type != null  and type != ''"> and type = #{type}</if>
			<if test="content != null  and content != ''"> and content = #{content}</if>
			<if test="phone != null  and phone != ''"> and phone = #{phone}</if>
			<if test="address != null  and address != ''"> and address = #{address}</if>
			<if test="postalCode != null  and postalCode != ''"> and postal_code = #{postalCode}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
			<if test="url != null  and url != ''"> and url = #{url}</if>
		</where>
	</select>

	<select id="selectXAboutUsById" parameterType="Long" resultMap="xAboutUsMap">
		<include refid="selectXAboutUsVo"/>
		where id = #{id}
	</select>

	<insert id="insertXAboutUs" parameterType="com.tdkj.tdcloud.admin.api.entity.XAboutUs" useGeneratedKeys="true" keyProperty="id">
		insert into x_about_us
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="languageType != null">language_type,</if>
			<if test="type != null">type,</if>
			<if test="content != null">content,</if>
			<if test="phone != null">phone,</if>
			<if test="address != null">address,</if>
			<if test="postalCode != null">postal_code,</if>
			<if test="createTime != null">create_time,</if>
			<if test="name != null">name,</if>
			<if test="url != null">url,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="languageType != null">#{languageType},</if>
			<if test="type != null">#{type},</if>
			<if test="content != null">#{content},</if>
			<if test="phone != null">#{phone},</if>
			<if test="address != null">#{address},</if>
			<if test="postalCode != null">#{postalCode},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="name != null">#{name},</if>
			<if test="url != null">#{url},</if>
		</trim>
	</insert>

	<update id="updateXAboutUs" parameterType="com.tdkj.tdcloud.admin.api.entity.XAboutUs">
		update x_about_us
		<trim prefix="SET" suffixOverrides=",">
			<if test="languageType != null">language_type = #{languageType},</if>
			<if test="type != null">type = #{type},</if>
			<if test="content != null">content = #{content},</if>
			<if test="phone != null">phone = #{phone},</if>
			<if test="address != null">address = #{address},</if>
			<if test="postalCode != null">postal_code = #{postalCode},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="name != null">name = #{name},</if>
			<if test="url != null">url = #{url},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteXAboutUsById" parameterType="Long">
		delete from x_about_us where id = #{id}
	</delete>

	<delete id="deleteXAboutUsByIds" parameterType="String">
		delete from x_about_us where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>
