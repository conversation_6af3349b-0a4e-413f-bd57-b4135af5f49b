/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.tdkj.tdcloud.admin.api.dto.PaleoecologyDataDTO;
import com.tdkj.tdcloud.admin.service.PaleoecologyDataService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * Paleoecology Data Controller
 * REST endpoints for paleoecology data queries and lookups
 * Migrated from Python Flask server newDB.py
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/paleoecology")
@Tag(description = "paleoecology", name = "Paleoecology Data Management")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PaleoecologyDataController {

    private final PaleoecologyDataService paleoecologyDataService;

    /**
     * Main data query endpoint with complex filtering
     * Equivalent to Python Flask /data endpoint
     * Supports taxonomic, temporal, and geographic filtering
     */
    @Inner(value = false)
    @Operation(summary = "Query paleoecology data", description = "Complex data query with taxonomic, temporal, and geographic filtering")
    @GetMapping("/data")
    public R getData(PaleoecologyDataDTO queryParams) {
        log.debug("Received paleoecology data query: {}", queryParams);
        return paleoecologyDataService.getData(queryParams);
    }

    /**
     * Get detail data from Chattian table by ID
     * Equivalent to Python Flask /detail endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get detail data", description = "Get detailed Chattian table data by ID")
    @GetMapping("/detail")
    public R getDetailData(@RequestParam("id") String id) {
        log.debug("Received detail data request for ID: {}", id);
        return paleoecologyDataService.getDetailData(id);
    }

    /**
     * Get unique dating method values
     * Equivalent to Python Flask /dating-methods endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get dating methods", description = "Get unique dating method values from Rupelian table")
    @GetMapping("/dating-methods")
    public R getDatingMethods() {
        return paleoecologyDataService.getDatingMethods();
    }

    /**
     * Get unique dating quality values
     * Equivalent to Python Flask /dating-qualities endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get dating qualities", description = "Get unique dating quality values from Rupelian table")
    @GetMapping("/dating-qualities")
    public R getDatingQualities() {
        return paleoecologyDataService.getDatingQualities();
    }

    /**
     * Get unique family values
     * Equivalent to Python Flask /taxa/families endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get families", description = "Get unique family values from Chattian table")
    @GetMapping("/taxa/families")
    public R getFamilies() {
        return paleoecologyDataService.getFamilies();
    }

    /**
     * Get unique genus values
     * Equivalent to Python Flask /taxa/genera endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get genera", description = "Get unique genus values from Chattian table")
    @GetMapping("/taxa/genera")
    public R getGenera() {
        return paleoecologyDataService.getGenera();
    }

    /**
     * Get unique species values
     * Equivalent to Python Flask /taxa/species endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get species", description = "Get unique species values from Chattian table")
    @GetMapping("/taxa/species")
    public R getSpecies() {
        return paleoecologyDataService.getSpecies();
    }

    /**
     * Get unique scientific name values
     * Equivalent to Python Flask /taxa/scientific-names endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get scientific names", description = "Get unique scientific name values from Chattian table")
    @GetMapping("/taxa/scientific-names")
    public R getScientificNames() {
        return paleoecologyDataService.getScientificNames();
    }

    /**
     * Get unique original name values
     * Equivalent to Python Flask /taxa/original-names endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get original names", description = "Get unique original name values from Chattian table")
    @GetMapping("/taxa/original-names")
    public R getOriginalNames() {
        return paleoecologyDataService.getOriginalNames();
    }

    /**
     * Get unique fossil type values
     * Equivalent to Python Flask /fossil-types endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get fossil types", description = "Get unique fossil type values from Rupelian table")
    @GetMapping("/fossil-types")
    public R getFossilTypes() {
        return paleoecologyDataService.getFossilTypes();
    }

    /**
     * Get unique plant organ values
     * Equivalent to Python Flask /plant-organs endpoint
     */
    @Inner(value = false)
    @Operation(summary = "Get plant organs", description = "Get unique plant organ values from Chattian table")
    @GetMapping("/plant-organs")
    public R getPlantOrgans() {
        return paleoecologyDataService.getPlantOrgans();
    }
}
