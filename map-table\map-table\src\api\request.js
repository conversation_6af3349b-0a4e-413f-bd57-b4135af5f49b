import axios from "axios";
import store from "@/store";
import { Message } from 'element-ui'
import router from '@/router'
import errorCode from '@/util/errorCode'

export function request(config) {
    // 1.创建axios的实例
    const instance = axios.create({
        timeout: 2500000
    })
    instance.interceptors.request.use(config => {
        const isToken = (config.headers || {}).isToken === false
        const token = store.state.access_token
        if (token && !isToken) {
            config.headers['Authorization'] = 'Bearer ' + token// token
        }
        return config
    }, err => {
        return err
    })

    // 2.2.响应拦截
    instance.interceptors.response.use(res => {
        const status = Number(res.status) || 200
        const message = res.data.msg || errorCode[status] || errorCode['default']
        // 后台定义 401针对的特殊响应码
        if (status === 401) {
            Message({
                message: message,
                type: 'error'
            })
            return Promise.reject(new Error(message))
        }
        if (status !== 200 || res.data.code === 1) {
            Message({
                message: message,
                type: 'error'
            })
            return Promise.reject(new Error(message))
        }
        return res
    }, err => {
        return err
    })
    return instance(config)
}
