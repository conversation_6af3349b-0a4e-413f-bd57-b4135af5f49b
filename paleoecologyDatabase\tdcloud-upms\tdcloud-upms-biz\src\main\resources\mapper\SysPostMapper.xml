<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.SysPostMapper">

	<resultMap id="sysPostMap" type="com.tdkj.tdcloud.admin.api.entity.SysPost">
		<id property="postId" column="post_id"/>
		<result property="postCode" column="post_code"/>
		<result property="postName" column="post_name"/>
		<result property="postSort" column="post_sort"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="remark" column="remark"/>
	</resultMap>

	<!-- 通过用户ID，查询岗位信息-->
	<select id="listPostsByUserId" resultType="com.tdkj.tdcloud.admin.api.entity.SysPost">
		SELECT p.post_id,
			   p.post_name,
			   p.post_code,
			   p.post_sort,
			   p.del_flag,
			   p.create_time,
			   p.update_time,
			   p.remark
		FROM sys_post p,
			 sys_user_post up
		WHERE p.post_id = up.post_id
		  AND p.del_flag = 0
		  and up.user_id = #{userId}
	</select>

</mapper>
