<template>
    <div style="position: relative;">
        <div id="map" style="height: 900px;"></div>
        <div v-if="showOverlappingPoints" class="overlapping-points-container">
            <div class="overlapping-points-header">
                <span>Overlapping Points ({{ overlappingPoints.length }})</span>
                <button class="close-button" @click="closeOverlappingPoints">&times;</button>
            </div>
            <div class="overlapping-points-list">
                <div v-for="point in overlappingPoints" :key="point.ID" class="overlapping-point-item"
                    @click="navigateToDetail(point.ID)">
                    <strong>{{ point.SiteName || 'Unknown Site' }}</strong>
                    <div>Country: {{ point.Country || 'Unknown' }}</div>
                    <div>Epoch: {{ point.Epoch || 'Unknown' }}</div>
                    <div>Stage: {{ point.Stage || 'Unknown' }}</div>
                    <div>Early Interval: {{ point.EarlyInterval || 'Unknown' }}</div>
                    <div>Late Interval: {{ point.LateInterval || 'Unknown' }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import L from 'leaflet';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet-draw';
import { getPaleoecologyData } from '@/api/paleoecology';

let boundingBoxes = [];
let polygons = [];
let circleCenters = [];
let circleRadii = [];
let storedShapes = [];

export default {
    props: ['searchCriteria'],
    watch: {
        searchCriteria: {
            handler(newCriteria) {
                this.updateMap(newCriteria);

                // Check if the criteria contains shape data to restore
                if (newCriteria && (newCriteria.bboxes || newCriteria.polygons || newCriteria.circle_centers)) {
                    // If shapes are defined in criteria but not yet drawn, restore them
                    const hasDrawnShapes = this.drawnItems && this.drawnItems.getLayers().length > 0;
                    if (!hasDrawnShapes) {
                        console.log('Restoring shapes from search criteria:', newCriteria);
                        this.restoreShapesFromCriteria(newCriteria);
                    }
                }
            },
            deep: true
        }
    },
    data() {
        return {
            map: null,
            drawnItems: null,
            dataPointLayer: null,
            boundingBoxes,
            polygons,
            circleCenters,
            circleRadii,
            debounceTimeout: null,
            // Data for managing overlapping points
            pointsMap: {}, // Stores coordinates and corresponding point data
            markerMap: {}, // Stores markers and their corresponding point data
            clusterMap: {}, // Maps coordinates to point clusters
            showOverlappingPoints: false, // Whether to show the overlapping points list
            overlappingPoints: [], // Currently selected overlapping points
            selectedCoordKey: null, // Currently selected coordinate key
            petalMarkers: [], // Stores petal markers
            hoveredCoordKey: null, // Stores currently hovered coordinate key
            petalDisplayMode: null, // 'hover' or 'fixed' to indicate petal display mode
            centerPointMarker: null, // Store the marker for the center point when expanded
            clusterDistance: 0.0003, // Distance threshold for clustering points (in degrees, ~33m at equator)
        };
    },
    computed: {
        // Function to get color based on point count
        getMarkerColor() {
            return (count) => {
                // Color gradient from orange to red based on point count
                if (count <= 1) return '#ff8c00'; // Default orange for single points

                // For multiple points, create a gradient from orange to deep red
                const maxIntensity = Math.min(count, 20); // Cap at 20 for color calculation
                const ratio = (maxIntensity - 1) / 19; // Scale from 0 to 1

                // RGB interpolation from orange (255,140,0) to deep red (178,34,34)
                const r = Math.round(255 - (77 * ratio));
                const g = Math.round(140 - (106 * ratio));
                const b = Math.round(0 + (34 * ratio));

                return `rgb(${r}, ${g}, ${b})`;
            };
        }
    },
    methods: {
        // Show overlapping points list
        showPointsList(coordKey, points) {
            this.overlappingPoints = points;
            this.showOverlappingPoints = true;
            this.selectedCoordKey = coordKey;
        },

        // Close overlapping points list
        closeOverlappingPoints() {
            this.showOverlappingPoints = false;
            this.overlappingPoints = [];
            this.selectedCoordKey = null;

            // Clear petal markers if in fixed mode
            if (this.petalDisplayMode === 'fixed') {
                this.clearPetalMarkers();
                this.petalDisplayMode = null;
            }
        },

        // Navigate to detail page
        navigateToDetail(id) {
            this.$router.push(`/mapDetail/${id}`);
            this.closeOverlappingPoints();
        },

        // Clear petal markers
        clearPetalMarkers() {
            this.petalMarkers.forEach(marker => {
                if (this.map.hasLayer(marker)) {
                    this.map.removeLayer(marker);
                }
            });
            this.petalMarkers = [];

            // Also remove the center point marker if it exists
            if (this.centerPointMarker && this.map.hasLayer(this.centerPointMarker)) {
                this.map.removeLayer(this.centerPointMarker);
                this.centerPointMarker = null;
            }

            this.hoveredCoordKey = null;
        },

        // Create petal layout and display
        createPetalLayout(coordKey, points, baseMarker, mode = 'hover') {
            // Set the display mode
            this.petalDisplayMode = mode;

            // Clear any previous petal markers
            this.clearPetalMarkers();

            // If only one point, no need to create petal layout
            if (points.length <= 1) return;

            // Set hovered key
            this.hoveredCoordKey = coordKey;

            const baseLatLng = baseMarker.getLatLng();

            // Calculate petal radius based on number of points
            const petalRadius = Math.max(25, 15 + points.length * 2); // Expand radius for more points

            // Create petals for ALL points, distributed evenly around the circle
            for (let i = 0; i < points.length; i++) {
                const point = points[i];

                // Calculate petal angle, evenly distributed around the full circle
                const angle = i * (2 * Math.PI / points.length);

                // Convert pixel offset to geographic coordinate offset
                const centerPoint = this.map.latLngToLayerPoint(baseLatLng);
                const targetPoint = L.point(
                    centerPoint.x + petalRadius * Math.cos(angle),
                    centerPoint.y + petalRadius * Math.sin(angle)
                );
                const targetLatLng = this.map.layerPointToLatLng(targetPoint);

                // Create petal marker
                const petalMarker = L.circleMarker(targetLatLng, {
                    radius: 6,
                    fillColor: '#4169E1', // Use different color for petal points
                    color: '#ffffff',
                    weight: 1.5,
                    opacity: 1,
                    fillOpacity: 0.9
                });

                // Create popup content with consistent fields
                const popupContent = `
                    <b>${point.SiteName || 'Unknown Site'}</b><br>
                    Country: ${point.Country || 'Unknown'}<br>
                    Epoch: ${point.Epoch || 'Unknown'}<br>
                    Stage: ${point.Stage || 'Unknown'}<br>
                    Early Interval: ${point.EarlyInterval || 'Unknown'}<br>
                    Late Interval: ${point.LateInterval || 'Unknown'}
                `;

                petalMarker.bindPopup(popupContent, { closeButton: false });

                // Add event listeners
                petalMarker.on('mouseover', function () {
                    this.openPopup();
                });

                petalMarker.on('mouseout', function () {
                    this.closePopup();
                });

                petalMarker.on('click', () => {
                    this.$router.push(`/mapDetail/${point.ID}`);
                });

                // Add to map and tracking array
                petalMarker.addTo(this.map);
                this.petalMarkers.push(petalMarker);
            }

            // Update base marker popup content
            const basePopupContent = `
                <strong>${points.length} overlapping points</strong><br>
                ${mode === 'hover' ? 'Click to fix this view' : 'Click elsewhere to close'}
            `;
            baseMarker.bindPopup(basePopupContent);
            baseMarker.openPopup();
        },

        // Calculate distance between two points in degrees (approximate)
        calculateDistance(lat1, lng1, lat2, lng2) {
            return Math.sqrt(Math.pow(lat1 - lat2, 2) + Math.pow(lng1 - lng2, 2));
        },

        // Find existing cluster for a point
        findClusterForPoint(lat, lng) {
            const keys = Object.keys(this.pointsMap);
            for (const key of keys) {
                const [clusterLat, clusterLng] = key.split(',').map(parseFloat);
                const distance = this.calculateDistance(lat, lng, clusterLat, clusterLng);

                // If distance is less than threshold, points are considered overlapping
                if (distance <= this.clusterDistance) {
                    return key;
                }
            }
            return null;
        },

        // Update cluster center to be the average of all points in cluster
        updateClusterCenter(clusterKey) {
            const points = this.pointsMap[clusterKey];
            if (!points || points.length === 0) return clusterKey;

            // Calculate average coordinates
            let sumLat = 0, sumLng = 0;
            points.forEach(point => {
                sumLat += parseFloat(point.Latitude);
                sumLng += parseFloat(point.Longitude);
            });

            const avgLat = sumLat / points.length;
            const avgLng = sumLng / points.length;

            // Create new key with average coordinates
            const newKey = `${avgLat.toFixed(6)},${avgLng.toFixed(6)}`;

            // If the key changed, update the mapping
            if (newKey !== clusterKey) {
                this.pointsMap[newKey] = [...points];
                delete this.pointsMap[clusterKey];
                return newKey;
            }

            return clusterKey;
        },

        updateMap(criteria) {
            console.log('UpdateMap called with criteria:', criteria);
            if (this.debounceTimeout) {
                clearTimeout(this.debounceTimeout);
            }
            this.debounceTimeout = setTimeout(() => {
                // Clear petal markers and overlapping points list
                this.clearPetalMarkers();
                this.closeOverlappingPoints();
                this.petalDisplayMode = null;

                // Reset point data mappings
                this.pointsMap = {};
                this.markerMap = {};
                this.clusterMap = {};

                // Clear existing data points before adding new ones
                if (this.dataPointLayer) {
                    this.dataPointLayer.clearLayers();
                }

                console.log('Received search criteria in MapView:', criteria);
                console.log('Criteria object:', JSON.stringify(criteria));
                const plainBoundingBoxes = [...boundingBoxes];
                const plainPolygons = [...polygons];
                const plainCircleCenters = [...circleCenters];
                const plainCircleRadii = [...circleRadii];
                console.log('Bounding boxes:', plainBoundingBoxes);
                console.log('Polygons:', plainPolygons);
                console.log('Circle centers:', plainCircleCenters);
                console.log('Circle radii:', plainCircleRadii);

                // Prepare request parameters
                const requestParams = {
                    ...criteria
                };

                // Add bounding box parameters (if any)
                if (plainBoundingBoxes.length > 0) {
                    requestParams.bboxes = plainBoundingBoxes.join('|');
                    console.log('Sending bounding boxes:', requestParams.bboxes);
                }

                // Add polygon parameters (if any)
                if (plainPolygons.length > 0) {
                    requestParams.polygons = plainPolygons.join('|');
                    console.log('Sending polygons:', requestParams.polygons);
                }

                // Add circle parameters (if any)
                if (plainCircleCenters.length > 0 && plainCircleRadii.length > 0) {
                    requestParams.circle_centers = plainCircleCenters.join('|');
                    requestParams.circle_radii = plainCircleRadii.join('|');
                    console.log('Circle data being sent to backend:');
                    for (let i = 0; i < plainCircleCenters.length; i++) {
                        console.log(`  Circle ${i}: center=${plainCircleCenters[i]}, radius=${plainCircleRadii[i]}m`);
                    }
                }

                console.log('Fetching data with params:', requestParams);

                getPaleoecologyData(requestParams)
                    .then(response => {
                        // Handle the R response wrapper structure
                        let data = [];
                        if (response.data && response.data.data && response.data.data.table1) {
                            // Java Spring Boot R.ok(response) format
                            data = response.data.data.table1;
                        } else if (response.data && response.data.table1) {
                            // Direct response format
                            data = response.data.table1;
                        } else if (Array.isArray(response.data)) {
                            // Direct array format
                            data = response.data;
                        }

                        // Trigger event to pass data to parent component
                        this.$emit('updateTableData', data);

                        // Log map update process
                        console.log('Updating map with criteria:', criteria);
                        console.log(`Found ${data.length} data points to display`);

                        // Step 1: Collect points and cluster nearby points
                        data.forEach(item => {
                            try {
                                const lat = parseFloat(item.Latitude);
                                const lng = parseFloat(item.Longitude);

                                if (isNaN(lat) || isNaN(lng)) {
                                    console.warn(`Invalid coordinates for item ${item.ID}: (${item.Latitude}, ${item.Longitude})`);
                                    return;
                                }

                                // Find if this point belongs to an existing cluster
                                let clusterKey = this.findClusterForPoint(lat, lng);

                                if (clusterKey) {
                                    // Add to existing cluster
                                    this.pointsMap[clusterKey].push(item);

                                    // Update cluster center
                                    clusterKey = this.updateClusterCenter(clusterKey);
                                } else {
                                    // Create new cluster with this point
                                    const coordKey = `${lat.toFixed(6)},${lng.toFixed(6)}`;
                                    this.pointsMap[coordKey] = [item];
                                }
                            } catch (e) {
                                console.error(`Error processing data point ${item.ID}:`, e);
                            }
                        });

                        // Log clustering results
                        console.log(`After clustering: ${Object.keys(this.pointsMap).length} locations from ${data.length} points`);

                        // Step 2: Create markers for each location
                        Object.keys(this.pointsMap).forEach(coordKey => {
                            const points = this.pointsMap[coordKey];
                            const [lat, lng] = coordKey.split(',').map(parseFloat);

                            // Get color based on number of points
                            const pointCount = points.length;
                            const markerColor = this.getMarkerColor(pointCount);

                            // Set different styles based on number of points at this location
                            const isCluster = pointCount > 1;
                            const markerOptions = {
                                radius: isCluster ? Math.min(8 + (pointCount / 3), 12) : 6, // Size increases with point count, up to a limit
                                fillColor: markerColor,
                                color: '#ffffff',
                                weight: 1.5,
                                opacity: 1,
                                fillOpacity: 0.9
                            };

                            // Create marker
                            const marker = L.circleMarker([lat, lng], markerOptions);

                            // Save marker and corresponding point data
                            this.markerMap[coordKey] = marker;
                            this.clusterMap[coordKey] = points;

                            // Bind popup content
                            if (isCluster) {
                                // For clusters, show point count
                                const popupContent = `
                                    <strong>${points.length} overlapping points</strong><br>
                                    Hover to expand, click to fix view
                                `;
                                marker.bindPopup(popupContent);
                            } else {
                                // For single points, show detailed info
                                const point = points[0];
                                const popupContent = `
                                    <b>${point.SiteName || 'Unknown Site'}</b><br>
                                    Country: ${point.Country || 'Unknown'}<br>
                                    Epoch: ${point.Epoch || 'Unknown'}<br>
                                    Stage: ${point.Stage || 'Unknown'}<br>
                                    Early Interval: ${point.EarlyInterval || 'Unknown'}<br>
                                    Late Interval: ${point.LateInterval || 'Unknown'}
                                `;
                                marker.bindPopup(popupContent, { closeButton: false });
                            }

                            // Show popup on hover
                            marker.on('mouseover', () => {
                                marker.openPopup();

                                // For clusters, expand petals on hover
                                if (isCluster && this.petalDisplayMode !== 'fixed') {
                                    this.createPetalLayout(coordKey, points, marker, 'hover');
                                }
                            });

                            marker.on('mouseout', () => {
                                // For single points or if display mode is fixed, keep popup open
                                if (!isCluster || this.petalDisplayMode === 'fixed') {
                                    return;
                                }

                                // Otherwise close popup and clear petals
                                marker.closePopup();

                                // Only clear petals if we're in hover mode and moving away from this coord
                                if (this.hoveredCoordKey === coordKey && this.petalDisplayMode === 'hover') {
                                    setTimeout(() => {
                                        // Add a small delay to prevent flickering when moving between marker and its petals
                                        if (this.hoveredCoordKey === coordKey) {
                                            this.clearPetalMarkers();
                                        }
                                    }, 50);
                                }
                            });

                            // Click events
                            marker.on('click', () => {
                                if (isCluster) {
                                    if (this.petalDisplayMode === 'fixed' && this.hoveredCoordKey === coordKey) {
                                        // If already fixed on this cluster, unfix it
                                        this.clearPetalMarkers();
                                        this.petalDisplayMode = null;
                                        this.closeOverlappingPoints();
                                    } else {
                                        // Fix the petal layout and show point list
                                        this.showPointsList(coordKey, points);
                                        this.createPetalLayout(coordKey, points, marker, 'fixed');
                                    }
                                } else {
                                    // For single points, navigate directly to detail page
                                    const point = points[0];
                                    this.$router.push(`/mapDetail/${point.ID}`);
                                }
                            });

                            // Add to layer
                            this.dataPointLayer.addLayer(marker);
                        });

                        // Handle clicks on the map to clear fixed petals
                        this.map.on('click', (e) => {
                            // Only clear if we're in fixed mode and didn't click on a marker
                            if (this.petalDisplayMode === 'fixed' && e.originalEvent.target._leaflet_id === undefined) {
                                this.clearPetalMarkers();
                                this.petalDisplayMode = null;
                                this.closeOverlappingPoints();
                            }
                        });
                    })
                    .catch(error => {
                        // console.error('Error fetching data:', error);
                        // console.error('Error details:', error.response ? error.response.data : 'No response data');
                    });
            }, 300);
        },
        clearDrawingArea() {
            this.drawnItems.clearLayers();
            storedShapes = [];
            boundingBoxes = [];
            polygons = [];
            circleCenters = [];
            circleRadii = [];
            // 触发搜索更新
            this.updateMap(this.searchCriteria);
        },
        clearDrawnItems() {
            // console.log('Clearing all drawn items from map');

            // Clear the drawn items layer
            if (this.drawnItems) {
                this.drawnItems.clearLayers();
            }

            // Reset all shape arrays
            storedShapes = [];
            boundingBoxes = [];
            polygons = [];
            circleCenters = [];
            circleRadii = [];

            // Emit the updated shape data (empty)
            this.$emit('shapes-updated', {
                boundingBoxes: [],
                polygons: [],
                circleCenters: [],
                circleRadii: []
            });
        },
        handleUpdateMap(criteria) {
            this.updateMap(criteria);
        },
        getDrawnShapes() {
            // Return all the necessary data to recreate the shapes
            return {
                storedShapes: storedShapes.map(layer => {
                    if (layer instanceof L.Rectangle) {
                        return {
                            type: 'rectangle',
                            bounds: layer.getBounds()
                        };
                    } else if (layer instanceof L.Polygon) {
                        return {
                            type: 'polygon',
                            latlngs: layer.getLatLngs()
                        };
                    } else if (layer instanceof L.Circle) {
                        return {
                            type: 'circle',
                            center: layer.getLatLng(),
                            radius: layer.getRadius()
                        };
                    }
                    return null;
                }).filter(shape => shape !== null),
                boundingBoxes: [...boundingBoxes],
                polygons: [...polygons],
                circleCenters: [...circleCenters],
                circleRadii: [...circleRadii]
            };
        },
        restoreDrawnShapes(shapesData) {
            if (!shapesData || !this.map || !this.drawnItems) return;

            // Clear existing shapes
            this.drawnItems.clearLayers();

            // Reset arrays
            storedShapes = [];
            boundingBoxes = shapesData.boundingBoxes || [];
            polygons = shapesData.polygons || [];
            circleCenters = shapesData.circleCenters || [];
            circleRadii = shapesData.circleRadii || [];

            // Recreate each shape
            if (shapesData.storedShapes && shapesData.storedShapes.length > 0) {
                shapesData.storedShapes.forEach(shapeData => {
                    let layer;

                    if (shapeData.type === 'rectangle') {
                        layer = L.rectangle(shapeData.bounds, {
                            color: '#3388ff',
                            weight: 2
                        });
                    } else if (shapeData.type === 'polygon') {
                        layer = L.polygon(shapeData.latlngs, {
                            color: '#3388ff',
                            weight: 2
                        });
                    } else if (shapeData.type === 'circle') {
                        layer = L.circle(shapeData.center, {
                            radius: shapeData.radius,
                            color: '#3388ff',
                            weight: 2
                        });
                    }

                    if (layer) {
                        this.drawnItems.addLayer(layer);
                        storedShapes.push(layer);
                    }
                });
            }
        },
        restoreShapesFromCriteria(criteria) {
            // Clear existing shapes
            if (this.drawnItems) {
                this.drawnItems.clearLayers();
            }

            // Reset arrays
            storedShapes = [];
            boundingBoxes = [];
            polygons = [];
            circleCenters = [];
            circleRadii = [];

            // Restore bounding boxes
            if (criteria.bboxes && typeof criteria.bboxes === 'string') {
                const bboxStrings = criteria.bboxes.split('|');
                bboxStrings.forEach(bboxStr => {
                    if (bboxStr) {
                        const [west, south, east, north] = bboxStr.split(',').map(parseFloat);
                        if (!isNaN(west) && !isNaN(south) && !isNaN(east) && !isNaN(north)) {
                            const bounds = L.latLngBounds(
                                L.latLng(south, west),
                                L.latLng(north, east)
                            );
                            const rectangle = L.rectangle(bounds, {
                                color: '#3388ff',
                                weight: 2
                            });
                            this.drawnItems.addLayer(rectangle);
                            storedShapes.push(rectangle);
                            boundingBoxes.push(bboxStr);
                        }
                    }
                });
            }

            // Restore polygons
            if (criteria.polygons && typeof criteria.polygons === 'string') {
                const polygonStrings = criteria.polygons.split('|');
                polygonStrings.forEach(polygonStr => {
                    if (polygonStr) {
                        const coordPairs = polygonStr.split(',');
                        const latlngs = [];
                        for (let i = 0; i < coordPairs.length; i += 2) {
                            if (i + 1 < coordPairs.length) {
                                const lng = parseFloat(coordPairs[i]);
                                const lat = parseFloat(coordPairs[i + 1]);
                                if (!isNaN(lng) && !isNaN(lat)) {
                                    latlngs.push(L.latLng(lat, lng));
                                }
                            }
                        }
                        if (latlngs.length > 2) {
                            const polygon = L.polygon(latlngs, {
                                color: '#3388ff',
                                weight: 2
                            });
                            this.drawnItems.addLayer(polygon);
                            storedShapes.push(polygon);
                            polygons.push(polygonStr);
                        }
                    }
                });
            }

            // Restore circles
            if (criteria.circle_centers && criteria.circle_radii &&
                typeof criteria.circle_centers === 'string' &&
                typeof criteria.circle_radii === 'string') {
                const centerStrings = criteria.circle_centers.split('|');
                const radiusStrings = criteria.circle_radii.split('|');

                for (let i = 0; i < centerStrings.length; i++) {
                    if (i < radiusStrings.length && centerStrings[i] && radiusStrings[i]) {
                        const [lng, lat] = centerStrings[i].split(',').map(parseFloat);
                        const radius = parseFloat(radiusStrings[i]);

                        if (!isNaN(lng) && !isNaN(lat) && !isNaN(radius)) {
                            const circle = L.circle(L.latLng(lat, lng), {
                                radius: radius,
                                color: '#3388ff',
                                weight: 2
                            });
                            this.drawnItems.addLayer(circle);
                            storedShapes.push(circle);
                            circleCenters.push(centerStrings[i]);
                            circleRadii.push(radius);
                        }
                    }
                }
            }

            // Emit the updated shape data
            this.$emit('shapes-updated', {
                boundingBoxes,
                polygons,
                circleCenters,
                circleRadii
            });
        }
    },
    mounted() {
        this.map = L.map('map', {
            zoomControl: false,
            minZoom: 2,
            maxZoom: 6,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView([0, 0], 3);

        const baseLayers = {
            "Map": L.tileLayer('http://t{s}.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=3f64ed522ab5440a6a52cc13715da2f3', {
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                maxZoom: 18,
            }),
            "Satellite": L.tileLayer('http://t{s}.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=3f64ed522ab5440a6a52cc13715da2f3', {
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                maxZoom: 18,
            }),
            "Terrain": L.tileLayer('http://t{s}.tianditu.gov.cn/DataServer?T=ter_w&x={x}&y={y}&l={z}&tk=3f64ed522ab5440a6a52cc13715da2f3', {
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                maxZoom: 18,
            })
        };

        // Set Terrain as the default layer
        baseLayers["Terrain"].addTo(this.map);

        const overlayMaps = {
            "Place Names": L.tileLayer('http://t{s}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=3f64ed522ab5440a6a52cc13715da2f3', {
                subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
                maxZoom: 18,
            })
        };

        L.control.layers(baseLayers, overlayMaps, { position: 'topright' }).addTo(this.map);

        // Feature Group for drawn shapes
        this.drawnItems = new L.FeatureGroup();
        this.map.addLayer(this.drawnItems);

        // Feature Group for data points
        this.dataPointLayer = new L.FeatureGroup();
        this.map.addLayer(this.dataPointLayer);

        // 保存原始的 L.Draw.Feature.prototype._fireCreatedEvent 方法
        const originalFireCreatedEvent = L.Draw.Feature.prototype._fireCreatedEvent;

        // 重写 _fireCreatedEvent 方法，防止清除已有图形
        L.Draw.Feature.prototype._fireCreatedEvent = function (layer) {
            // 调用原始方法，但不清除已有图形
            originalFireCreatedEvent.call(this, layer);
        };

        // 修复 Leaflet.Draw 的行为，防止在开始绘制新图形时清除已有图形
        if (L.DrawToolbar && L.DrawToolbar.prototype) {
            // 直接修改 DrawHandler 的行为
            if (L.Draw.Polygon && L.Draw.Polygon.prototype) {
                const originalPolygonEnable = L.Draw.Polygon.prototype.enable;
                L.Draw.Polygon.prototype.enable = function () {
                    // 调用原始的 enable 方法
                    originalPolygonEnable.call(this);
                    // 阻止清除现有图形
                    // console.log('Polygon drawing started, preserving existing shapes');
                };
            }

            if (L.Draw.Rectangle && L.Draw.Rectangle.prototype) {
                const originalRectangleEnable = L.Draw.Rectangle.prototype.enable;
                L.Draw.Rectangle.prototype.enable = function () {
                    // 调用原始的 enable 方法
                    originalRectangleEnable.call(this);
                    // 阻止清除现有图形
                    // console.log('Rectangle drawing started, preserving existing shapes');
                };
            }

            if (L.Draw.Circle && L.Draw.Circle.prototype) {
                const originalCircleEnable = L.Draw.Circle.prototype.enable;
                L.Draw.Circle.prototype.enable = function () {
                    // 调用原始的 enable 方法
                    originalCircleEnable.call(this);
                    // 阻止清除现有图形
                    // console.log('Circle drawing started, preserving existing shapes');
                };
            }
        }

        // Initialize draw control
        const drawControl = new L.Control.Draw({
            position: 'topright',
            edit: {
                featureGroup: this.drawnItems,
                remove: true,
                poly: {
                    allowIntersection: false
                }
            },
            draw: {
                polygon: {
                    allowIntersection: false,
                    showArea: true
                },
                polyline: false,
                circle: {
                    shapeOptions: {
                        color: '#3388ff'
                    }
                },
                marker: false,
                rectangle: {
                    shapeOptions: {
                        color: '#3388ff'
                    }
                }
            }
        });
        this.map.addControl(drawControl);

        // 添加 DRAWSTART 事件处理，防止绘制新图形时清除已有图形
        this.map.on(L.Draw.Event.DRAWSTART, (event) => {
            // 保存当前的图层，防止被清除
            // console.log('Draw start event detected, preserving existing shapes');
            // 确保不清除现有图形
            event.preventDefault = true;
        });

        // Handle draw events
        this.map.on(L.Draw.Event.CREATED, (event) => {
            const layer = event.layer;
            this.drawnItems.addLayer(layer);
            storedShapes.push(layer);

            if (layer instanceof L.Rectangle) {
                const bounds = layer.getBounds();
                const bbox = `${bounds.getWest().toFixed(6)},${bounds.getSouth().toFixed(6)},${bounds.getEast().toFixed(6)},${bounds.getNorth().toFixed(6)}`;
                boundingBoxes.push(bbox);
                // console.log(`Rectangle created: ${bbox}`);
            } else if (layer instanceof L.Polygon) {
                const latlngs = layer.getLatLngs()[0];
                // 确保多边形坐标格式正确 - 使用更精确的坐标格式，并确保坐标顺序正确
                // 多边形坐标必须按顺时针或逆时针顺序排列
                const polygonCoords = latlngs.map(latlng => `${latlng.lng.toFixed(6)},${latlng.lat.toFixed(6)}`).join(',');
                polygons.push(polygonCoords);
                // console.log(`Polygon created with ${latlngs.length} points: ${polygonCoords}`);

                // 打印多边形的顶点坐标，用于调试
                // console.log("Polygon vertices:");
                // latlngs.forEach((latlng, index) => {
                //     console.log(`Vertex ${index}: [${latlng.lng.toFixed(6)}, ${latlng.lat.toFixed(6)}]`);
                // });
            } else if (layer instanceof L.Circle) {
                const center = layer.getLatLng();
                const radius = layer.getRadius();
                circleCenters.push(`${center.lng.toFixed(6)},${center.lat.toFixed(6)}`);
                circleRadii.push(radius);
                // console.log(`Circle created: center (${center.lng.toFixed(6)},${center.lat.toFixed(6)}), radius ${radius}m`);
            }

            // Emit the updated shape data
            this.$emit('shapes-updated', {
                boundingBoxes,
                polygons,
                circleCenters,
                circleRadii
            });

            // 触发搜索更新
            this.updateMap(this.searchCriteria);
        });

        // Handle delete events
        this.map.on(L.Draw.Event.DELETED, (event) => {
            const layers = event.layers;

            // Track deleted layers by their Leaflet IDs
            const deletedLayerIds = new Set();
            layers.eachLayer((layer) => {
                if (layer._leaflet_id) {
                    deletedLayerIds.add(layer._leaflet_id);
                }
            });

            // Filter out deleted shapes from storedShapes
            storedShapes = storedShapes.filter(shape => !deletedLayerIds.has(shape._leaflet_id));

            // Reset all shape arrays
            boundingBoxes = [];
            polygons = [];
            circleCenters = [];
            circleRadii = [];

            // Rebuild shape arrays from remaining shapes
            storedShapes.forEach(layer => {
                if (layer instanceof L.Rectangle) {
                    const bounds = layer.getBounds();
                    const bbox = `${bounds.getWest().toFixed(6)},${bounds.getSouth().toFixed(6)},${bounds.getEast().toFixed(6)},${bounds.getNorth().toFixed(6)}`;
                    boundingBoxes.push(bbox);
                } else if (layer instanceof L.Polygon) {
                    const latlngs = layer.getLatLngs()[0];
                    const polygonCoords = latlngs.map(latlng => `${latlng.lng.toFixed(6)},${latlng.lat.toFixed(6)}`).join(',');
                    polygons.push(polygonCoords);
                } else if (layer instanceof L.Circle) {
                    const center = layer.getLatLng();
                    const radius = layer.getRadius();
                    circleCenters.push(`${center.lng.toFixed(6)},${center.lat.toFixed(6)}`);
                    circleRadii.push(radius);
                }
            });

            // Emit the updated shape data
            this.$emit('shapes-updated', {
                boundingBoxes,
                polygons,
                circleCenters,
                circleRadii
            });

            // Update the map with the remaining shapes
            this.updateMap(this.searchCriteria);
        });

        // Handle edit events
        this.map.on(L.Draw.Event.EDITED, (event) => {
            const layers = event.layers;
            // 清除原有的数据
            boundingBoxes = [];
            polygons = [];
            circleCenters = [];
            circleRadii = [];

            // console.log('Shapes edited, recollecting spatial data...');

            // 重新收集所有图形的数据
            this.drawnItems.eachLayer((layer) => {
                if (layer instanceof L.Rectangle) {
                    const bounds = layer.getBounds();
                    const bbox = `${bounds.getWest().toFixed(6)},${bounds.getSouth().toFixed(6)},${bounds.getEast().toFixed(6)},${bounds.getNorth().toFixed(6)}`;
                    boundingBoxes.push(bbox);
                    // console.log(`Rectangle updated: ${bbox}`);
                } else if (layer instanceof L.Polygon) {
                    const latlngs = layer.getLatLngs()[0];
                    // 确保多边形坐标格式正确 - 使用更精确的坐标格式，并确保坐标顺序正确
                    const polygonCoords = latlngs.map(latlng => `${latlng.lng.toFixed(6)},${latlng.lat.toFixed(6)}`).join(',');
                    polygons.push(polygonCoords);
                    // console.log(`Polygon updated with ${latlngs.length} points: ${polygonCoords}`);

                    // 打印多边形的顶点坐标，用于调试
                    // console.log("Polygon vertices after edit:");
                    // latlngs.forEach((latlng, index) => {
                    //     console.log(`Vertex ${index}: [${latlng.lng.toFixed(6)}, ${latlng.lat.toFixed(6)}]`);
                    // });
                } else if (layer instanceof L.Circle) {
                    const center = layer.getLatLng();
                    const radius = layer.getRadius();
                    circleCenters.push(`${center.lng.toFixed(6)},${center.lat.toFixed(6)}`);
                    circleRadii.push(radius);
                    // console.log(`Circle updated: center (${center.lng.toFixed(6)},${center.lat.toFixed(6)}), radius ${radius}m`);
                }
            });

            // Emit the updated shape data
            this.$emit('shapes-updated', {
                boundingBoxes,
                polygons,
                circleCenters,
                circleRadii
            });

            // Update the map with the remaining shapes
            this.updateMap(this.searchCriteria);
        });

        L.control.zoom({
            position: 'topright'
        }).addTo(this.map);

        // Call updateMap with the initial search criteria
        this.updateMap(this.searchCriteria);
    }
}
</script>

<style>
#map {
    width: 100%;
    height: 900px;
    /*margin-top: 4%;*/
}

.leaflet-control-attribution {
    display: none;
}

/* Override Element UI button styles */
.el-button--success {
    background-color: #344952 !important;
    border-color: #344952 !important;
}

.el-button--success:hover,
.el-button--success:focus {
    background-color: #425a64 !important;
    border-color: #425a64 !important;
}

/* Enhanced marker styles - without transform effects that cause jumping */
.leaflet-interactive {
    transition: stroke-width 0.2s ease;
}

/* Stable hover effect without transform */
.leaflet-interactive:hover {
    stroke-width: 2.5px !important;
}

/* Add a subtle shadow to markers */
.leaflet-interactive {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

/* Overlapping points list styles */
.overlapping-points-container {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 320px;
    max-height: 500px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.overlapping-points-header {
    padding: 10px 15px;
    background-color: #344952;
    color: white;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0 5px;
    margin: 0;
}

.overlapping-points-list {
    padding: 10px;
    overflow-y: auto;
    max-height: 450px;
}

.overlapping-point-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 5px;
}

.overlapping-point-item strong {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.overlapping-point-item div {
    font-size: 13px;
    margin-bottom: 3px;
}

.overlapping-point-item:hover {
    background-color: #f5f7fa;
}

.overlapping-point-item:last-child {
    border-bottom: none;
}
</style>
