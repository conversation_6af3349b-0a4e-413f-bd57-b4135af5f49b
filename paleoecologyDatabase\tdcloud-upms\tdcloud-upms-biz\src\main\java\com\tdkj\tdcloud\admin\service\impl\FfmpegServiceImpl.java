package com.tdkj.tdcloud.admin.service.impl;

import com.tdkj.tdcloud.admin.service.FfmpegService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

@Service
@Slf4j
public class FfmpegServiceImpl implements FfmpegService {
	@Override
	public void convertToM3u8(String inputFilePath, String outputFilePath) throws Exception {
		// ffmpeg 命令行参数
		String[] cmd = new String[]{
//				"D:\\java\\ffmpeg-release-essentials\\ffmpeg-4.3.1-2021-01-01-essentials_build\\bin\\ffmpeg",
				"/usr/bin/ffmpeg",
				"-i", inputFilePath,
				"-c:v", "libx264",
				"-c:a", "aac", // 将音频编码器从 aac 更改为 libfdk_aac new问题将音频编码器从libfdk_aac 更改为aac
				"-b:a", "128k", // 设置音频比特率为128 kbps
				"-hls_time", "10",
				"-hls_list_size", "0",
				"-hls_segment_filename", System.getProperty("java.io.tmpdir") + File.separator + "%03d.ts",
				outputFilePath
		};

		// 执行 ffmpeg 命令
		ProcessBuilder pb = new ProcessBuilder(cmd);
		pb.redirectErrorStream(true);
		Process p = pb.start();

		// 打印 ffmpeg 输出
		BufferedReader br = new BufferedReader(new InputStreamReader(p.getInputStream()));
		String line;
		while ((line = br.readLine()) != null) {
			System.out.println(line);
		}

		// 等待 ffmpeg 命令执行结束
		try {
			p.waitFor();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		// 检查转换结果是否正确
		File outputFile = new File(outputFilePath);
		if (!outputFile.exists()) {
			throw new IOException("Failed to convert to m3u8: " + outputFilePath);
		}
	}

}
