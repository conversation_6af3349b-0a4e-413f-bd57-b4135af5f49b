/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XTeam;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队
 *
 * <AUTHOR> code generator
 * @date 2025-01-06 09:47:46
 */
@Mapper
public interface XTeamMapper extends TdcloudBaseMapper<XTeam> {

	/**
	 * 查询团队
	 *
	 * @param id 团队主键
	 * @return 团队
	 */
	public XTeam selectXTeamById(Long id);
	public SysFile getFileByXId(@Param("xId")Integer xId,@Param("xType")String xType);

	/**
	 * 查询团队列表
	 *
	 * @param xTeam 团队
	 * @return 团队集合
	 */
	public List<XTeam> selectXTeamList(XTeam xTeam);

	/**
	 * 新增团队
	 *
	 * @param xTeam 团队
	 * @return 结果
	 */
	public int insertXTeam(XTeam xTeam);

	/**
	 * 修改团队
	 *
	 * @param xTeam 团队
	 * @return 结果
	 */
	public int updateXTeam(XTeam xTeam);

	/**
	 * 删除团队
	 *
	 * @param id 团队主键
	 * @return 结果
	 */
	public int deleteXTeamById(Long id);

	/**
	 * 批量删除团队
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteXTeamByIds(Long[] ids);
}
