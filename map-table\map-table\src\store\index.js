import Vue from 'vue'
import Vuex from 'vuex'
import {getStore, setStore} from "@/util/store";
import webiste from "@/util/website";
import {encryption} from "@/util/index";
import {loginByUsername, getUserInfo, refreshToken, logout} from "@/api/login";
import {Message} from "element-ui";
Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        language: 'en',
        openLoginBox: false,
        openRegisterBox: false,
        openForgotBox: false,
        userInfo: getStore({
            name: 'userInfo'
        }) || {},
        permissions: getStore({
            name: 'permissions'
        }) || [],
        roles: [],
        access_token: getStore({
            name: 'access_token'
        }) || '',
        refresh_token: getStore({
            name: 'refresh_token'
        }) || ''
    },
    mutations: {
        setLocale(state, locale) {
            state.language = locale;
        },
        SET_OPEN_LOGIN_BOX: (state, openLoginBox) => {
            state.openLoginBox = openLoginBox
        },
        SET_OPEN_REGISTER_BOX:(state,openRegisterBox) => {
            state.openRegisterBox = openRegisterBox
        },
        SET_OPEN_FORGOT_BOX:(state, openForgotBox) => {
            state.openForgotBox = openForgotBox
        },
        SET_ACCESS_TOKEN: (state, access_token) => {
            state.access_token = access_token
            setStore({
                name: 'access_token',
                content: state.access_token,
                type: 'session'
            })
        },
        SET_REFRESH_TOKEN: (state, rfToken) => {
            state.refresh_token = rfToken
            setStore({
                name: 'refresh_token',
                content: state.refresh_token,
                type: 'session'
            })
        },
        SET_USER_INFO: (state, userInfo) => {
            state.userInfo = userInfo
            setStore({
                name: 'userInfo',
                content: userInfo,
                type: 'session'
            })
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles
        },
        SET_PERMISSIONS: (state, permissions) => {
            const list = {}
            for (let i = 0; i < permissions.length; i++) {
                list[permissions[i]] = true
            }

            state.permissions = list
            setStore({
                name: 'permissions',
                content: list,
                type: 'session'
            })
        }
    },
    actions: {
        changeLanguage({ commit }, newLanguage) {
            commit('setLocale', newLanguage);
        },
        // 根据用户名登录
        LoginByUsername({commit}, userInfo) {
            let user = {}
            if (webiste.passwordEnc) {
                user = encryption({
                    data: userInfo,
                    key: 'pigxpigxpigxpigx',
                    param: ['password']
                })
            } else {
                user = userInfo
            }
            return new Promise((resolve, reject) => {
                loginByUsername(user.username, user.password,  user.randomStr).then(response => {
                    if(response.status === 401) {
                        // console.log(response.response.data.code,response.response.data.msg)
                        const errorMsg = response.response.data.code === 1 ? response.response.data.msg : '当前操作没有权限';
                        Message.error(errorMsg);
                        reject({
                            code: 401,
                            message: errorMsg,
                            response: response.data
                        });
                    }else if(response.status === 200) {
                        const data = response.data
                        commit('SET_ACCESS_TOKEN', data.access_token)
                        commit('SET_REFRESH_TOKEN', data.refresh_token)
                        commit('CLEAR_LOCK')
                        resolve()
                    }
                })
            })
        },
        // 刷新token
        RefreshToken({commit, state}) {
            return new Promise((resolve, reject) => {
                refreshToken(state.refresh_token).then(response => {
                    const data = response.data
                    commit('SET_ACCESS_TOKEN', data.access_token)
                    commit('SET_REFRESH_TOKEN', data.refresh_token)
                    // commit('CLEAR_LOCK')
                    resolve()
                }).catch(error => {
                    reject(error)
                })
            })
        },
        // 查询用户信息
        GetUserInfo({commit}) {
            return new Promise((resolve, reject) => {
                getUserInfo().then((res) => {
                    const data = res.data.data || {}
                    commit('SET_USER_INFO', data.sysUser)
                    commit('SET_ROLES', data.roles || [])
                    commit('SET_PERMISSIONS', data.permissions || [])
                    resolve(data)
                }).catch(() => {
                    reject()
                })
            })
        },
        // 登出
        LogOut({commit}) {
            return new Promise((resolve, reject) => {
                logout().then(() => {

                    commit('SET_PERMISSIONS', [])
                    commit('SET_USER_INFO', {})
                    commit('SET_ACCESS_TOKEN', '')
                    commit('SET_REFRESH_TOKEN', '')
                    commit('SET_ROLES', [])
                    sessionStorage.clear()
                    resolve()
                }).catch(error => {
                    reject(error)
                })
            })
        },
    },
    modules: {
    }
})
