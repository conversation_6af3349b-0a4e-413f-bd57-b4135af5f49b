/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.File;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 团队
 *
 * <AUTHOR> code generator
 * @date 2025-01-06 09:47:46
 */
@Data
@TableName("x_team")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "团队")
public class XTeam extends Model<XTeam> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Integer id;

    /**
     * 名字
     */
    @Schema(description="名字")
    private String personName;

    /**
     * 职称
     */
    @Schema(description="职称")
    private String academicTitle;

    /**
     * 标题
     */
    @Schema(description="标题")
    private String title;

    /**
     * 邮箱
     */
    @Schema(description="邮箱")
    private String email;

    /**
     * 邮政编码
     */
    @Schema(description="邮政编码")
    private String postalCode;

    /**
     * 通讯地址
     */
    @Schema(description="通讯地址")
    private String mailingAddress;

    /**
     * 简历
     */
    @Schema(description="简历")
    private String resume;

    /**
     * 出版物
     */
    @Schema(description="出版物")
    private String publications;

    /**
     * 语言类型zh中文en英文
     */
    @Schema(description="语言类型zh中文en英文")
    private String languageType;

    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 图片地址
     */
    @Schema(description="图片地址")
    private String url;

    /**
     * 图片名字
     */
    @Schema(description="图片名字")
    private String name;

    /**
     * academician院士researcher研究员
     */
    @Schema(description="academician院士researcher研究员")
    private String type;
    @TableField(exist = false)
    private SysFile sysFile;
    private int sort;



}
