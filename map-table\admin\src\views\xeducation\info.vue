<template>
  <el-dialog width="70%" title="详情" center :close-on-click-modal="false" append-to-body
             :visible.sync="visible">
    <el-descriptions class="margin-top" title="" :column="2"  border>
      <el-descriptions-item label="语言类型">{{ infoContent.languageType === 'en' ? '英文' : '中文' }}</el-descriptions-item>
      <el-descriptions-item label="标题">{{ infoContent.title }}</el-descriptions-item>
      <el-descriptions-item label="内容" :span="2">
        <div v-html="infoContent.content" class="content ql-container ql-editor"></div>
      </el-descriptions-item>
      <el-descriptions-item label="封面图片" :span="2">
        <el-image style="width: 100px;height: 100px;" fit="cover"
          :src="infoContent.sysFile && infoContent.sysFile.url !== undefined ? infoContent.sysFile.url : ''"></el-image>
      </el-descriptions-item>
      <el-descriptions-item  :span="2" v-if="type === 'video'" label="视频">{{ infoContent.video && infoContent.video.name ? infoContent.video.name : '' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ infoContent.createTime }}</el-descriptions-item>

    </el-descriptions>

  </el-dialog>

</template>

<script>
import {getObj} from "@/api/xeducation/xeducation";

export default {
  name: "info",
  data() {
    return {
      visible: false,
      type: '',
      infoContent: {}
    }
  },
  methods: {
    getInfo (id, type) {
      this.visible = true
      this.type = type
      getObj(id).then(res => {
        if(res.data.code === 0) {
          this.infoContent = res.data.data
        }
      })

    }
  }
}
</script>

<style scoped lang="scss">
.content {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
img {
  width: 75%;
  height: auto;
}
}
</style>
