<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tdkj</groupId>
        <artifactId>tdcloud</artifactId>
        <version>4.6.0</version>
    </parent>

    <artifactId>tdcloud-quartz</artifactId>
    <packaging>jar</packaging>

    <description>tdcloud 定时任务</description>

    <dependencies>
        <!--认证中心-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-auth</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-upms-api</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-log</artifactId>
        </dependency>
        <!--多租户-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-data</artifactId>
        </dependency>
        <!--XSS 安全过滤-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-xss</artifactId>
        </dependency>
        <!--swagger -->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-swagger</artifactId>
        </dependency>
        <!-- quartz 模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
    </dependencies>
</project>
