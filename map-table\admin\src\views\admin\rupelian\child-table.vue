<template>
  <el-dialog :title="'子表信息'" width="70%" :close-on-click-modal="false"
             append-to-body :visible.sync="visible" @close="pageIndex = 1">
    <div class="avue-crud">
      <el-table :data="dataList" border v-loading="dataListLoading">
        <el-table-column fixed prop="id" header-align="center" align="center" label="ID"></el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="originalname"
          header-align="center"
          align="center"
          label="OriginalName">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="scientificname1"
          header-align="center"
          align="center"
          label="ScientificName1">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="scientificname2"
          header-align="center"
          align="center"
          label="ScientificName2">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="scientificname3"
          header-align="center"
          align="center"
          label="ScientificName3">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="acceptedrank"
          header-align="center"
          align="center"
          label="AcceptedRank">
        </el-table-column>
        <el-table-column
          prop="phylum" width="140" show-overflow-tooltip
          header-align="center"
          align="center"
          label="Phylum">
        </el-table-column>
        <el-table-column
          prop="classNew"
          header-align="center"
          align="center"
          label="Class">
        </el-table-column>
        <el-table-column
          prop="orderNew"
          header-align="center"
          align="center"
          label="Order">
        </el-table-column>
        <el-table-column
          prop="family" width="140" show-overflow-tooltip
          header-align="center"
          align="center"
          label="Family">
        </el-table-column>
        <el-table-column
          prop="genus"
          header-align="center"
          align="center"
          label="Genus">
        </el-table-column>
        <el-table-column
          prop="species1"
          header-align="center"
          align="center"
          label="Species1">
        </el-table-column>
        <el-table-column
          prop="species2"
          header-align="center"
          align="center"
          label="Species2">
        </el-table-column>
        <el-table-column
          prop="species3"
          header-align="center"
          align="center"
          label="Species3">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="plantorgan1"
          header-align="center"
          align="center"
          label="PlantOrgan1">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="plantorgan2"
          header-align="center"
          align="center"
          label="PlantOrgan2">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="abundvalue"
          header-align="center"
          align="center"
          label="AbundValue">
        </el-table-column>
        <el-table-column
          prop="abundunit"
          header-align="center"
          align="center"
          label="AbundUnit">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="fossiltype"
          header-align="center"
          align="center"
          label="FossilType">
        </el-table-column>
<!--        <el-table-column width="180" show-overflow-tooltip-->
<!--          prop="pollendiagram"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="PollenDiagram">-->
<!--        </el-table-column>-->
<!--        <el-table-column width="180" show-overflow-tooltip-->
<!--          prop="assemblage"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="Assemblage">-->
<!--        </el-table-column>-->
        <el-table-column
          prop="siteno"
          header-align="center"
          align="center"
          label="SiteNo">
        </el-table-column>
        <el-table-column
          prop="sitename" width="180" show-overflow-tooltip
          header-align="center"
          align="center"
          label="SiteName">
        </el-table-column>
        <el-table-column
          prop="extinct"
          header-align="center"
          align="center"
          label="Extinct">
        </el-table-column>
        <el-table-column width="180" show-overflow-tooltip
          prop="timecontext"
          header-align="center"
          align="center"
          label="TimeContext">
        </el-table-column>
<!--        <el-table-column-->
<!--          prop="cid"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="cid">-->
<!--        </el-table-column>-->
        <el-table-column
          header-align="center"
          align="center" width="180" fixed="right"
          label="操作">
          <template slot-scope="scope">
            <el-button  type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.cid)">修改</el-button>
            <el-button  type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.cid)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="avue-crud__pagination">
      <el-pagination background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalPage">
      </el-pagination>
    </div>
    <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getChildTableList(parentId)"></table-form>

  </el-dialog>
</template>

<script>
import {fetchList, delObj} from '@/api/chattian'
import {mapGetters} from 'vuex'
import TableForm from './chattian-form'
export default {
  name: "child-table",
  props: {
    parentId: String
  },
  components: { TableForm },
  data() {
    return {
      visible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      addOrUpdateVisible: false
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },

  methods: {
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    getChildTableList(id) {
      this.visible = true
      this.dataListLoading = true
      fetchList(Object.assign({
        current: this.pageIndex,
        size: this.pageSize,
        id: id
      })).then(response => {
        this.dataList = response.data.data.records
        this.totalPage = response.data.data.total
      })
      this.dataListLoading = false
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getChildTableList(this.parentId)
    },
    handleCurrentChange(val) {
      this.pageIndex = val
      this.getChildTableList(this.parentId)
    },
    // 删除
    deleteHandle(id) {
      this.$confirm('是否确认删除ID为' + id, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delObj(id)
      }).then(data => {
        this.$message.success('删除成功')
        this.getChildTableList(this.parentId)
      })
    },
    //  导出excel
    exportExcel() {
      this.downBlobFile('/admin/chattian/export', this.searchForm, 'chattian.xlsx')
    }
  }
}
</script>

<style scoped>

</style>
