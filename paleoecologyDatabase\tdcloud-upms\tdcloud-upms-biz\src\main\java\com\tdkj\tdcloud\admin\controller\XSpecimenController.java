/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.XSpecimenDTO;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XSpecimen;
import com.tdkj.tdcloud.admin.api.vo.XSpecimenExcelVO;
import com.tdkj.tdcloud.admin.mapper.XSpecimenMapper;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.annotation.RequestExcel;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import com.tdkj.tdcloud.admin.service.XSpecimenService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标本信息表，存储标本的基本信息
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:35:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/xspecimen" )
@Tag(description = "xspecimen" , name = "标本信息表，存储标本的基本信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XSpecimenController {

    private final  XSpecimenService xSpecimenService;

    @Resource
	private XSpecimenMapper xSpecimenMapper;
    /**
     * 分页查询
     * @param page 分页对象
     * @param xSpecimen 标本信息表，存储标本的基本信息
     * @return
     */
	@Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_xspecimen_view')" )
    public R getXSpecimenPage(Page page, XSpecimenDTO xSpecimen) {
        return R.ok(xSpecimenService.getXSpecimenPage(page, xSpecimen));
    }


    /**
     * 通过id查询标本信息表，存储标本的基本信息
     * @param id id
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_xspecimen_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
		XSpecimen xSpecimen = xSpecimenService.getById(id);
		List<SysFile> imageList = xSpecimenMapper.getSpecimenImageList("specimen");
		if (imageList!=null && imageList.size()>0){
			Pattern pattern = Pattern.compile("([^_]+_\\d+X)\\.jpg$");
			for (SysFile sf : imageList){
				if (sf.getOriginal().contains(xSpecimen.getSpecimenNo() + "_" + "up" + "_")){
					Matcher matcher = pattern.matcher(sf.getOriginal());
					if (matcher.find()) {
						String result = matcher.group(1);
						sf.setResult(result);
					}
					xSpecimen.getUpSysFileList().add(sf);
				}
				if (sf.getOriginal().contains(xSpecimen.getSpecimenNo() + "_" + "lw" + "_")){
					Matcher matcher = pattern.matcher(sf.getOriginal());
					if (matcher.find()) {
						String result = matcher.group(1);
						sf.setResult(result);
					}
					xSpecimen.getLwSysFileList().add(sf);
				}
			}

		}
		return R.ok(xSpecimen);
    }

    /**
     * 新增标本信息表，存储标本的基本信息
     * @param xSpecimen 标本信息表，存储标本的基本信息
     * @return R
     */
    @Operation(summary = "新增标本信息表，存储标本的基本信息" , description = "新增标本信息表，存储标本的基本信息" )
    @SysLog("新增标本信息表，存储标本的基本信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_xspecimen_add')" )
    public R save(@RequestBody XSpecimen xSpecimen) {
        return R.ok(xSpecimenService.save(xSpecimen));
    }

    /**
     * 修改标本信息表，存储标本的基本信息
     * @param xSpecimen 标本信息表，存储标本的基本信息
     * @return R
     */
    @Operation(summary = "修改标本信息表，存储标本的基本信息" , description = "修改标本信息表，存储标本的基本信息" )
    @SysLog("修改标本信息表，存储标本的基本信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_xspecimen_edit')" )
    public R updateById(@RequestBody XSpecimen xSpecimen) {
        return R.ok(xSpecimenService.updateById(xSpecimen));
    }

    /**
     * 通过id删除标本信息表，存储标本的基本信息
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id删除标本信息表，存储标本的基本信息" , description = "通过id删除标本信息表，存储标本的基本信息" )
    @SysLog("通过id删除标本信息表，存储标本的基本信息" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xspecimen_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(xSpecimenService.removeById(id));
    }


    /**
     * 导出excel 表格
     * @param xSpecimen 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_xspecimen_export')" )
    public List<XSpecimen> export(XSpecimen xSpecimen) {
        return xSpecimenService.list(Wrappers.query(xSpecimen));
    }

	@PostMapping("/importSpecimen")
	public R importSpecimen(@RequestExcel List<XSpecimenExcelVO> excelVOList, BindingResult bindingResult) throws Exception {
		return xSpecimenService.importSpecimen(excelVOList, bindingResult);
	}

	@GetMapping("/getXSpecimenTree")
	public R getXSpecimenTree(String parentEn) {
		return xSpecimenService.getXSpecimenTree(parentEn);
	}

	@GetMapping("/getSpecimenCount")
	public R getSpecimenCount() {
		return xSpecimenService.getSpecimenCount();
	}


	@GetMapping("/getSpecimenVisits")
	public R getSpecimenVisits() {
		return xSpecimenService.getSpecimenVisits();
	}


}
