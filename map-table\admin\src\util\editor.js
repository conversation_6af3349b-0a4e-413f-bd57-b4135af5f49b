/*富文本编辑图片上传配置*/
import store from '../store/index'
const token = 'Bearer ' + store.state.user.access_token
import Quill from "quill";
import { editorUpload } from "@/api/common";
const uploadConfig = {
  action: '/admin/sys-file/upload',  // 必填参数 图片上传地址
  methods: 'POST',  // 必填参数 图片上传方式
  headers: {
    'Content-Type': "multipart/form-data",
  },  // 可选参数 设置请求头部
  token: token,  // 可选参数 如果需要token验证，假设你的token有存放在sessionStorage
  name: 'file',  // 必填参数 文件的参数名
  size: 1024 * 200,  // 可选参数 图片大小，单位为Kb, 1M = 1024Kb
  accept: 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon'  // 可选 可上传的图片格式
};
// toolbar工具栏的工具选项（默认展示全部）
// 自定义文字大小
let fontSize = ['10px', '12px', '14px', '16px', '18px','20px', '22px', '24px', '36px']
Quill.imports['attributors/style/size'].whitelist = fontSize;
Quill.register(Quill.imports['attributors/style/size']);

let Parchment = Quill.import('parchment');
let IndentAttributor = new Parchment.Attributor.Style('indent', 'text-indent', {
  scope: Parchment.Scope.BLOCK,
  whitelist: ['0em', '1em', '2em']
});
let lineHeightClass = new Parchment.Attributor.Class('lineheight', 'ql-lineheight');
Quill.register(lineHeightClass, true);
Quill.register(IndentAttributor, true);
const toolOptions = [
  ['bold', 'italic', 'underline', 'strike', { 'indent': '2em' }],
  ['blockquote', 'code-block'],
  [{ 'header': 1 }, { 'header': 2 }],
  [{ 'list': 'ordered' }, { 'list': 'bullet' }],
  [{ 'script': 'sub' }, { 'script': 'super' }],
  [{ 'indent': '-1' }, { 'indent': '+1' }],
  [{ 'size': fontSize }],
  [{ 'lineheight': ['1.0', '1.5', '1.75', '1.8', '2.0', '2.5', '2.75', '2.8', '3.0'] }],
  [{ 'indentCustom': ['0em', '1em', '2em'] }],
  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
  [{ 'color': [] }, { 'background': [] }],
  [{ 'font': [] }],
  [{ 'align': [] }],
  ['clean'],
  ['link', 'image', 'video']
];
const handlers = {
  image: function image() {
    var self = this;
    var fileInput = this.container.querySelector('input.ql-image[type=file]');
    if (fileInput === null) {
      fileInput = document.createElement('input');
      fileInput.setAttribute('type', 'file');
      // 设置图片参数名
      if (uploadConfig.name) {
        fileInput.setAttribute('name', uploadConfig.name);
      }
      fileInput.setAttribute('accept', uploadConfig.accept);
      fileInput.classList.add('ql-image');
      // 监听选择文件
      fileInput.addEventListener('change', function () {
        // 创建formData
        var formData = new FormData();
        formData.append(uploadConfig.name, fileInput.files[0]);
        // 如果需要token且存在token
        if (uploadConfig.token) {
          formData.append('token', uploadConfig.token);
        }
        // 图片上传
        var xhr = new XMLHttpRequest();
        // 可设置上传图片的格式
        xhr.open(uploadConfig.methods, uploadConfig.action, true);
        xhr.setRequestHeader('authorization', uploadConfig.token);
        // 上传数据成功，会触发
        xhr.onload = function (e) {
          if (xhr.status === 200) {
            console.log('777',self)
            var res = JSON.parse(xhr.responseText);
            let length = self.quill.getSelection(true).index;
            self.quill.insertEmbed(length, 'image', res.data.url);
            self.quill.setSelection(length + 1);
          }
          fileInput.value = '';
        };
        xhr.upload.onloadstart = function (e) {
          fileInput.value = '';
        };
        xhr.upload.onerror = function (e) {
        };
        xhr.upload.onloadend = function (e) {
        };
        xhr.send(formData);
      });
      this.container.appendChild(fileInput);
    }
    fileInput.click();
  },

  lineheight: function (value) {
    if (value) {
      this.quill.format('lineHeight', value);
    } else {
      // console.log(value);
    }
  },

  indentCustom: function (value) {
    if (value) {
      this.quill.format('indent', value);
    }
  },

};

// 添加粘贴处理
function handleCustomPaste(node, delta, quill) {
  let quillVal = store.state.common.quillVal
  console.log('range-quill',quillVal,quillVal.getSelection())
  if (node.tagName === 'IMG' && node.src.startsWith('data:image')) {
    uploadBase64Image(node.src).then((url) => {
      replaceImageSrcInEditor(url, quillVal); // 将 quill 对象传递给 replaceImageSrcInEditor 函数
    });
    return false; // 禁止默认行为，防止直接插入 Base64 图片
  }
}

function uploadBase64Image(base64) {
  const blob = base64ToBlob(base64);
  const formData = new FormData();
  formData.append('file', blob, 'image.png');
  return new Promise((resolve, reject) => {
    editorUpload(formData).then((response) => {
      console.log('Upload successful', response);
      resolve(response.data.data.url);
    }).catch((error) => {
      reject(error);
    });
  });
}

function base64ToBlob(base64) {
  const parts = base64.split(';base64,');
  const contentType = parts[0].split(':')[1]; // 获取 MIME 类型
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  return new Blob([uInt8Array], { type: contentType });
}

function replaceImageSrcInEditor(url, quill) { // 接收 quill 对象作为参数
  console.log('Before getting selection:', quill.root);
  console.log('range-quill',quill,quill.getSelection())
  const range = quill.getSelection();
  if (range) {
    const index = range.index;
    console.log('range',range)
    const length = range.length;
    quill.deleteText(index, length);
    quill.insertEmbed(index, 'image', url, Quill.sources.USER);
  }
}



export default {
  placeholder: '',
  theme: 'snow',  // 主题
  modules: {
    toolbar: {
      container: toolOptions,  // 工具栏选项
      handlers: handlers,// 事件重写
    },
    // clipboard: {
    //   matchers: [
    //     [Node.ELEMENT_NODE, handleCustomPaste] // 添加粘贴处理
    //   ]
    // }
  },
  uploadConfig: uploadConfig, // 添加uploadConfig到tl对象中
};


