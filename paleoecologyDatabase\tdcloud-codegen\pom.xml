<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tdkj</groupId>
        <artifactId>tdcloud</artifactId>
        <version>4.6.0</version>
    </parent>

    <artifactId>tdcloud-codegen</artifactId>
    <packaging>jar</packaging>

    <description>代码生成模块</description>

    <properties>
        <magic.version>2.0.1</magic.version>
        <screw.version>0.0.2</screw.version>
        <db2.version>********</db2.version>
        <configuration.version>1.10</configuration.version>
    </properties>

    <dependencies>
        <!--数据操作-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-data</artifactId>
        </dependency>
        <!--动态数据源 数据操作-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-datasource</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- postgresql 驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- oracle 驱动 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!-- sqlserver 驱动 -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <!-- db2 驱动-->
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>jcc</artifactId>
            <version>${db2.version}</version>
        </dependency>
        <!--DM-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${configuration.version}</version>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-xss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tdkj</groupId>
            <artifactId>tdcloud-common-log</artifactId>
        </dependency>
        <!--代码生成模板引擎-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>${velocity.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity.tools</groupId>
            <artifactId>velocity-tools-generic</artifactId>
            <version>${velocity.tool.version}</version>
        </dependency>
        <!--生成文档-->
        <dependency>
            <groupId>io.springboot.plugin</groupId>
            <artifactId>screw-spring-boot-starter</artifactId>
            <version>${screw.version}</version>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 低代码 -->
        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>${magic.version}</version>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
