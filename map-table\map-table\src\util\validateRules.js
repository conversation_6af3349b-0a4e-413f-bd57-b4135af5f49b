

export const rule = {
  /**
   *
   */
  /**
   * 校验 请输入中文、英文、数字包括下划线
   * 名称校验
   */
  validatorNameCn(rule, value, callback) {
    let acount = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/
    if (value && (!(acount).test(value))) {
      callback(new Error('请输入中文、英文、数字包括下划线'))
    } else {
      callback()
    }
  },
  /**
   * 校验 请输入中文、英文、数字包括下划线
   * 名称校验
   */
  validatorKey(rule, value, callback) {
    let acount = /^[A-Z_]+$/
    if (value && (!(acount).test(value))) {
      callback(new Error('请输入大写英文、下划线'))
    } else {
      callback()
    }
  },

  /**
   * 校验首尾空白字符的正则表达式
   *
   */
  checkSpace(rule, value, callback) {
    let longrg = /[^\s]+$/;
    if(!longrg.test(value)){
      callback(new Error('请输入非空格信息'));
    } else {
      callback();
    }
  },
  /**
   * 校验邮箱
   *
   */
  validatorEmail(rule, value, callback) {
  const pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (value && !pattern.test(value)) {
    callback(new Error('请输入有效的邮箱地址（如：<EMAIL>）'));
  } else {
    callback();
    }
  },
  /**
   * 校验密码（8-16位，包含字母、数字和特殊字符）
   *
   */
  validatorPassword(rule, value, callback) {
    // 正则解释：
    // ^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,16}$
    // - (?=.*[A-Za-z]) 至少一个字母
    // - (?=.*\d)       至少一个数字
    // - (?=.*[@$!%*#?&]) 至少一个特殊字符
    // - [A-Za-z\d@$!%*#?&]{8,16} 总长度8-16
    const pattern = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,16}$/;

    if (value && !pattern.test(value)) {
      callback(new Error('密码必须为8-16位，包含字母、数字和特殊字符（如：zhangsan@123）'));
    } else {
      callback();
    }
  }

}
