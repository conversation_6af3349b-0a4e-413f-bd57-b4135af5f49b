/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.XInterfaceDTO;
import com.tdkj.tdcloud.admin.api.entity.XInterface;
import com.tdkj.tdcloud.admin.mapper.XInterfaceMapper;
import com.tdkj.tdcloud.admin.service.XInterfaceService;
import org.springframework.stereotype.Service;

/**
 * 接口文档
 *
 * <AUTHOR> code generator
 * @date 2025-04-08 14:04:12
 */
@Service
public class XInterfaceServiceImpl extends ServiceImpl<XInterfaceMapper, XInterface> implements XInterfaceService {

	@Override
	public Page getXInterfacePage(Page page, XInterfaceDTO xTeamDto) {
		//查询条件
		QueryWrapper<XInterface> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}
		wrapper.eq(StringUtils.isNotBlank(xTeamDto.getLanguageType()), "language_type", xTeamDto.getLanguageType());
//		wrapper.like(StringUtils.isNotBlank(kibSpecimenDto.getTitle()), "title", kibSpecimenDto.getTitle());
		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}
}
