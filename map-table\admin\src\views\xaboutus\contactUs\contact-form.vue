<template>
  <el-dialog
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="80px">
    <el-form-item label="语言类型" prop="languageType">
      <el-select v-model="dataForm.languageType" placeholder="语言类型" style="width: 100%;">
        <el-option label="中文" value="zh"></el-option>
        <el-option label="英文" value="en"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="联系电话" prop="phone">
      <el-input v-model="dataForm.phone" placeholder="联系电话"></el-input>
    </el-form-item>
    <el-form-item label="地址" prop="address">
      <el-input v-model="dataForm.address" placeholder="地址"></el-input>
    </el-form-item>
    <el-form-item label="邮政编码" prop="postalCode">
      <el-input v-model="dataForm.postalCode" placeholder="邮政编码"></el-input>
    </el-form-item>
    <el-form-item label="图片地址" prop="sysFile">
      <singleFileUpload v-model="dataForm.sysFile" :file="fileList" @successUpload="successUpload"></singleFileUpload>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/xaboutus/xaboutus'
  import singleFileUpload from "@/components/upload/singleFileUpload";
  export default {
    data() {
      return {
        visible: false,
        canSubmit: false,
        fileList: [],
        dataForm: {
          id: null,
          languageType: '',
          type: 'contactUs',
          content: '',
          phone: '',
          address: '',
          postalCode: '',
          createTime: '',
          name: '',
          url: '',
          sysFile: {}
        },
        dataRule: {
          languageType: [
            {required: true, message: '语言类型不能为空', trigger: 'blur'}
          ],
          phone: [
            {required: true, message: '联系电话不能为空', trigger: 'blur'}
          ],
          address: [
            {required: true, message: '地址不能为空', trigger: 'blur'}
          ],
          postalCode: [
            {required: true, message: '邮政编码不能为空', trigger: 'blur'}
          ],
          sysFile:  [
            {required: true, message: '图片不能为空', trigger: 'change'}
          ],
        }
      }
    },
    components: { singleFileUpload },
    methods: {
      init(id) {
        this.dataForm.id = id || null
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
              this.fileList = [response.data.data.sysFile]
            })
          }else {
            this.dataForm.sysFile = {}
            this.fileList = []
          }
        })
      },
      successUpload(val) {

        this.dataForm.sysFile = val ?  val.data.data : {}
        this.dataForm.url = val ?  val.data.data.url : ''
        this.dataForm.name = val ?  val.data.data.name : ''
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        let pic = !this.dataForm.sysFile || this.dataForm.sysFile === {} || this.dataForm.sysFile.url === undefined
        if(pic) {
          this.$message.error('图片不能为空')
        }
          if (valid && !pic) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
