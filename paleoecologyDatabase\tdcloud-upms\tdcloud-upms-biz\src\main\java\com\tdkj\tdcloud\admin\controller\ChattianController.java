/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.ChattianDTO;
import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.api.vo.ChattianExcelVO;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.annotation.RequestExcel;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import com.tdkj.tdcloud.admin.service.ChattianService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 13:42:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/chattian" )
@Tag(description = "chattian" , name = "管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ChattianController {

    private final  ChattianService chattianService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param chattian 
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_chattian_view')" )
    public R getChattianPage(Page page, ChattianDTO chattian) {
        return R.ok(chattianService.getChattianPage(page, chattian));
    }


    /**
     * 通过id查询
     * @param  id
     * @return R
     */
	@Inner(value = false)
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_chattian_view')" )
    public R getChattianById(@PathVariable Integer id) {
        return R.ok(chattianService.getChattianById(id));
    }

	@Operation(summary = "通过cId查询" , description = "通过cId查询" )
	@GetMapping("/getChattianByCId" )
//    @PreAuthorize("@pms.hasPermission('admin_chattian_view')" )
	public R getChattianByCId(Integer cId) {
		return chattianService.getChattianByCId(cId);
	}

    /**
     * 新增
     * @param chattian 
     * @return R
     */
    @Operation(summary = "新增" , description = "新增" )
    @SysLog("新增" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_chattian_add')" )
    public R save(@RequestBody Chattian chattian) {
        return R.ok(chattianService.save(chattian));
    }

    /**
     * 修改
     * @param chattian 
     * @return R
     */
    @Operation(summary = "修改" , description = "修改" )
    @SysLog("修改" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('admin_chattian_edit')" )
    public R updateById(@RequestBody Chattian chattian) {
        return R.ok(chattianService.updateById(chattian));
    }

    /**
     * 通过id删除
     * @param cid
     * @return R
     */
    @Operation(summary = "通过id删除" , description = "通过id删除" )
    @SysLog("通过id删除" )
    @DeleteMapping("/{cid}" )
    @PreAuthorize("@pms.hasPermission('admin_chattian_del')" )
    public R removeById(@PathVariable Integer cid) {
        return R.ok(chattianService.removeById(cid));
    }


    /**
     * 导出excel 表格
     * @param chattian 查询条件
     * @return excel 文件流
     */
	@Inner(value = false)
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('admin_chattian_export')" )
    public List<Chattian> export(Chattian chattian) {
        return chattianService.list(Wrappers.query(chattian));
    }

	@PostMapping("/importChattian")
	public R importChattian(@RequestExcel List<ChattianExcelVO> excelVOList, BindingResult bindingResult) throws Exception {
		return chattianService.importChattian(excelVOList, bindingResult);
	}
}
