/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XSpecimen;
import com.tdkj.tdcloud.admin.api.vo.XSpecimenExcelVO;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标本信息表，存储标本的基本信息
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:35:18
 */
@Mapper
public interface XSpecimenMapper extends TdcloudBaseMapper<XSpecimen> {

	/**
	 * 查询标本信息，存储标本的基本信息
	 *
	 * @param id 标本信息，存储标本的基本信息主键
	 * @return 标本信息，存储标本的基本信息
	 */
	public XSpecimen selectXSpecimenById(Long id);
	public XSpecimen selectXSpecimenBySpeciesEn(String speciesEn);
	List<SysFile> getSpecimenImageList(@Param("xType")String xType);

	/**
	 * 查询标本信息，存储标本的基本信息列表
	 *
	 * @param xSpecimen 标本信息，存储标本的基本信息
	 * @return 标本信息，存储标本的基本信息集合
	 */
	public List<XSpecimen> selectXSpecimenList(XSpecimen xSpecimen);
	public int selectXSpecimenTotal();
	public int getSpecimenCollectionPlaceTotal();

	/**
	 * 新增标本信息，存储标本的基本信息
	 *
	 * @param xSpecimen 标本信息，存储标本的基本信息
	 * @return 结果
	 */
	public int insertXSpecimen(XSpecimen xSpecimen);

	/**
	 * 修改标本信息，存储标本的基本信息
	 *
	 * @param xSpecimen 标本信息，存储标本的基本信息
	 * @return 结果
	 */
	public int updateXSpecimen(XSpecimen xSpecimen);
	public int insertXSpecimenExcel(XSpecimenExcelVO xSpecimen);
	int batchInsertXSpecimenExcel(List<XSpecimenExcelVO> list);
	public int updateXSpecimenExcel(XSpecimenExcelVO xSpecimen);
	public int updateVisits();
	public int selectVisits();

	/**
	 * 删除标本信息，存储标本的基本信息
	 *
	 * @param id 标本信息，存储标本的基本信息主键
	 * @return 结果
	 */
	public int deleteXSpecimenById(Long id);

	/**
	 * 批量删除标本信息，存储标本的基本信息
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteXSpecimenByIds(Long[] ids);
}
