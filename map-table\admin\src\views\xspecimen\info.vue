<template>
    <el-drawer
      title="" size="70%"
      :visible.sync="visible"
      :direction="'rtl'">
      <div class="detail">
        <el-descriptions class="margin-top" title="详情" :column="3" size="medium"  border>
          <el-descriptions-item label="标本号/Specimen No." label-class-name="my-label" content-class-name="my-content">{{ infoContent.specimenNo}}</el-descriptions-item>
          <el-descriptions-item label="物种中文名" label-class-name="my-label" content-class-name="my-content">{{infoContent.speciesCn}}</el-descriptions-item>
          <el-descriptions-item label="species" label-class-name="my-label" content-class-name="my-content">{{ infoContent.speciesEn }}</el-descriptions-item>
          <el-descriptions-item label="属名" label-class-name="my-label" content-class-name="my-content">{{infoContent.genusCn}}</el-descriptions-item>
          <el-descriptions-item label="Genus" label-class-name="my-label" content-class-name="my-content">{{infoContent.genusEn}}</el-descriptions-item>
          <el-descriptions-item label="科名" label-class-name="my-label" content-class-name="my-content">{{infoContent.familyCn}}</el-descriptions-item>
          <el-descriptions-item label="Family" label-class-name="my-label" content-class-name="my-content">{{infoContent.familyEn}}</el-descriptions-item>
          <el-descriptions-item label="采集地点" label-class-name="my-label" content-class-name="my-content">{{infoContent.collectionPlace}}</el-descriptions-item>
          <el-descriptions-item label="Place of Collection" label-class-name="my-label" content-class-name="my-content" >{{infoContent.placeEn}}</el-descriptions-item>
          <el-descriptions-item label="采集日期/Collect Date" label-class-name="my-label" content-class-name="my-content" >{{infoContent.collectionTime}}</el-descriptions-item>
          <el-descriptions-item label="采集人" label-class-name="my-label" content-class-name="my-content" >{{infoContent.collector}}</el-descriptions-item>
          <el-descriptions-item label="collector" label-class-name="my-label" content-class-name="my-content">{{infoContent.collectorEn}}</el-descriptions-item>
          <el-descriptions-item label="数据库导入批次" label-class-name="my-label" content-class-name="my-content">{{infoContent.importBatch}}</el-descriptions-item>
          <el-descriptions-item label="语言类型" label-class-name="my-label" content-class-name="my-content">{{infoContent.languageType  === 'zh' ? '中文' : '英文'}}</el-descriptions-item>
          <el-descriptions-item label="创建时间" label-class-name="my-label" content-class-name="my-content">{{infoContent.createTime}}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-drawer>
</template>

<script>
import { getObj } from '@/api/xspecimen/xspecimen'
export default {
  name: "info",
  data() {
    return {
      visible: false,
      infoContent: {}
    }
  },
  methods: {
    getDetailInfo(id) {
      this.visible = true
      getObj(id).then(res => {
        if(res.data.code === 0) {
          this.infoContent = res.data.data
        }
      })
    }
  }
}
</script>

<style scoped>
.detail {
  padding: 32px 16px;
}
::v-deep .my-label {
  background-color: #F3F3F3 !important;
  color: #929292 !important;
  border-color: #DCDCDC !important;
  width: 125px !important;
}
::v-deep .my-content {
  color: #525252 !important;
  font-size: 16px !important;
}
::v-deep .el-descriptions-item__label {
//font-weight: 700 !important;

}
</style>
