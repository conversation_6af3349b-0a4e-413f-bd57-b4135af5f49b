
<template>
  <el-upload
    class="upload-demo"
    :action="actionUrl"
    :on-change="handleChange"
    :file-list="fileList"
    :before-remove="removeHandle"
    :headers="headers"
    :auto-upload="true"
    :before-upload="beforeUploadSmallTwoImg"
    :show-file-list="true"
    accept=".mp4,.mpeg,.avi,.wmv,.flv,.mov"
    :http-request="requestHandle">
    <el-button size="small" type="primary" style="font-size: 14px">
      <span>选择视频</span>
    </el-button>
    <div slot="tip" class="el-upload__tip">只能上传视频，且大小不能超过200MB</div>
  </el-upload>
</template>

<script>
import {  videoUpload } from '@/api/common'
import { delObj } from "@/api/admin/sys-file";

export default {
  name: "videoUpload",
  props: {
    videoFile: Array
  },
  data() {
    return {
      actionUrl: '/admin/sys-file/uploadVideo',
      headers: {},
      fileList: [...this.videoFile], // 使用展开运算符创建 file 的副本
    }
  },
  watch: {
    videoFile: {
      handler(newVal) {
        this.fileList = [...newVal]; // 监听 prop 的变化，以更新本地的 fileList
      },
      immediate: true,
    },
  },
  methods: {
    handleChange(file, fileList) {
      this.fileList = fileList; // 更新本地的 fileList
      this.$emit('update:file', fileList); // 通过 emit 更新父组件的 file prop
    },
    beforeUploadSmallTwoImg(file) {
      // console.log('999',file)
      if (file.size > 200 * 1024 * 1024) {
        this.$message.error('视频大小不能超过 200MB');
        return false;
      }
      const acceptedFormats = ['video/mpeg', 'video/x-msvideo', 'video/x-ms-wmv', 'video/3gpp', 'video/x-flv', 'video/mp4', 'video/quicktime', 'video/x-ms-asf'];
      const isAcceptedFormat = acceptedFormats.includes(file.type);
      if (!isAcceptedFormat) {
        this.$message.error("上传视频只能是 MPEG/AVI/WMV/3GP/FLV/MP4/MOV/ASF 格式!");
        return false;
      }
    },
    removeHandle(file,fileList) {
      if (file.id) {
        delObj(file.id).then(res => {
          this.$message.success('移除成功');
          this.$emit('successVideoUpload', null);
        });
      }else {
        // 从本地 fileList 中移除文件
        this.fileList = this.fileList.filter(item => item.uid !== file.uid);
        this.$emit('successVideoUpload', this.fileList); // 更新 parent component
      }

    },
    async requestHandle(upload) {
      let loading = this.$loading({
        lock:true,
        text:'视频上传中，请稍等',
        background:'rgba(0,0,0,0.4)'
      })
      let formData = new FormData();
      formData.append('file', upload.file);
      let res = await videoUpload(formData);
      if (res.data.code === 0) {
        this.$message.success('上传成功');
        this.$emit('successVideoUpload', res.data.data);
        loading.close()
      }
    },
  }
}
</script>

<style scoped>

</style>
