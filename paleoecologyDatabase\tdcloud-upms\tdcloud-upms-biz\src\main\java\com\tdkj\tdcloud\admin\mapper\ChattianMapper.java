/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.mapper;

import com.tdkj.tdcloud.admin.api.entity.Chattian;
import com.tdkj.tdcloud.admin.api.vo.ChattianExcelVO;
import com.tdkj.tdcloud.common.data.datascope.TdcloudBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 13:42:23
 */
@Mapper
public interface ChattianMapper extends TdcloudBaseMapper<Chattian> {

	/**
	 * 查询【请填写功能名称】
	 *
	 * @param Cid 【请填写功能名称】主键
	 * @return 【请填写功能名称】
	 */
	public Chattian selectChattianByCid(Long Cid);
	public Chattian selectChattianById(Integer id);

	/**
	 * 查询【请填写功能名称】列表
	 *
	 * @param chattian 【请填写功能名称】
	 * @return 【请填写功能名称】集合
	 */
	public List<Chattian> selectChattianList(Chattian chattian);

	/**
	 * 新增【请填写功能名称】
	 *
	 * @param chattian 【请填写功能名称】
	 * @return 结果
	 */
	public int insertChattian(Chattian chattian);

	/**
	 * 修改【请填写功能名称】
	 *
	 * @param chattian 【请填写功能名称】
	 * @return 结果
	 */
	public int updateChattian(Chattian chattian);

	/**
	 * 删除【请填写功能名称】
	 *
	 * @param Cid 【请填写功能名称】主键
	 * @return 结果
	 */
	public int deleteChattianByCid(Long Cid);

	/**
	 * 批量删除【请填写功能名称】
	 *
	 * @param Cids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteChattianByCids(Long[] Cids);
	public int insertChattianExcel(ChattianExcelVO chattian);


}
