<template>
  <div class="team">
    <section class="team-main">

      <template>
        <template v-if="type === 'introduction'">
          <div v-if="language === 'en'" class="introduction-box">
            <p class="introduction-box-item">The Paleoecology Research Group was established to study the response and adaptation mechanisms of plant diversity to environmental change at global change and geological and historical scales. Relying on XTBG's dominant disciplines (ecology and botany), the research group will use plant fossils and modern plants as research materials, combine macro and micro, multidisciplinary, and use models and big data to explore the evolution history and law of plant distribution patterns at different time scales, as well as the response and adaptation mechanism of biodiversity to paleoenvironmental changes (paleoclimate, paleovegetation, paleolandform, paleoaltitude, paleoatmospheric carbon dioxide). The paleoecology group mainly focuses on the Cenozoic plants widely distributed in the Yunnan-Guizhou Plateau and its adjacent areas (such as the Qinghai-Tibet Plateau and Southeast Asian countries), and carries out the following studies:
            </p>
            <p class="introduction-box-item">1) The study of the fossil history of paleoflora and specific plant groups. More than 50,000 beautifully preserved fossil materials from different geological eras of the Cenozoic in southwest China and Southeast Asia have been collected, covering leaves, seeds, fruits, flowers and wood, including Pinaceae, Cypressaceae, Fagaceae, Lauraceae, Betulaceae, Dipterocarpaceae, Fabaceae and other important taxa in the current ecosystem.</p>
            <p class="introduction-box-item">2) Quantitatively reconstruct the paleoenvironment (paleoclimate, paleovegetation, paleolandform, paleoaltitude) using the abundant Cenozoic fossil materials from southwest China and Southeast Asia.</p>
            <p class="introduction-box-item">3) Carry out the research on model simulation of key geological periods, key taxa and important geological events. Combining big data analysis, numerical simulation (HadCM3, CESM), and vegetation and plant diversity simulation, this paper comprehensively explores how changes in the earth's environment drive the evolution of ecosystems, vegetation and plant diversity.</p>
            <p class="introduction-box-item">4) On the basis of the study of paleoflora and specific plant groups, explore the evolution of biodiversity in geological time and the response mechanism to environmental changes.</p>
          </div>
          <div v-else class="introduction-box">
            <p class="introduction-box-item">古生态研究组为研究全球变化及地质和历史尺度上植物多样性对环境变化响应及其适应机制而设立。研究组将依托版纳植物园的优势学科（生态学和植物学），以植物化石和现代植物为研究材料，宏观与微观相结合，多学科交叉，运用模型及大数据，探索不同时间尺度（百万年、万年、千年）植物分布格局的演变历史和规律，以及生物多样性对古环境变化（古气候、古植被、古地貌、古海拔、古大气二氧化碳）的响应与适应机制。古生态组主要以云贵高原及其邻近区域（如青藏高原、东南亚各国）广泛分布的新生代植物为研究主要对象，开展以下方面的研究：</p>
            <p class="introduction-box-item">1）古植物群及特定植物类群的化石历史的研究。在中国西南-东南亚收集了新生代不同地质时代50000余件保存精美的化石材料，涵盖叶、种子、果实、花和木材等多种类型，包括：松科、柏科、壳斗科、樟科、桦木科、龙脑香科、豆科等现今生态系统中的重要类群。</p>
            <p class="introduction-box-item">2）利用中国西南-东南亚新生代丰富的化石材料进行古环境（古气候、古植被、古地貌、古海拔）的定量重建。</p>
            <p class="introduction-box-item">3）开展关键地质时期、关键类群和重要地质事件的模型模拟的研究。综合不同地区的化石数据，建立新生代化石数据库；结合大数据分析、数值模拟（HadCM3，CESM），植被和植物多样性模拟，综合探讨地球环境变化如何驱动生态系统、植被和植物多样性演变。</p>
            <p class="introduction-box-item">4）在古植物群和特定植物类群研究的基础上，探索生物多样性在地质时期的演变以及对环境变化的响应机制。</p>
            <p class="introduction-box-item"></p>
            <p class="introduction-box-item"></p>
          </div>
        </template>
        <template v-else>
          <div v-if="total > 0" class="team-con" style="display:flex;flex-direction: column">
            <ul class="team-con-ul">
              <li class="team-con-item" v-for="team in teamDataList" :key="team.id" @click="clickItem(team)">
                <div class="team-con-item-pic">
                  <img class="pic" :src="team.url" alt="">
                </div>
                <div class="team-con-item-name">{{ team.personName }}</div>
              </li>
            </ul>
            <el-pagination style="width: 100%;text-align: right" v-if="total >0"
                           background  @size-change="handleSizeChange"
                           @current-change="handleCurrentChange"
                           :current-page.sync="pageIndex"
                           :page-size="pageSize"
                           layout="prev, pager, next"
                           :total="total">
            </el-pagination>
          </div>
          <el-empty style="margin: 0 auto" v-else description="暂无数据"></el-empty>
        </template>
      </template>
      <div class="team-nav">
        <div class="team-nav-title">{{ $t('team.teamText') }}</div>
        <ul class="team-ul">
          <li :class="[currentIndex === 0 ? 'active' : '']" @click="toggelTeam(0,'introduction')">{{ $t('team.introductionText') }}</li>
          <li :class="[currentIndex === 1 ? 'active' : '']" @click="toggelTeam(1,'academician')">{{ $t('team.academicianText') }}</li>
          <li :class="[currentIndex === 2 ? 'active' : '']" @click="toggelTeam(2,'researcher')">{{ $t('team.researcherText') }}</li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script>
import {getTeamList} from "@/api/team";
import {mapState} from "vuex";
export default {
  name: "TeamIndex",
  data() {
    return {
      currentIndex: 0,
      type: 'introduction',
      teamDataList: [],
      pageIndex: 1,
      pageSize: 8,
      total: 0
    }
  },
  computed: {
    ...mapState({
      language: state => state.language // 将language从store映射到计算属性
    })
  },
  watch: {
    language() {
      this.getTeamList(); // 语言变化时重新获取内容
    }
  },
  mounted() {
    this.getTeamList()
  },
  methods: {
    toggelTeam(val,type) {
      this.currentIndex = val
      this.type = type
      this.pageIndex = 1
      this.getTeamList()
    },
    clickItem(item) {
      this.$router.push(`/teamDetail/${item.id}`)
    },
    getTeamList() {
      getTeamList(Object.assign({
        current: this.pageIndex,
        size: this.pageSize,
        type: this.type,
        languageType: this.language // 使用 Vuex 中的 language
      })).then(res => {
        if(res.data.code === 0) {
          this.teamDataList = res.data.data.records || []
          this.total = res.data.data.total || 0
        }
      })
    },
    // 每页数
    handleSizeChange(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getTeamList()
    },
    // 当前页
    handleCurrentChange(val) {
      this.pageIndex = val
      this.getTeamList()
    },
  }
}
</script>

<style scoped>
.team {
  width: 100%;
  min-height: calc(100vh - 372px);
  position: relative;
  background-color: var(--about-background-color);
}

.team-main {
  display: flex;
  padding: 50px 80px 50px;
  position: relative;
}
.introduction-box {
  width: calc(100% - 320px - 43px );
  .introduction-box-item{
    text-align: left;
    line-height: 1.6;
  }
}
.team-con {
  text-align: left;
  width: calc(100% - 320px - 43px );
}
.team-con-ul{
  width: 100%;
}
.team-nav {
  width: 320px;
  margin-left: 43px;
  position: fixed;
  right: 80px;
}
.team-nav-title {
  height: 60px;
  line-height: 60px;
  padding-left: 10px;
  font-size: 24px;
  color: var(--primary-text-color);
  border-bottom:  1px solid #DBDBDB;
  text-align: left;
}
.team-ul {
  text-align: left;
  padding: 0;
  margin: 0;
}
.team-ul li {
  height: 60px;
  line-height: 60px;
  padding-left: 10px;
  font-size: 18px;
  color: #2C4A52;
  border-bottom:  1px solid #DBDBDB;
  cursor: pointer;
}
.team-ul .active  {
  background: #FFFFFF;
}
.team-con-item {
  float: left;
  width: calc((100% - 90px) / 4);
  margin-right: 30px;
  margin-bottom: 30px;
  cursor: pointer;
  /*aspect-ratio: 4 / 3;*/
}
.team-con-item:nth-child(4n) {
  margin-right: 0;
}
.team-con-item:nth-child(4n + 1) {
  clear: both;
}
.team-con-item-pic {
  width: 100%;
  height: 444px;
}
.team-con-item-pic .pic{
  width: 100%;
  height: 444px;
  object-position: center;
  object-fit: cover;
  display: block;
}
.team-con-item-name {
  height: 46px;
  line-height: 69px;
  text-align: center;
  font-size: 18px;
}
</style>