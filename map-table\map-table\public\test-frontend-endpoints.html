<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Endpoint Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #results { margin-top: 20px; max-height: 600px; overflow-y: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .progress { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Paleoecology API Frontend Testing</h1>
    <p>This page tests the API endpoints through the Vue.js development proxy.</p>
    
    <div class="test-section">
        <h3>Quick Tests</h3>
        <button onclick="testAllEndpoints()" id="testAllBtn">Test All Endpoints</button>
        <button onclick="testMainDataEndpoint()">Test Main Data</button>
        <button onclick="testDetailEndpoint()">Test Detail Data</button>
        <button onclick="testGeographicFiltering()">Test Geographic Filtering</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div class="test-section">
        <h3>Individual Lookup Tests</h3>
        <button onclick="testLookupEndpoints()">Test All Lookups</button>
        <button onclick="testEndpoint(endpoints[0])">Dating Methods</button>
        <button onclick="testEndpoint(endpoints[2])">Families</button>
        <button onclick="testEndpoint(endpoints[3])">Genera</button>
        <button onclick="testEndpoint(endpoints[4])">Species</button>
    </div>
    
    <div id="results"></div>

    <script>
        // Base URL for frontend proxy (Vue.js dev server)
        const BASE_URL = '/admin/paleoecology';
        
        // Test endpoints
        const endpoints = [
            { name: 'Dating Methods', url: `${BASE_URL}/dating-methods` },
            { name: 'Dating Qualities', url: `${BASE_URL}/dating-qualities` },
            { name: 'Families', url: `${BASE_URL}/taxa/families` },
            { name: 'Genera', url: `${BASE_URL}/taxa/genera` },
            { name: 'Species', url: `${BASE_URL}/taxa/species` },
            { name: 'Scientific Names', url: `${BASE_URL}/taxa/scientific-names` },
            { name: 'Original Names', url: `${BASE_URL}/taxa/original-names` },
            { name: 'Fossil Types', url: `${BASE_URL}/fossil-types` },
            { name: 'Plant Organs', url: `${BASE_URL}/plant-organs` }
        ];

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function setButtonState(disabled) {
            document.getElementById('testAllBtn').disabled = disabled;
        }

        async function testEndpoint(endpoint) {
            try {
                addResult(`Testing ${endpoint.name}...`, 'info');
                
                const response = await fetch(endpoint.url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ ${endpoint.name}: SUCCESS - Status ${response.status}`, 'success');
                    
                    // Handle different response formats
                    let dataArray = null;
                    if (data && data.data && Array.isArray(data.data)) {
                        dataArray = data.data;
                    } else if (Array.isArray(data)) {
                        dataArray = data;
                    }
                    
                    if (dataArray) {
                        addResult(`   📊 Data length: ${dataArray.length}`, 'info');
                        if (dataArray.length > 0) {
                            addResult(`   📝 Sample data: ${dataArray.slice(0, 3).join(', ')}`, 'info');
                        } else {
                            addResult(`   ⚠️ No data returned`, 'warning');
                        }
                    } else {
                        addResult(`   📋 Response: ${JSON.stringify(data).substring(0, 100)}...`, 'info');
                    }
                } else {
                    addResult(`❌ ${endpoint.name}: FAILED - Status ${response.status}`, 'error');
                    const errorText = await response.text();
                    addResult(`   Error: ${errorText.substring(0, 200)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ ${endpoint.name}: ERROR - ${error.message}`, 'error');
            }
        }

        async function testMainDataEndpoint() {
            try {
                addResult('🔍 Testing Main Data Endpoint...', 'info');
                
                const params = new URLSearchParams({
                    family: 'Fagaceae',
                    southLatitude: '-90',
                    northLatitude: '90',
                    westLongitude: '-180',
                    eastLongitude: '180'
                });
                
                const response = await fetch(`${BASE_URL}/data?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Main Data Endpoint: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && data.data.table1) {
                        addResult(`   📊 Results count: ${data.data.table1.length}`, 'info');
                        if (data.data.table1.length > 0) {
                            const sample = data.data.table1[0];
                            addResult(`   📝 Sample fields: ${Object.keys(sample).slice(0, 5).join(', ')}`, 'info');
                            addResult(`   🌍 Sample coordinates: ${sample.Longitude}, ${sample.Latitude}`, 'info');
                        }
                    } else {
                        addResult(`   ⚠️ Unexpected response format: ${JSON.stringify(data).substring(0, 100)}`, 'warning');
                    }
                } else {
                    addResult(`❌ Main Data Endpoint: FAILED - Status ${response.status}`, 'error');
                    const errorText = await response.text();
                    addResult(`   Error: ${errorText.substring(0, 200)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Main Data Endpoint: ERROR - ${error.message}`, 'error');
            }
        }

        async function testDetailEndpoint() {
            try {
                addResult('🔍 Testing Detail Endpoint...', 'info');
                
                const response = await fetch(`${BASE_URL}/detail?id=1`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Detail Endpoint: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && data.data.detail) {
                        addResult(`   📊 Detail records: ${data.data.detail.length}`, 'info');
                        if (data.data.detail.length > 0) {
                            const sample = data.data.detail[0];
                            addResult(`   📝 Sample detail fields: ${Object.keys(sample).slice(0, 5).join(', ')}`, 'info');
                        }
                    } else {
                        addResult(`   ⚠️ Unexpected response format: ${JSON.stringify(data).substring(0, 100)}`, 'warning');
                    }
                } else {
                    addResult(`❌ Detail Endpoint: FAILED - Status ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Detail Endpoint: ERROR - ${error.message}`, 'error');
            }
        }

        async function testGeographicFiltering() {
            try {
                addResult('🌍 Testing Geographic Filtering...', 'info');
                
                // Test with polygon coordinates
                const params = new URLSearchParams({
                    polygons: '100,30,110,30,110,40,100,40,100,30',
                    family: 'Fagaceae'
                });
                
                const response = await fetch(`${BASE_URL}/data?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Geographic Filtering: SUCCESS - Status ${response.status}`, 'success');
                    
                    if (data && data.data && data.data.table1) {
                        addResult(`   📊 Filtered results: ${data.data.table1.length}`, 'info');
                        addResult(`   🗺️ Polygon filter applied successfully`, 'success');
                    }
                } else {
                    addResult(`❌ Geographic Filtering: FAILED - Status ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Geographic Filtering: ERROR - ${error.message}`, 'error');
            }
        }

        async function testLookupEndpoints() {
            addResult('📋 Testing Lookup Endpoints:', 'info');
            addResult('-'.repeat(30), 'info');
            for (const endpoint of endpoints) {
                await testEndpoint(endpoint);
            }
        }

        async function testAllEndpoints() {
            setButtonState(true);
            clearResults();
            
            addResult('='.repeat(60), 'info');
            addResult('🧪 PALEOECOLOGY API FRONTEND TESTING', 'info');
            addResult('='.repeat(60), 'info');
            
            // Test lookup endpoints
            await testLookupEndpoints();
            
            // Test main data endpoint
            addResult('🔍 Testing Main Data Endpoint:', 'info');
            addResult('-'.repeat(30), 'info');
            await testMainDataEndpoint();
            
            // Test detail endpoint
            addResult('🔍 Testing Detail Endpoint:', 'info');
            addResult('-'.repeat(30), 'info');
            await testDetailEndpoint();
            
            // Test geographic filtering
            addResult('🌍 Testing Geographic Filtering:', 'info');
            addResult('-'.repeat(30), 'info');
            await testGeographicFiltering();
            
            addResult('='.repeat(60), 'info');
            addResult('✅ TESTING COMPLETE', 'success');
            addResult('='.repeat(60), 'info');
            
            setButtonState(false);
        }

        // Add initial instructions
        window.onload = function() {
            addResult('📋 Instructions:', 'info');
            addResult('1. Make sure the Vue.js development server is running on port 8181', 'info');
            addResult('2. Make sure the Java Spring Boot backend is running on port 8888', 'info');
            addResult('3. Access this page at: http://localhost:8181/test-frontend-endpoints.html', 'info');
            addResult('4. Click "Test All Endpoints" to run comprehensive tests', 'info');
            addResult('-'.repeat(60), 'info');
        };
    </script>
</body>
</html>
