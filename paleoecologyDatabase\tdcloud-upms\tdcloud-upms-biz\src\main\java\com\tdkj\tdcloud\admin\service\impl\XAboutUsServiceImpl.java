/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */
package com.tdkj.tdcloud.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tdkj.tdcloud.admin.api.dto.XAboutUsDto;
import com.tdkj.tdcloud.admin.api.entity.SysFile;
import com.tdkj.tdcloud.admin.api.entity.XAboutUs;
import com.tdkj.tdcloud.admin.mapper.XAboutUsMapper;
import com.tdkj.tdcloud.admin.mapper.XTeamMapper;
import com.tdkj.tdcloud.admin.service.SysFileService;
import com.tdkj.tdcloud.admin.service.XAboutUsService;
import com.tdkj.tdcloud.common.core.util.R;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 关于我们
 *
 * <AUTHOR> code generator
 * @date 2025-01-03 14:40:32
 */
@Service
public class XAboutUsServiceImpl extends ServiceImpl<XAboutUsMapper, XAboutUs> implements XAboutUsService {

	@Resource
	private XAboutUsMapper xAboutUsMapper;
	@Resource
	private XTeamMapper xTeamMapper;
	@Resource
	private SysFileService sysFileService;

	@Override
	public Page getXAboutUsPage(Page page, XAboutUsDto xAboutUsDto) {
		//查询条件
		QueryWrapper<XAboutUs> wrapper = new QueryWrapper<>();


		//日期戳
//		if (ArrayUtil.isNotEmpty(kizTransverseProjectPriceDto.getInPlaceTime())) {
//			wrapper.ge(KizTransverseProjectPrice::getInPlaceTime, kizTransverseProjectPriceDto.getInPlaceTime()[0]).le(KizTransverseProjectPrice::getInPlaceTime,
//					kizTransverseProjectPriceDto.getInPlaceTime()[1]);
//		}
		wrapper.eq(StringUtils.isNotBlank(xAboutUsDto.getType()), "type", xAboutUsDto.getType());
		wrapper.eq(StringUtils.isNotBlank(xAboutUsDto.getLanguageType()), "language_type", xAboutUsDto.getLanguageType());
//		wrapper.like(StringUtils.isNotBlank(kibSpecimenDto.getTitle()), "title", kibSpecimenDto.getTitle());
		wrapper.orderByDesc("create_time");

		Page page1 = baseMapper.selectPage(page, wrapper);


		return page1;
	}

	@Override
	public R saveXAboutUs(XAboutUs xAboutUs) {
		xAboutUs.setCreateTime(LocalDateTime.now());
		int i = xAboutUsMapper.insertXAboutUs(xAboutUs);
		if (i==1){
			if (xAboutUs.getSysFile()!=null){
				xAboutUs.getSysFile().setXId(xAboutUs.getId());
				xAboutUs.getSysFile().setXType("aboutUs");
				sysFileService.save(xAboutUs.getSysFile());
			}
			return R.ok(i,"添加成功");
		}
		return null;
	}

	@Override
	public R updateXAboutUsById(XAboutUs xAboutUs) {
		int i = xAboutUsMapper.updateXAboutUs(xAboutUs);
		if (i==1){
			if (xAboutUs.getSysFile()!=null){
				if (xAboutUs.getSysFile().getId()==null){
					xAboutUs.getSysFile().setXId(xAboutUs.getId());
					xAboutUs.getSysFile().setXType("aboutUs");
					sysFileService.save(xAboutUs.getSysFile());
				}else {
					sysFileService.updateById(xAboutUs.getSysFile());
				}

			}
			return R.ok(i,"修改成功");
		}
		return null;
	}

	@Override
	public R getXAboutUsById(Integer id) {
		XAboutUs xAboutUs = xAboutUsMapper.selectXAboutUsById(Long.valueOf(id));
		SysFile sysFile = xTeamMapper.getFileByXId(id,"aboutUs");
		if (sysFile!=null){
			xAboutUs.setSysFile(sysFile);
		}
		return R.ok(xAboutUs,"详情");
	}
}
