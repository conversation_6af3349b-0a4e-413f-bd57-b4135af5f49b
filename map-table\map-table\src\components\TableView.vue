<template>
  <div class="table-view">
    <div class="table-header">
      <el-select v-model="localSelectedResult" placeholder="Select saved search" @change="applySelectedSearch">
        <el-option v-for="result in results" :key="result.id" :label="result.name" :value="result.id">
          <span :class="{ 'map-selection': isMapSelection(result) }">{{ result.name }}</span>
        </el-option>
      </el-select>
      <el-button type="success" size="small" @click="downloadData">Download</el-button>
    </div>
    <!--      :max-height="520"-->
    <!--      height="520px"-->

    <el-table :data="paginatedData" :header-cell-style="{ background: '#E9E9E9' }" style="width: 100%" border
      :highlight-current-row="true" :header-row-style="{ position: 'sticky', top: 0 }" @row-click="handleRowClick"
      row-key="ID" :expand-row-keys="expandedRowKeys" @expand-change="handleExpandChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div v-loading="childTableLoading" class="child-table-loading" element-loading-text="Loading...">
            <!-- Debug info -->
            <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
              <strong>Debug Info:</strong><br>
              Loading: {{ childTableLoading }}<br>
              Child Data Length: {{ childTableData.length }}<br>
              Current Expanded Row: {{ currentExpandedRow ? currentExpandedRow.ID : 'None' }}<br>
              Props Row ID: {{ props.row.ID }}
            </div>

            <div v-if="!childTableLoading && childTableData.length === 0" class="no-child-data">
              No related data found for ID: {{ props.row.ID }}
            </div>
            <div v-else-if="!childTableLoading" class="child-table-container">
              <div style="background: #e8f4fd; padding: 10px; margin-bottom: 10px; font-size: 12px;">
                <strong>Table Data Info:</strong><br>
                processedChildTableData length: {{ processedChildTableData.length }}<br>
                Raw childTableData length: {{ childTableData.length }}
              </div>
              <el-table :data="processedChildTableData" border size="mini" style="width: 100%"
                :header-cell-style="{ background: '#F5F7FA' }">
                <!-- Test column to verify table is working -->
                <el-table-column label="Test" width="100">
                  <template slot-scope="scope">
                    Row {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <!--                                    <el-table-column prop="cid" label="Cid" width="80"></el-table-column>-->
                <!--                                    <el-table-column prop="id" label="ID" width="80"></el-table-column>-->
                <el-table-column fixed="left" prop="originalname" label="OriginalName"
                  show-overflow-tooltip></el-table-column>
                <el-table-column prop="ScientificName" label="ScientificName" show-overflow-tooltip></el-table-column>
                <el-table-column prop="acceptedrank" label="AcceptedRank"></el-table-column>
                <el-table-column prop="phylum" label="Phylum"></el-table-column>
                <el-table-column prop="classNew" label="Class"></el-table-column>
                <el-table-column prop="orderNew" label="Order"></el-table-column>
                <el-table-column prop="family" label="Family"></el-table-column>
                <el-table-column prop="genus" label="Genus"></el-table-column>
                <el-table-column prop="Species" label="Species" show-overflow-tooltip></el-table-column>
                <el-table-column prop="PlantOrgan" label="PlantOrgan"></el-table-column>
                <el-table-column prop="abundvalue" label="AbundValue"></el-table-column>
                <el-table-column prop="abundunit" label="AbundUnit"></el-table-column>
                <el-table-column prop="fossiltype" label="FossilType"></el-table-column>
                <!--                <el-table-column-->
                <!--                  prop="pollendiagram"-->
                <!--                  label="PollenDiagram"-->
                <!--                ></el-table-column>-->
                <el-table-column prop="pollendiagram" label="Assemblage"></el-table-column>
                <el-table-column prop="siteno" label="SiteNo"></el-table-column>
                <el-table-column prop="sitename" label="SiteName" show-overflow-tooltip></el-table-column>
                <el-table-column prop="extinct" label="Extinct"></el-table-column>
                <el-table-column fixed="right" prop="timecontext" label="TimeContext"></el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="ID" label="ID" width="70px"></el-table-column>
      <el-table-column prop="SiteNo" label="SiteNo" width="100px"></el-table-column>
      <el-table-column prop="SiteName" label="SiteName" width="150px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="Country" label="Country" width="100px"></el-table-column>
      <el-table-column prop="DatingMethod" label="DatingMethod" width="150px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="DatingQuality" label="DatingQuality" width="120px"></el-table-column>
      <el-table-column prop="Epoch" label="Epoch" width="100px"></el-table-column>
      <el-table-column prop="Stage" label="Stage" width="100px"></el-table-column>
      <el-table-column prop="EarlyInterval" label="EarlyInterval" width="120px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="LateInterval" label="LateInterval" width="120px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="AgeMax" label="AgeMax" width="100px"></el-table-column>
      <el-table-column prop="AgeMin" label="AgeMin" width="100px"></el-table-column>
      <el-table-column prop="AgeMiddle" label="AgeMiddle" width="100px"></el-table-column>
      <el-table-column prop="Author" label="Author" width="120px" show-overflow-tooltip></el-table-column>
      <el-table-column prop="Pubyr" label="Pubyr" width="100px"></el-table-column>
      <el-table-column prop="Longitude" label="Longitude" width="100px"></el-table-column>
      <el-table-column prop="Latitude" label="Latitude" width="100px"></el-table-column>
      <el-table-column prop="TimeBin" label="TimeBin" width="100px"></el-table-column>
      <el-table-column prop="FossilType" label="FossilType" width="120px"></el-table-column>
      <el-table-column prop="pollendiagram" label="PollenDiagram" width="120px"></el-table-column>
      <el-table-column prop="Reference" label="Reference" width="300px" show-overflow-tooltip></el-table-column>
    </el-table>
    <div class="table-footer">
      <el-pagination background layout="sizes, prev, pager, next, total" :total="tableData.length" :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]" :current-page.sync="currentPage" @current-change="handlePageChange"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script>
import { getDetailData } from '@/api/paleoecology';
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { mapMutations } from "vuex";

export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    searchCriteria: {
      type: Object,
      default: () => { },
    },
    searchHistory: {
      type: Array,
      default: () => [],
    },
    selectedSearchId: {
      type: String,
      default: "",
    },
  },
  watch: {
    searchHistory: {
      immediate: true,
      handler(newHistory) {
        if (newHistory && newHistory.length > 0) {
          this.results = [...newHistory];
          if (
            this.searchCriteria &&
            Object.keys(this.searchCriteria).length > 0
          ) {
            // Try to find a matching search result based on criteria
            this.updateSelectedResultFromCriteria(this.searchCriteria);
          } else if (newHistory.length > 0) {
            // If no criteria or can't find match, default to most recent
            this.localSelectedResult = newHistory[newHistory.length - 1].id;
          }
        }
      },
    },
    selectedSearchId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.localSelectedResult = newId;
        }
      },
    },
    searchCriteria: {
      handler(newCriteria) {
        // Reset to first page when search criteria changes
        this.currentPage = 1;

        // Update the selected result in dropdown based on criteria
        if (newCriteria && Object.keys(newCriteria).length > 0) {
          this.updateSelectedResultFromCriteria(newCriteria);
        }
      },
      deep: true,
    },
    tableData: {
      handler() {
        // Reset to first page when tableData changes
        this.currentPage = 1;
        // Clear expanded rows when table data changes
        this.expandedRowKeys = [];
        this.childTableData = [];
      },
    },
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      localSelectedResult: "",
      results: [],
      debounceTimeout: null,
      expandedRowKeys: [], // 存储当前展开的行的keys
      childTableData: [], // 子表数据
      childTableLoading: false, // 子表加载状态
      currentExpandedRow: null, // 当前展开的行
    };
  },
  created() {
    // Initialize localSelectedResult from props if available
    if (this.selectedSearchId) {
      this.localSelectedResult = this.selectedSearchId;
    } else if (this.searchHistory && this.searchHistory.length > 0) {
      // Try to find a matching search result based on criteria
      if (this.searchCriteria && Object.keys(this.searchCriteria).length > 0) {
        this.updateSelectedResultFromCriteria(this.searchCriteria);
      } else {
        // Default to most recent search
        this.localSelectedResult =
          this.searchHistory[this.searchHistory.length - 1].id;
      }
    }
  },
  computed: {
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      // Use processed table data to include combined fields
      return this.processedTableData.slice(start, end);
    },
    processedTableData() {
      if (!this.tableData || this.tableData.length === 0) {
        return [];
      }

      // Process each item in the tableData array
      return this.tableData.map((item) => {
        const processedItem = { ...item }; // Create a copy of the original item

        // Combine Reference fields
        processedItem.Reference = this.combineFields(item, "Reference", 3);

        // Add OtherReferences to Reference if it exists
        if (item.OtherReferences && item.OtherReferences.trim() !== "") {
          if (processedItem.Reference) {
            processedItem.Reference += ", " + item.OtherReferences;
          } else {
            processedItem.Reference = item.OtherReferences;
          }
        }

        return processedItem;
      });
    },
    processedChildTableData() {
      console.log('processedChildTableData computed called, childTableData:', this.childTableData);

      if (!this.childTableData || this.childTableData.length === 0) {
        console.log('processedChildTableData: returning empty array');
        return [];
      }

      console.log('processedChildTableData: processing', this.childTableData.length, 'items');

      // Process each item in the childTableData array
      const processedData = this.childTableData.map((item) => {
        const processedItem = { ...item }; // Create a copy of the original item

        // Combine ScientificName fields (scientificname1, scientificname2, scientificname3)
        processedItem.ScientificName = this.combineFields(
          item,
          "scientificname",
          3
        );

        // Combine Species fields (species1, species2, species3)
        processedItem.Species = this.combineFields(item, "species", 3);

        // Combine PlantOrgan fields (plantorgan1, plantorgan2)
        processedItem.PlantOrgan = this.combineFields(item, "plantorgan", 2);

        return processedItem;
      });

      console.log('processedChildTableData result:', processedData);
      console.log('processedChildTableData sample item:', processedData.length > 0 ? processedData[0] : 'No items');
      console.log('processedChildTableData sample keys:', processedData.length > 0 ? Object.keys(processedData[0]) : 'No items');

      return processedData;
    },
  },
  methods: {
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    // Helper method to combine numbered fields
    combineFields(item, baseFieldName, count) {
      const values = [];

      for (let i = 1; i <= count; i++) {
        const fieldName = `${baseFieldName}${i}`;
        if (item[fieldName] && item[fieldName].trim() !== "") {
          values.push(item[fieldName]);
        }
      }

      return values.join(", ");
    },
    handlePageChange(page) {
      this.currentPage = page;
      // 切换页面时清空展开的行
      this.expandedRowKeys = [];
      this.childTableData = [];
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // Reset to first page when changing page size
      // 切换页面大小时清空展开的行
      this.expandedRowKeys = [];
      this.childTableData = [];
    },
    applySelectedSearch(value) {
      // Update the local selected result
      this.localSelectedResult = value;

      // Find the selected search result
      const selectedSearch = this.results.find((result) => result.id === value);
      if (selectedSearch && selectedSearch.criteria) {
        // Emit event to parent component to update search criteria
        this.$emit(
          "search-result-selected",
          JSON.parse(JSON.stringify(selectedSearch.criteria))
        );

        // Also emit the selected search ID to the parent
        this.$emit("update:selected-search-id", value);
      }

    },
    downloadData() {
      if (this.$store.state.userInfo && this.$store.state.userInfo.username && this.$store.state.userInfo.username !== '') {
        // Get current date in YYYYMMDD format
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, "0");
        const day = String(now.getDate()).padStart(2, "0");
        const dateStr = `${year}${month}${day}`;

        // Get the selected search name or use a default
        let searchName = "Search";
        if (this.localSelectedResult) {
          const selectedSearch = this.results.find(
            (result) => result.id === this.localSelectedResult
          );
          if (selectedSearch) {
            searchName = selectedSearch.id;
          }
        }

        // Create filename in format Search[number]+[date]
        const filename = `${searchName}_${dateStr}.xlsx`;

        // Get the table columns in the exact order they appear in the UI
        const tableColumns = [
          "ID", "SiteNo", "SiteName", "Country", "DatingMethod", "DatingQuality",
          "Epoch", "Stage", "EarlyInterval", "LateInterval", "AgeMax",
          "AgeMin", "AgeMiddle", "Author", "Pubyr", "Longitude",
          "Latitude", "TimeBin", "FossilType", "Reference"
        ];

        // Use processedTableData but filter to include only the fields shown in the table
        const dataToExport = this.processedTableData.map(item => {
          const filteredItem = {};
          tableColumns.forEach(column => {
            filteredItem[column] = item[column];
          });
          return filteredItem;
        });

        // Create a worksheet with columns in the same order as the table
        const ws = XLSX.utils.json_to_sheet(dataToExport, {
          header: tableColumns
        });

        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        const wbout = XLSX.write(wb, { bookType: "xlsx", type: "binary" });
        const blob = new Blob([this.s2ab(wbout)], {
          type: "application/octet-stream",
        });
        saveAs(blob, filename);


      } else {
        // console.log('未登录')
        this.$message.error('您还未登录，登录成功后才可下载数据！')
        this.SET_OPEN_LOGIN_BOX(true)
      }


    },
    s2ab(s) {
      const buf = new ArrayBuffer(s.length);
      const view = new Uint8Array(buf);
      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
      return buf;
    },
    handleRowClick(row) {
      // Navigate to detail page on row click
      // Note: Row expansion should be handled by clicking the expand icon, not the row itself
      this.$router.push({
        path: `/mapDetail/${row.ID}`,
      });
    },
    handleExpandChange(row, expanded) {
      // 当手动点击展开图标时触发
      console.log('handleExpandChange called:', { rowID: row.ID, expanded, currentChildData: this.childTableData.length });

      if (expanded) {
        this.currentExpandedRow = row;
        this.fetchChildTableData(row.ID);
      } else {
        this.childTableData = [];
        this.currentExpandedRow = null;
      }
    },
    fetchChildTableData(id) {
      this.childTableLoading = true;
      this.childTableData = [];

      // 使用新添加的/detail接口获取子表数据
      getDetailData(id)
        .then((response) => {
          console.log('Detail API response for ID', id, ':', response.data);

          // Handle the R response wrapper structure
          let detailData = [];
          if (response.data && response.data.data && response.data.data.detail) {
            // Java Spring Boot R.ok({detail: [...]}) format
            detailData = response.data.data.detail;
          } else if (response.data && response.data.detail) {
            // Direct {detail: [...]} format
            detailData = response.data.detail;
          } else if (Array.isArray(response.data)) {
            // Direct array format
            detailData = response.data;
          } else if (response.data && Array.isArray(response.data.data)) {
            // R.ok([...]) format (direct array in data)
            detailData = response.data.data;
          }

          console.log('Extracted detail data:', detailData);
          console.log('Sample detail item (first item):', detailData.length > 0 ? detailData[0] : 'No items');
          console.log('Detail data keys (first item):', detailData.length > 0 ? Object.keys(detailData[0]) : 'No items');
          this.childTableData = detailData;

          // 格式化日期时间字段
          this.childTableData.forEach((item) => {
            if (item.TimeContext) {
              // 将ISO格式的日期转换为本地日期时间格式
              const date = new Date(item.TimeContext);
              item.TimeContext = date.toLocaleString();
            }
          });

          this.childTableLoading = false;
        })
        .catch((error) => {
          // console.error("获取子表数据失败:", error);
          this.childTableData = [];
          this.childTableLoading = false;
          this.$message.error("获取子表数据失败");
        });
    },
    updateSelectedResultFromCriteria(criteria) {
      // Find a search result that matches the current criteria
      if (!this.results || this.results.length === 0) return;

      // Convert criteria to string for comparison
      const criteriaStr = JSON.stringify(criteria);

      // Try to find an exact match
      for (const result of this.results) {
        if (
          result.criteria &&
          JSON.stringify(result.criteria) === criteriaStr
        ) {
          this.localSelectedResult = result.id;
          return;
        }
      }

      // If no exact match found, we could either:
      // 1. Leave the current selection as is
      // 2. Clear the selection
      // 3. Set to the most recent result

      // Option 3: Set to most recent if no match found
      // this.localSelectedResult = this.results[this.results.length - 1].id;

      // Option 1: Leave as is (do nothing)
    },
    isMapSelection(searchItem) {
      if (!searchItem || !searchItem.criteria) return false;

      // Check if this search includes map selection
      return (
        (searchItem.criteria.bboxes && searchItem.criteria.bboxes.length > 0) ||
        (searchItem.criteria.polygons && searchItem.criteria.polygons.length > 0) ||
        (searchItem.criteria.circle_centers && searchItem.criteria.circle_centers.length > 0)
      );
    },
  },
};
</script>

<style>
.table-view {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: calc(100vh - 80px - 272px);
}

.table-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.map-selection {
  color: #409EFF;
  font-weight: bold;
}

.table-container {
  overflow: auto;
}

.table-footer {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  overflow: auto !important;
}

.el-table--border {
  border: 1px solid #ebeef5;
}

.el-table .el-table__header-wrapper th {
  position: sticky;
  top: 0;
  z-index: 2;
}

.el-table .el-table__fixed-header-wrapper th {
  position: sticky;
  top: 0;
  z-index: 3;
}

/* 子表样式 */
.child-table-container {
  padding: 10px;
  background-color: #f9f9f9;
}

.child-table-loading {
  min-height: 300px;
  /*display: flex;*/
  /*align-items: center;*/
  /*justify-content: center;*/
  max-width: 100% !important;
  padding: 8px 16px;
}

.no-child-data {
  padding: 20px;
  text-align: center;
  color: #909399;
}

::v-deep .el-pagination .el-pager .active {
  background-color: #304f56 !important;
}
</style>
