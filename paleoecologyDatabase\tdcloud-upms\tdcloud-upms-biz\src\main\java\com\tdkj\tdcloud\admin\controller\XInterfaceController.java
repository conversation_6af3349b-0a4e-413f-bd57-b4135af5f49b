/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.XInterfaceDTO;
import com.tdkj.tdcloud.admin.api.entity.XInterface;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.log.annotation.SysLog;

import com.tdkj.tdcloud.admin.service.XInterfaceService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 接口文档
 *
 * <AUTHOR> code generator
 * @date 2025-04-08 14:04:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/xinterface" )
@Tag(description = "xinterface" , name = "接口文档管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class XInterfaceController {

    private final  XInterfaceService xInterfaceService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param xInterface 接口文档
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_xinterface_view')" )
    public R getXInterfacePage(Page page, XInterfaceDTO xInterface) {
        return R.ok(xInterfaceService.getXInterfacePage(page, xInterface));
    }


    /**
     * 通过id查询接口文档
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_xinterface_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(xInterfaceService.getById(id));
    }

    /**
     * 新增接口文档
     * @param xInterface 接口文档
     * @return R
     */
    @Operation(summary = "新增接口文档" , description = "新增接口文档" )
    @SysLog("新增接口文档" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_xinterface_add')" )
    public R save(@RequestBody XInterface xInterface) {
    	xInterface.setCreateTime(LocalDateTime.now());
        return R.ok(xInterfaceService.save(xInterface));
    }

    /**
     * 修改接口文档
     * @param xInterface 接口文档
     * @return R
     */
    @Operation(summary = "修改接口文档" , description = "修改接口文档" )
    @SysLog("修改接口文档" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_xinterface_edit')" )
    public R updateById(@RequestBody XInterface xInterface) {
        return R.ok(xInterfaceService.updateById(xInterface));
    }

    /**
     * 通过id删除接口文档
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id删除接口文档" , description = "通过id删除接口文档" )
    @SysLog("通过id删除接口文档" )
    @DeleteMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('admin_xinterface_del')" )
    public R removeById(@PathVariable Integer id) {
        return R.ok(xInterfaceService.removeById(id));
    }


    /**
     * 导出excel 表格
     * @param xInterface 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_xinterface_export')" )
    public List<XInterface> export(XInterface xInterface) {
        return xInterfaceService.list(Wrappers.query(xInterface));
    }
}
