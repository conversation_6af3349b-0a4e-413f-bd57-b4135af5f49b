/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tdkj.tdcloud.admin.api.dto.XAboutUsDto;
import com.tdkj.tdcloud.admin.api.entity.XAboutUs;
import com.tdkj.tdcloud.common.core.util.R;

/**
 * 关于我们
 *
 * <AUTHOR> code generator
 * @date 2025-01-03 14:40:32
 */
public interface XAboutUsService extends IService<XAboutUs> {

	Page getXAboutUsPage(Page page, XAboutUsDto xAboutUsDto);
	R saveXAboutUs(XAboutUs xAboutUs);
	R updateXAboutUsById(XAboutUs xAboutUs);
	R getXAboutUsById(Integer id);

}
