import { request } from '@/api/request'

/**
 * Fetch unique Family values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getFamilies() {
    return request({
        url: '/admin/paleoecology/taxa/families',
        method: 'get'
    })
}

/**
 * Fetch unique Genus values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getGenera() {
    return request({
        url: '/admin/paleoecology/taxa/genera',
        method: 'get'
    })
}

/**
 * Fetch unique Species values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getSpecies() {
    return request({
        url: '/admin/paleoecology/taxa/species',
        method: 'get'
    })
}

/**
 * Fetch unique ScientificName values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getScientificNames() {
    return request({
        url: '/admin/paleoecology/taxa/scientific-names',
        method: 'get'
    })
}
