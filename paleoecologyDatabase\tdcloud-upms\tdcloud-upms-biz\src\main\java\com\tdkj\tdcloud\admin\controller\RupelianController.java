/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.admin.api.dto.RupelianDTO;
import com.tdkj.tdcloud.admin.api.entity.Rupelian;
import com.tdkj.tdcloud.admin.api.vo.ChattianExcelVO;
import com.tdkj.tdcloud.admin.api.vo.RupelianExcelVO;
import com.tdkj.tdcloud.admin.mapper.RupelianMapper;
import com.tdkj.tdcloud.admin.service.ChattianService;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.excel.annotation.RequestExcel;
import com.tdkj.tdcloud.common.log.annotation.SysLog;

import com.tdkj.tdcloud.admin.service.RupelianService;
import com.tdkj.tdcloud.common.security.annotation.Inner;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:21:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/rupelian" )
@Tag(description = "rupelian" , name = "管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class RupelianController {

    private final  RupelianService rupelianService;
    private final ChattianService chattianService;
    @Resource
	private RupelianMapper rupelianMapper;

    /**
     * 分页查询
     * @param page 分页对象
     * @param rupelian 
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_rupelian_view')" )
    public R getRupelianPage(Page page, RupelianDTO rupelian) {

        return R.ok(rupelianService.getRupelianPage(page, rupelian));
    }


    /**
     * 通过id查询
     * @param  id
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_rupelian_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(rupelianService.getRupelianById(id));
    }

	@Operation(summary = "通过id查询" , description = "通过id查询" )
	@GetMapping("/getByPId" )
//    @PreAuthorize("@pms.hasPermission('admin_rupelian_view')" )
	public R getByPId(Long pId) {
		return rupelianService.getByPId(pId);
	}

    /**
     * 新增
     * @param rupelian 
     * @return R
     */
    @Operation(summary = "新增" , description = "新增" )
    @SysLog("新增" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin_rupelian_add')" )
    public R save(@RequestBody Rupelian rupelian) {
        return R.ok(rupelianService.save(rupelian));
    }

    /**
     * 修改
     * @param rupelian 
     * @return R
     */
    @Operation(summary = "修改" , description = "修改" )
    @SysLog("修改" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin_rupelian_edit')" )
    public R updateById(@RequestBody Rupelian rupelian) {
        return R.ok(rupelianService.updateById(rupelian));
    }

    /**
     * 通过id删除
     * @param pid id
     * @return R
     */
    @Operation(summary = "通过id删除" , description = "通过id删除" )
    @SysLog("通过id删除" )
    @DeleteMapping("/{pid}" )
    @PreAuthorize("@pms.hasPermission('admin_rupelian_del')" )
    public R removeById(@PathVariable Integer pid) {
		Rupelian rupelian = rupelianMapper.selectRupelianByPid(Long.valueOf(pid));
		chattianService.removeById(rupelian.getId());
		boolean b = rupelianService.removeById(pid);
		return R.ok(b);
    }


    /**
     * 导出excel 表格
     * @param rupelian 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_rupelian_export')" )
    public List<Rupelian> export(Rupelian rupelian) {
        return rupelianService.list(Wrappers.query(rupelian));
    }

	@PostMapping("/importRupelian")
	public R importRupelian(@RequestExcel List<RupelianExcelVO> excelVOList, BindingResult bindingResult) throws Exception {
		return rupelianService.importRupelian(excelVOList, bindingResult);
	}
}
