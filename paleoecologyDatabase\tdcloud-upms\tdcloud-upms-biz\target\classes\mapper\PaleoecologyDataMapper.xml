<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdkj.tdcloud.admin.mapper.PaleoecologyDataMapper">

    <!-- Complex data query with dynamic filtering -->
    <select id="selectPaleoecologyData" resultType="java.util.Map">
        SELECT DISTINCT
            r.ID, r.Source, r.<PERSON>, r.<PERSON>o, r.SiteName, r.<PERSON>, r.<PERSON>, r.<PERSON>,
            r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>,
            r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>, r.<PERSON>,
            r.<PERSON>, r.<PERSON>ossilType, r.Reference1, r.Reference2, r.Reference3, r.<PERSON>References
        FROM Rupelian r
        <if test="params.acceptedRank != null and params.acceptedRank != '' or
                  params.phylum != null and params.phylum != '' or
                  params.classField != null and params.classField != '' or
                  params.order != null and params.order != '' or
                  params.family != null and params.family != '' or
                  params.genus != null and params.genus != '' or
                  params.species != null and params.species != '' or
                  params.scientificName != null and params.scientificName != '' or
                  params.originalName != null and params.originalName != '' or
                  params.plantOrgan != null and params.plantOrgan != ''">
            LEFT JOIN Chattian c ON r.ID = c.ID
        </if>
        <where>
            <if test="params.acceptedRank != null and params.acceptedRank != ''">
                AND c.AcceptedRank LIKE CONCAT('%', #{params.acceptedRank}, '%')
            </if>
            <if test="params.phylum != null and params.phylum != ''">
                AND c.Phylum LIKE CONCAT('%', #{params.phylum}, '%')
            </if>
            <if test="params.classField != null and params.classField != ''">
                AND c.class_new LIKE CONCAT('%', #{params.classField}, '%')
            </if>
            <if test="params.order != null and params.order != ''">
                AND c.order_new LIKE CONCAT('%', #{params.order}, '%')
            </if>
            <if test="params.family != null and params.family != ''">
                AND c.Family LIKE CONCAT('%', #{params.family}, '%')
            </if>
            <if test="params.genus != null and params.genus != ''">
                AND c.Genus LIKE CONCAT('%', #{params.genus}, '%')
            </if>
            <if test="params.species != null and params.species != ''">
                AND (c.Species1 LIKE CONCAT('%', #{params.species}, '%') OR 
                     c.Species2 LIKE CONCAT('%', #{params.species}, '%') OR 
                     c.Species3 LIKE CONCAT('%', #{params.species}, '%'))
            </if>
            <if test="params.scientificName != null and params.scientificName != ''">
                AND (c.ScientificName1 LIKE CONCAT('%', #{params.scientificName}, '%') OR 
                     c.ScientificName2 LIKE CONCAT('%', #{params.scientificName}, '%') OR 
                     c.ScientificName3 LIKE CONCAT('%', #{params.scientificName}, '%'))
            </if>
            <if test="params.originalName != null and params.originalName != ''">
                AND c.OriginalName LIKE CONCAT('%', #{params.originalName}, '%')
            </if>
            <if test="params.epoch != null and params.epoch != ''">
                AND r.Epoch LIKE CONCAT('%', #{params.epoch}, '%')
            </if>
            <if test="params.stage != null and params.stage != ''">
                AND r.Stage LIKE CONCAT('%', #{params.stage}, '%')
            </if>
            <if test="params.earlyInterval != null and params.earlyInterval != ''">
                AND r.EarlyInterval LIKE CONCAT('%', #{params.earlyInterval}, '%')
            </if>
            <if test="params.lateInterval != null and params.lateInterval != ''">
                AND r.LateInterval LIKE CONCAT('%', #{params.lateInterval}, '%')
            </if>
            <if test="params.timeBin != null and params.timeBin != ''">
                AND r.TimeBin LIKE CONCAT('%', #{params.timeBin}, '%')
            </if>
            <if test="params.ageMin != null and params.ageMin != '' and params.ageMax != null and params.ageMax != ''">
                AND r.AgeMin >= #{params.ageMin} AND r.AgeMax &lt;= #{params.ageMax}
            </if>
            <if test="params.ageMin != null and params.ageMin != '' and (params.ageMax == null or params.ageMax == '')">
                AND r.AgeMin >= #{params.ageMin}
            </if>
            <if test="params.ageMax != null and params.ageMax != '' and (params.ageMin == null or params.ageMin == '')">
                AND r.AgeMax &lt;= #{params.ageMax}
            </if>
            <if test="params.country != null and params.country != ''">
                AND r.Country LIKE CONCAT('%', #{params.country}, '%')
            </if>
            <if test="params.southLatitude != null and params.southLatitude != '-90' or
                      params.northLatitude != null and params.northLatitude != '90' or
                      params.westLongitude != null and params.westLongitude != '-180' or
                      params.eastLongitude != null and params.eastLongitude != '180'">
                AND (CAST(r.Latitude AS DECIMAL(10,6)) BETWEEN #{params.southLatitude} AND #{params.northLatitude} 
                     AND CAST(r.Longitude AS DECIMAL(10,6)) BETWEEN #{params.westLongitude} AND #{params.eastLongitude})
            </if>
            <if test="params.datingMethod != null and params.datingMethod != ''">
                AND r.DatingMethod LIKE CONCAT('%', #{params.datingMethod}, '%')
            </if>
            <if test="params.datingQuality != null and params.datingQuality != ''">
                AND r.DatingQuality LIKE CONCAT('%', #{params.datingQuality}, '%')
            </if>
            <if test="params.author != null and params.author != ''">
                AND r.Author LIKE CONCAT('%', #{params.author}, '%')
            </if>
            <if test="params.pubyr != null and params.pubyr != ''">
                AND r.Pubyr LIKE CONCAT('%', #{params.pubyr}, '%')
            </if>
            <if test="params.fossilType != null and params.fossilType != ''">
                AND r.FossilType LIKE CONCAT('%', #{params.fossilType}, '%')
            </if>
            <if test="params.plantOrgan != null and params.plantOrgan != ''">
                AND (c.PlantOrgan1 LIKE CONCAT('%', #{params.plantOrgan}, '%') OR 
                     c.PlantOrgan2 LIKE CONCAT('%', #{params.plantOrgan}, '%'))
            </if>
        </where>
    </select>

    <!-- Get detail data from Chattian table by ID -->
    <select id="selectChattianDetailById" resultType="com.tdkj.tdcloud.admin.api.entity.Chattian">
        SELECT * FROM Chattian WHERE ID = #{id}
    </select>

    <!-- Get unique dating method values -->
    <select id="selectUniqueDatingMethods" resultType="java.lang.String">
        SELECT DISTINCT DatingMethod 
        FROM Rupelian 
        WHERE DatingMethod IS NOT NULL AND DatingMethod != ''
        ORDER BY DatingMethod
    </select>

    <!-- Get unique dating quality values -->
    <select id="selectUniqueDatingQualities" resultType="java.lang.String">
        SELECT DISTINCT DatingQuality 
        FROM Rupelian 
        WHERE DatingQuality IS NOT NULL AND DatingQuality != ''
        ORDER BY DatingQuality
    </select>

    <!-- Get unique family values -->
    <select id="selectUniqueFamilies" resultType="java.lang.String">
        SELECT DISTINCT Family 
        FROM Chattian 
        WHERE Family IS NOT NULL AND Family != ''
        ORDER BY Family
    </select>

    <!-- Get unique genus values -->
    <select id="selectUniqueGenera" resultType="java.lang.String">
        SELECT DISTINCT Genus 
        FROM Chattian 
        WHERE Genus IS NOT NULL AND Genus != ''
        ORDER BY Genus
    </select>

    <!-- Get unique species values -->
    <select id="selectUniqueSpecies" resultType="java.lang.String">
        SELECT DISTINCT Species1 FROM Chattian WHERE Species1 IS NOT NULL AND Species1 != ''
        UNION
        SELECT DISTINCT Species2 FROM Chattian WHERE Species2 IS NOT NULL AND Species2 != ''
        UNION
        SELECT DISTINCT Species3 FROM Chattian WHERE Species3 IS NOT NULL AND Species3 != ''
        ORDER BY Species1
    </select>

    <!-- Get unique scientific name values -->
    <select id="selectUniqueScientificNames" resultType="java.lang.String">
        SELECT DISTINCT ScientificName1 FROM Chattian WHERE ScientificName1 IS NOT NULL AND ScientificName1 != ''
        UNION
        SELECT DISTINCT ScientificName2 FROM Chattian WHERE ScientificName2 IS NOT NULL AND ScientificName2 != ''
        UNION
        SELECT DISTINCT ScientificName3 FROM Chattian WHERE ScientificName3 IS NOT NULL AND ScientificName3 != ''
        ORDER BY ScientificName1
    </select>

    <!-- Get unique original name values -->
    <select id="selectUniqueOriginalNames" resultType="java.lang.String">
        SELECT DISTINCT OriginalName 
        FROM Chattian 
        WHERE OriginalName IS NOT NULL AND OriginalName != ''
        ORDER BY OriginalName
    </select>

    <!-- Get unique fossil type values -->
    <select id="selectUniqueFossilTypes" resultType="java.lang.String">
        SELECT DISTINCT FossilType 
        FROM Rupelian 
        WHERE FossilType IS NOT NULL AND FossilType != ''
        ORDER BY FossilType
    </select>

    <!-- Get unique plant organ values -->
    <select id="selectUniquePlantOrgans" resultType="java.lang.String">
        SELECT DISTINCT PlantOrgan1 FROM Chattian WHERE PlantOrgan1 IS NOT NULL AND PlantOrgan1 != ''
        UNION
        SELECT DISTINCT PlantOrgan2 FROM Chattian WHERE PlantOrgan2 IS NOT NULL AND PlantOrgan2 != ''
        ORDER BY PlantOrgan1
    </select>

</mapper>
