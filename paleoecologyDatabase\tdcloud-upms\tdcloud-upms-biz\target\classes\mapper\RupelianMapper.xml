<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tdkj.tdcloud.admin.mapper.RupelianMapper">

  <resultMap id="rupelianMap" type="com.tdkj.tdcloud.admin.api.entity.Rupelian">
	  <id property="pid" column="Pid"/>
	  <result property="id" column="ID"/>
	  <result property="siteno" column="SiteNo"/>
	  <result property="sitename" column="SiteName"/>
	  <result property="country" column="Country"/>
	  <result property="datingmethod" column="DatingMethod"/>
	  <result property="datingquality" column="DatingQuality"/>
	  <result property="epoch" column="Epoch"/>
	  <result property="stage" column="Stage"/>
	  <result property="earlyinterval" column="EarlyInterval"/>
	  <result property="lateinterval" column="LateInterval"/>
	  <result property="agemax" column="AgeMax"/>
	  <result property="agemin" column="AgeMin"/>
	  <result property="agemiddle" column="AgeMiddle"/>
	  <result property="author" column="Author"/>
	  <result property="pubyr" column="Pubyr"/>
	  <result property="longitude" column="Longitude"/>
	  <result property="latitude" column="Latitude"/>
	  <result property="timebin" column="TimeBin"/>
	  <result property="fossiltype" column="FossilType"/>
	  <result property="reference1" column="Reference1"/>
	  <result property="reference2" column="Reference2"/>
	  <result property="reference3" column="Reference3"/>
	  <result property="otherreferences" column="OtherReferences"/>
	  <result property="pollendiagram" column="PollenDiagram"/>
  </resultMap>

	<sql id="selectRupelianVo">
		select Pid, ID, SiteNo, SiteName,PollenDiagram, Country, DatingMethod, DatingQuality, Epoch, Stage, EarlyInterval, LateInterval, AgeMax, AgeMin, AgeMiddle, Author, Pubyr, Longitude, Latitude, TimeBin, FossilType, Reference1, Reference2, Reference3, OtherReferences from rupelian
	</sql>

	<select id="selectRupelianList" parameterType="com.tdkj.tdcloud.admin.api.entity.Rupelian" resultType="com.tdkj.tdcloud.admin.api.entity.Rupelian">
		select Pid, ID, SiteNo, SiteName, Source, SourceID, Collectionme, Country, DatingMethod, DatingQuality, Epoch, Stage, EarlyInterval, LateInterval, AgeMax, AgeMin, AgeMiddle, Author, Pubyr, Longitude, Latitude, TimeBin, FossilType, Reference1, Reference2, Reference3, OtherReferences from rupelian
		<where>
			<if test="ID != null  and ID != ''"> and ID = #{ID}</if>
			<if test="SiteNo != null "> and SiteNo = #{SiteNo}</if>
			<if test="SiteName != null  and SiteName != ''"> and SiteName like concat('%', #{SiteName}, '%')</if>
			<if test="Source != null  and Source != ''"> and Source = #{Source}</if>
			<if test="SourceID != null  and SourceID != ''"> and SourceID = #{SourceID}</if>
			<if test="Collectionme != null  and Collectionme != ''"> and Collectionme = #{Collectionme}</if>
			<if test="Country != null  and Country != ''"> and Country = #{Country}</if>
			<if test="DatingMethod != null  and DatingMethod != ''"> and DatingMethod = #{DatingMethod}</if>
			<if test="DatingQuality != null  and DatingQuality != ''"> and DatingQuality = #{DatingQuality}</if>
			<if test="Epoch != null  and Epoch != ''"> and Epoch = #{Epoch}</if>
			<if test="Stage != null  and Stage != ''"> and Stage = #{Stage}</if>
			<if test="EarlyInterval != null  and EarlyInterval != ''"> and EarlyInterval = #{EarlyInterval}</if>
			<if test="LateInterval != null  and LateInterval != ''"> and LateInterval = #{LateInterval}</if>
			<if test="AgeMax != null  and AgeMax != ''"> and AgeMax = #{AgeMax}</if>
			<if test="AgeMin != null  and AgeMin != ''"> and AgeMin = #{AgeMin}</if>
			<if test="AgeMiddle != null  and AgeMiddle != ''"> and AgeMiddle = #{AgeMiddle}</if>
			<if test="Author != null  and Author != ''"> and Author = #{Author}</if>
			<if test="Pubyr != null  and Pubyr != ''"> and Pubyr = #{Pubyr}</if>
			<if test="Longitude != null  and Longitude != ''"> and Longitude = #{Longitude}</if>
			<if test="Latitude != null  and Latitude != ''"> and Latitude = #{Latitude}</if>
			<if test="TimeBin != null  and TimeBin != ''"> and TimeBin = #{TimeBin}</if>
			<if test="FossilType != null  and FossilType != ''"> and FossilType = #{FossilType}</if>
			<if test="Reference1 != null  and Reference1 != ''"> and Reference1 = #{Reference1}</if>
			<if test="Reference2 != null  and Reference2 != ''"> and Reference2 = #{Reference2}</if>
			<if test="Reference3 != null  and Reference3 != ''"> and Reference3 = #{Reference3}</if>
			<if test="OtherReferences != null  and OtherReferences != ''"> and OtherReferences = #{OtherReferences}</if>
		</where>
	</select>

	<select id="selectRupelianByPid" parameterType="Long" resultType="com.tdkj.tdcloud.admin.api.entity.Rupelian">
		select Pid, ID, SiteNo, SiteName, Source, PollenDiagram,SourceID, Collectionme, Country, DatingMethod, DatingQuality, Epoch, Stage, EarlyInterval, LateInterval, AgeMax, AgeMin, AgeMiddle, Author, Pubyr, Longitude, Latitude, TimeBin, FossilType, Reference1, Reference2, Reference3, OtherReferences from rupelian

		where Pid = #{Pid}
	</select>
	<select id="selectRupelianById" parameterType="int" resultType="com.tdkj.tdcloud.admin.api.entity.Rupelian">
		select Pid, ID, SiteNo, SiteName, Source,PollenDiagram, SourceID, Collectionme, Country, DatingMethod, DatingQuality, Epoch, Stage, EarlyInterval, LateInterval, AgeMax, AgeMin, AgeMiddle, Author, Pubyr, Longitude, Latitude, TimeBin, FossilType, Reference1, Reference2, Reference3, OtherReferences from rupelian

		where ID = #{id}
	</select>

	<select id="getPercentageList"  resultType="com.tdkj.tdcloud.admin.api.entity.SysFile">
		SELECT id,file_name,bucket_name,original,url,
			   `name` FROM sys_file WHERE x_type = #{xType} AND del_flag = '0' and original = #{original}
	</select>

	<select id="getRupelianSiteNameTotal"  resultType="int">
		select COUNT(DISTINCT SiteName) AS total from rupelian

	</select>

	<select id="getRupelianEpochTotal"  resultType="com.tdkj.tdcloud.admin.api.entity.Epoch">
		SELECT
			SUM(Epoch REGEXP 'Paleocene') AS paleoceneCount,
			SUM(Epoch REGEXP 'Eocene') AS eoceneCount,
			SUM(Epoch REGEXP 'Oligocene') AS oligoceneCount,
			SUM(Epoch REGEXP 'Miocene') AS mioceneCount,
			SUM(Epoch REGEXP 'Pliocene') AS plioceneCount,
			SUM(Epoch REGEXP 'Pleistocene') AS pleistoceneCount,
			SUM(Epoch REGEXP 'Holocene') AS holoceneCount
		FROM
			rupelian;

	</select>

	<select id="getRupelianFossilTotal"  resultType="com.tdkj.tdcloud.admin.api.entity.Fossil">
		SELECT
			FossilType name,
			COUNT(*) AS value
		FROM
			rupelian
		GROUP BY
			FossilType;
	</select>

	<insert id="insertRupelian" parameterType="com.tdkj.tdcloud.admin.api.entity.Rupelian" useGeneratedKeys="true" keyProperty="Pid">
		insert into rupelian
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="ID != null">ID,</if>
			<if test="SiteNo != null">SiteNo,</if>
			<if test="SiteName != null">SiteName,</if>

			<if test="Country != null">Country,</if>
			<if test="DatingMethod != null">DatingMethod,</if>
			<if test="DatingQuality != null">DatingQuality,</if>
			<if test="Epoch != null">Epoch,</if>
			<if test="Stage != null">Stage,</if>
			<if test="EarlyInterval != null">EarlyInterval,</if>
			<if test="LateInterval != null">LateInterval,</if>
			<if test="AgeMax != null">AgeMax,</if>
			<if test="AgeMin != null">AgeMin,</if>
			<if test="AgeMiddle != null">AgeMiddle,</if>
			<if test="Author != null">Author,</if>
			<if test="Pubyr != null">Pubyr,</if>
			<if test="Longitude != null">Longitude,</if>
			<if test="Latitude != null">Latitude,</if>
			<if test="TimeBin != null">TimeBin,</if>
			<if test="FossilType != null">FossilType,</if>
			<if test="Reference1 != null">Reference1,</if>
			<if test="Reference2 != null">Reference2,</if>
			<if test="Reference3 != null">Reference3,</if>
			<if test="OtherReferences != null">OtherReferences,</if>
			<if test="PollenDiagram != null">PollenDiagram,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="ID != null">#{ID},</if>
			<if test="SiteNo != null">#{SiteNo},</if>
			<if test="SiteName != null">#{SiteName},</if>

			<if test="Country != null">#{Country},</if>
			<if test="DatingMethod != null">#{DatingMethod},</if>
			<if test="DatingQuality != null">#{DatingQuality},</if>
			<if test="Epoch != null">#{Epoch},</if>
			<if test="Stage != null">#{Stage},</if>
			<if test="EarlyInterval != null">#{EarlyInterval},</if>
			<if test="LateInterval != null">#{LateInterval},</if>
			<if test="AgeMax != null">#{AgeMax},</if>
			<if test="AgeMin != null">#{AgeMin},</if>
			<if test="AgeMiddle != null">#{AgeMiddle},</if>
			<if test="Author != null">#{Author},</if>
			<if test="Pubyr != null">#{Pubyr},</if>
			<if test="Longitude != null">#{Longitude},</if>
			<if test="Latitude != null">#{Latitude},</if>
			<if test="TimeBin != null">#{TimeBin},</if>
			<if test="FossilType != null">#{FossilType},</if>
			<if test="Reference1 != null">#{Reference1},</if>
			<if test="Reference2 != null">#{Reference2},</if>
			<if test="Reference3 != null">#{Reference3},</if>
			<if test="OtherReferences != null">#{OtherReferences},</if>
			<if test="PollenDiagram != null">#{PollenDiagram},</if>
		</trim>
	</insert>

	<insert id="insertRupelianExcel" parameterType="com.tdkj.tdcloud.admin.api.vo.RupelianExcelVO" useGeneratedKeys="true" keyProperty="Pid">
		insert into rupelian
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="ID != null">ID,</if>
			<if test="SiteNo != null">SiteNo,</if>
			<if test="SiteName != null">SiteName,</if>
			<if test="Country != null">Country,</if>
			<if test="DatingMethod != null">DatingMethod,</if>
			<if test="DatingQuality != null">DatingQuality,</if>
			<if test="Epoch != null">Epoch,</if>
			<if test="Stage != null">Stage,</if>
			<if test="EarlyInterval != null">EarlyInterval,</if>
			<if test="LateInterval != null">LateInterval,</if>
			<if test="AgeMax != null">AgeMax,</if>
			<if test="AgeMin != null">AgeMin,</if>
			<if test="AgeMiddle != null">AgeMiddle,</if>
			<if test="Author != null">Author,</if>
			<if test="Pubyr != null">Pubyr,</if>
			<if test="Longitude != null">Longitude,</if>
			<if test="Latitude != null">Latitude,</if>
			<if test="TimeBin != null">TimeBin,</if>
			<if test="FossilType != null">FossilType,</if>
			<if test="Reference1 != null">Reference1,</if>
			<if test="Reference2 != null">Reference2,</if>
			<if test="Reference3 != null">Reference3,</if>
			<if test="OtherReferences != null">OtherReferences,</if>
			<if test="PollenDiagram != null">PollenDiagram,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="ID != null">#{ID},</if>
			<if test="SiteNo != null">#{SiteNo},</if>
			<if test="SiteName != null">#{SiteName},</if>
			<if test="Country != null">#{Country},</if>
			<if test="DatingMethod != null">#{DatingMethod},</if>
			<if test="DatingQuality != null">#{DatingQuality},</if>
			<if test="Epoch != null">#{Epoch},</if>
			<if test="Stage != null">#{Stage},</if>
			<if test="EarlyInterval != null">#{EarlyInterval},</if>
			<if test="LateInterval != null">#{LateInterval},</if>
			<if test="AgeMax != null">#{AgeMax},</if>
			<if test="AgeMin != null">#{AgeMin},</if>
			<if test="AgeMiddle != null">#{AgeMiddle},</if>
			<if test="Author != null">#{Author},</if>
			<if test="Pubyr != null">#{Pubyr},</if>
			<if test="Longitude != null">#{Longitude},</if>
			<if test="Latitude != null">#{Latitude},</if>
			<if test="TimeBin != null">#{TimeBin},</if>
			<if test="FossilType != null">#{FossilType},</if>
			<if test="Reference1 != null">#{Reference1},</if>
			<if test="Reference2 != null">#{Reference2},</if>
			<if test="Reference3 != null">#{Reference3},</if>
			<if test="OtherReferences != null">#{OtherReferences},</if>
			<if test="PollenDiagram != null">#{PollenDiagram},</if>
		</trim>
	</insert>

	<update id="updateRupelian" parameterType="com.tdkj.tdcloud.admin.api.entity.Rupelian">
		update rupelian
		<trim prefix="SET" suffixOverrides=",">
			<if test="ID != null">ID = #{ID},</if>
			<if test="SiteNo != null">SiteNo = #{SiteNo},</if>
			<if test="SiteName != null">SiteName = #{SiteName},</if>
			<if test="Country != null">Country = #{Country},</if>
			<if test="DatingMethod != null">DatingMethod = #{DatingMethod},</if>
			<if test="DatingQuality != null">DatingQuality = #{DatingQuality},</if>
			<if test="Epoch != null">Epoch = #{Epoch},</if>
			<if test="Stage != null">Stage = #{Stage},</if>
			<if test="EarlyInterval != null">EarlyInterval = #{EarlyInterval},</if>
			<if test="LateInterval != null">LateInterval = #{LateInterval},</if>
			<if test="AgeMax != null">AgeMax = #{AgeMax},</if>
			<if test="AgeMin != null">AgeMin = #{AgeMin},</if>
			<if test="AgeMiddle != null">AgeMiddle = #{AgeMiddle},</if>
			<if test="Author != null">Author = #{Author},</if>
			<if test="Pubyr != null">Pubyr = #{Pubyr},</if>
			<if test="Longitude != null">Longitude = #{Longitude},</if>
			<if test="Latitude != null">Latitude = #{Latitude},</if>
			<if test="TimeBin != null">TimeBin = #{TimeBin},</if>
			<if test="FossilType != null">FossilType = #{FossilType},</if>
			<if test="Reference1 != null">Reference1 = #{Reference1},</if>
			<if test="Reference2 != null">Reference2 = #{Reference2},</if>
			<if test="Reference3 != null">Reference3 = #{Reference3},</if>
			<if test="OtherReferences != null">OtherReferences = #{OtherReferences},</if>
			<if test="PollenDiagram != null">PollenDiagram = #{PollenDiagram},</if>
		</trim>
		where Pid = #{Pid}
	</update>

	<update id="updateRupelianExcel" parameterType="com.tdkj.tdcloud.admin.api.vo.RupelianExcelVO">
		update rupelian
		<trim prefix="SET" suffixOverrides=",">
			<if test="ID != null">ID = #{ID},</if>
			<if test="SiteNo != null">SiteNo = #{SiteNo},</if>
			<if test="SiteName != null">SiteName = #{SiteName},</if>
			<if test="Country != null">Country = #{Country},</if>
			<if test="DatingMethod != null">DatingMethod = #{DatingMethod},</if>
			<if test="DatingQuality != null">DatingQuality = #{DatingQuality},</if>
			<if test="Epoch != null">Epoch = #{Epoch},</if>
			<if test="Stage != null">Stage = #{Stage},</if>
			<if test="EarlyInterval != null">EarlyInterval = #{EarlyInterval},</if>
			<if test="LateInterval != null">LateInterval = #{LateInterval},</if>
			<if test="AgeMax != null">AgeMax = #{AgeMax},</if>
			<if test="AgeMin != null">AgeMin = #{AgeMin},</if>
			<if test="AgeMiddle != null">AgeMiddle = #{AgeMiddle},</if>
			<if test="Author != null">Author = #{Author},</if>
			<if test="Pubyr != null">Pubyr = #{Pubyr},</if>
			<if test="Longitude != null">Longitude = #{Longitude},</if>
			<if test="Latitude != null">Latitude = #{Latitude},</if>
			<if test="TimeBin != null">TimeBin = #{TimeBin},</if>
			<if test="FossilType != null">FossilType = #{FossilType},</if>
			<if test="Reference1 != null">Reference1 = #{Reference1},</if>
			<if test="Reference2 != null">Reference2 = #{Reference2},</if>
			<if test="Reference3 != null">Reference3 = #{Reference3},</if>
			<if test="OtherReferences != null">OtherReferences = #{OtherReferences},</if>
			<if test="PollenDiagram != null">PollenDiagram = #{PollenDiagram},</if>
		</trim>
		where Pid = #{Pid}
	</update>

	<delete id="deleteRupelianByPid" parameterType="Long">
		delete from rupelian where Pid = #{Pid}
	</delete>

	<delete id="deleteRupelianByPids" parameterType="String">
		delete from rupelian where Pid in
		<foreach item="Pid" collection="array" open="(" separator="," close=")">
			#{Pid}
		</foreach>
	</delete>


</mapper>
