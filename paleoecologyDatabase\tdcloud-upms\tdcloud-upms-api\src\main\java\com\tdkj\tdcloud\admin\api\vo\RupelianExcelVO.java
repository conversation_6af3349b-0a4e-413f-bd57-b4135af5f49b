/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 *
 * <AUTHOR> code generator
 * @date 2025-03-12 15:21:41
 */
@Data
@TableName("rupelian")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "")
public class RupelianExcelVO extends Model<RupelianExcelVO> {

	private static final long serialVersionUID = 1L;

	/**
	 * pid
	 */
//	@TableId(type = IdType.ASSIGN_ID)
//	@ExcelProperty("pid")
//	private Integer pid;

	private Long Pid;

	@ExcelProperty("ID")
	private String ID;

	@ExcelProperty("SiteNo")
	private Long SiteNo;

	@ExcelProperty("SiteName")
	private String SiteName;

	@ExcelProperty("Source")
	private String Source;

	@ExcelProperty("SourceID")
	private String SourceID;

	@ExcelProperty("Collectionme")
	private String Collectionme;

	@ExcelProperty("Country")
	private String Country;

	@ExcelProperty("DatingMethod")
	private String DatingMethod;

	@ExcelProperty("DatingQuality")
	private String DatingQuality;

	@ExcelProperty("Epoch")
	private String Epoch;

	@ExcelProperty("Stage")
	private String Stage;

	@ExcelProperty("EarlyInterval")
	private String EarlyInterval;

	@ExcelProperty("LateInterval")
	private String LateInterval;

	@ExcelProperty("AgeMax")
	private Float AgeMax;

	@ExcelProperty("AgeMin")
	private Float AgeMin;

	@ExcelProperty("AgeMiddle")
	private Float AgeMiddle;

	@ExcelProperty("Author")
	private String Author;

	@ExcelProperty("Pubyr")
	private String Pubyr;

	@ExcelProperty("Longitude")
	private String Longitude;

	@ExcelProperty("Latitude")
	private String Latitude;

	@ExcelProperty("TimeBin")
	private String TimeBin;

	@ExcelProperty("FossilType")
	private String FossilType;

	@ExcelProperty("PollenDiagram")
	private String PollenDiagram;

	@ExcelProperty("Reference1")
	private String Reference1;

	@ExcelProperty("Reference2")
	private String Reference2;

	@ExcelProperty("Reference3")
	private String Reference3;

	@ExcelProperty("OtherReferences")
	private String OtherReferences;


}