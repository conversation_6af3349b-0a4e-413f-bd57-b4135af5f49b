<template>
  <div class="mod-config">
    <basic-container>
      <el-form :inline="true" :model="dataForm" >
        <el-form-item>
<!--          <el-button v-if="permissions.admin_chattian_add" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
<!--          <el-button v-if="permissions.admin_chattian_export" icon="el-icon-download" type="primary" plain @click="exportExcel()">导出</el-button>-->
          <el-button  icon="el-icon-upload" type="primary" plain @click="importExcel()">导入</el-button>

        </el-form-item>
      </el-form>

      <div class="avue-crud">
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading">
            <el-table-column
                    prop="id"
                    header-align="center"
                    align="center"
                    label="id">
            </el-table-column>
            <el-table-column
                    prop="originalname"
                    header-align="center"
                    align="center"
                    label="originalname">
            </el-table-column>
            <el-table-column
                    prop="scientificname1"
                    header-align="center"
                    align="center"
                    label="scientificname1">
            </el-table-column>
            <el-table-column
                    prop="scientificname2"
                    header-align="center"
                    align="center"
                    label="scientificname2">
            </el-table-column>
            <el-table-column
                    prop="scientificname3"
                    header-align="center"
                    align="center"
                    label="scientificname3">
            </el-table-column>
            <el-table-column
                    prop="acceptedrank"
                    header-align="center"
                    align="center"
                    label="acceptedrank">
            </el-table-column>
            <el-table-column
                    prop="phylum"
                    header-align="center"
                    align="center"
                    label="phylum">
            </el-table-column>
            <el-table-column
                    prop="classNew"
                    header-align="center"
                    align="center"
                    label="class">
            </el-table-column>
            <el-table-column
                    prop="orderNew"
                    header-align="center"
                    align="center"
                    label="order">
            </el-table-column>
            <el-table-column
                    prop="family"
                    header-align="center"
                    align="center"
                    label="family">
            </el-table-column>
            <el-table-column
                    prop="genus"
                    header-align="center"
                    align="center"
                    label="genus">
            </el-table-column>
            <el-table-column
                    prop="species1"
                    header-align="center"
                    align="center"
                    label="species1">
            </el-table-column>
            <el-table-column
                    prop="species2"
                    header-align="center"
                    align="center"
                    label="species2">
            </el-table-column>
            <el-table-column
                    prop="species3"
                    header-align="center"
                    align="center"
                    label="species3">
            </el-table-column>
            <el-table-column
                    prop="plantorgan1"
                    header-align="center"
                    align="center"
                    label="plantorgan1">
            </el-table-column>
            <el-table-column
                    prop="plantorgan2"
                    header-align="center"
                    align="center"
                    label="plantorgan2">
            </el-table-column>
            <el-table-column
                    prop="abundvalue"
                    header-align="center"
                    align="center"
                    label="abundvalue">
            </el-table-column>
            <el-table-column
                    prop="abundunit"
                    header-align="center"
                    align="center"
                    label="abundunit">
            </el-table-column>
            <el-table-column
                    prop="fossiltype"
                    header-align="center"
                    align="center"
                    label="fossiltype">
            </el-table-column>
          <el-table-column
                  header-align="center"
                  align="center"
                  label="操作">
            <template slot-scope="scope">
              <el-button v-if="permissions.admin_chattian_edit" type="text" size="small" icon="el-icon-edit" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
              <el-button v-if="permissions.admin_chattian_del" type="text" size="small" icon="el-icon-delete" @click="deleteHandle(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="avue-crud__pagination">
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                background
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <table-form v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></table-form>
      <el-dialog
        title="导入叶角质层数据信息表" center
        :visible.sync="isImportData"
        width="50%"
        :close-on-click-modal="false"
        append-to-body >
        <ImportData ref="importData" :importType="'chattian'" :searchForm="searchForm" :export-file-name="'采集地数据信息'"
                    @exportSuccess="exportSuccess"  @errorSuccess="isImportData = false" v-on="$listeners"/>
      </el-dialog>
    </basic-container>
  </div>
</template>

<script>
  import {fetchList, delObj} from '@/api/chattian'
  import TableForm from './chattian-form'
  import ImportData from '@/components/importData/index'

  import {mapGetters} from 'vuex'

  export default {
    data() {
      return {
        dataForm: {
          key: ''
        },
        searchForm: {},
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        addOrUpdateVisible: false,
        isImportData: false
      }
    },
    components: {
      TableForm,
      ImportData
    },
    computed: {
      ...mapGetters(['permissions'])
    },
    created() {
      this.getDataList()
    },
    methods: {
      //导入生物分类层级表
      importExcel() {
        this.isImportData = true
      },
      //导入成功
      exportSuccess() {
        this.isImportData = false
        this.getDataList()
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        fetchList(Object.assign({
          current: this.pageIndex,
          size: this.pageSize
        })).then(response => {
          this.dataList = response.data.data.records
          this.totalPage = response.data.data.total
        })
        this.dataListLoading = false
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
      this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        this.$confirm('是否确认删除ID为' + id, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(id)
        }).then(data => {
          this.$message.success('删除成功')
          this.getDataList()
        })
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/admin/chattian/export', this.searchForm, 'chattian.xlsx')
      }
    }
  }
</script>
