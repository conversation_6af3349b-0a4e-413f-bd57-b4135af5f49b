<!--
  -    Copyright (c) 2018-2025, tdcloud All rights reserved.
  -
  - Redistribution and use in source and binary forms, with or without
  - modification, are permitted provided that the following conditions are met:
  -
  - Redistributions of source code must retain the above copyright notice,
  - this list of conditions and the following disclaimer.
  - Redistributions in binary form must reproduce the above copyright
  - notice, this list of conditions and the following disclaimer in the
  - documentation and/or other materials provided with the distribution.
  - Neither the name of the pig4cloud.com developer nor the names of its
  - contributors may be used to endorse or promote products derived from
  - this software without specific prior written permission.
  - Author: tdcloud
  -->
<template>
  <div class="execution">
    <basic-container>
      <avue-crud
              ref="crud"
              :page.sync="page"
              :data="tableData"
              :permission="permissionList"
              :table-loading="tableLoading"
              :option="tableOption"
              @on-load="getList"
              @search-change="searchChange"
              @refresh-change="refreshChange"
              @size-change="sizeChange"
              @current-change="currentChange"
              @row-update="handleUpdate"
              @row-save="handleSave"
              @row-del="handleDel">
        <template slot="menuLeft">
          <el-button
                  v-if="permissions.${moduleName}_${pathName}_export"
                  class="filter-item"
                  plain
                  type="primary"
                  size="small"
                  icon="el-icon-download"
                  @click="exportExcel">导出
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
  </div>
</template>

<script>
  import {fetchList, addObj, putObj, delObj} from '@/api/${pathName}'
  import {tableOption} from '@/const/crud/${pathName}'
  import {mapGetters} from 'vuex'

  export default {
    name: '${pathName}',
    data() {
      return {
        searchForm: {},
        tableData: [],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20 // 每页显示多少条
        },
        tableLoading: false,
        tableOption: tableOption
      }
    },
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permissions.${moduleName}_${pathName}_add, false),
          delBtn: this.vaildData(this.permissions.${moduleName}_${pathName}_del, false),
          editBtn: this.vaildData(this.permissions.${moduleName}_${pathName}_edit, false)
        }
      }
    },
    methods: {
      // 列表查询
      getList(page, params) {
        this.tableLoading = true
        fetchList(Object.assign({
          current: page.currentPage,
          size: page.pageSize
        }, params, this.searchForm)).then(response => {
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },
      // 删除
      handleDel: function (row, index) {
        this.$confirm('是否确认删除ID为' + row.$pk.lowerAttrName, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delObj(row.$pk.lowerAttrName)
        }).then(data => {
          this.$message.success('删除成功')
          this.getList(this.page)
        })
      },
      // 更新
      handleUpdate: function (row, index, done, loading) {
        putObj(row).then(data => {
          this.$message.success('修改成功')
          done()
          this.getList(this.page)
        }).catch(() => {
          loading()
        })
      },
      // 保存
      handleSave: function (row, done, loading) {
        addObj(row).then(data => {
          this.$message.success('添加成功')
          done()
          this.getList(this.page)
        }).catch(() => {
          loading()
        })
      },
      // 每页条数改变事件
      sizeChange(pageSize) {
        this.page.pageSize = pageSize
      },
      // 当前页发生改变事件
      currentChange(current) {
        this.page.currentPage = current
      },
      // 查询事件
      searchChange(form, done) {
        this.searchForm = form
        this.page.currentPage = 1
        this.getList(this.page, form)
        done()
      },
      // 刷新事件
      refreshChange() {
        this.getList(this.page)
      },
      //  导出excel
      exportExcel() {
        this.downBlobFile('/${moduleName}/${pathName}/export', this.searchForm, '${pathName}.xlsx')
      }
    }
  }
</script>
