/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 标本信息表，存储标本的基本信息
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:35:18
 */
@Data
@TableName("x_specimen")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标本信息表，存储标本的基本信息")
public class XSpecimenExcelVO extends Model<XSpecimenExcelVO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每条标本记录
     */
    @TableId(type = IdType.ASSIGN_ID)
	@ExcelProperty("id")
    private Integer id;

    /**
     * 标本的编号，可能为字母和数字的组合
     */
	@ExcelProperty("标本号/Specimen No.")
    private String specimenNo;

    /**
     * 标本的物种名称
     */
    @ExcelProperty("Species")
    private String speciesEn;

    /**
     * 标本的物种中文名称
     */
    @ExcelProperty("中名")
    private String speciesCn;

    /**
     * 标本所属的属名
     */
    @ExcelProperty("Genus")
    private String genusEn;

    /**
     * 标本中文所属的属名
     */
    @ExcelProperty("属名")
    private String genusCn;

    /**
     * 标本所属的科名
     */
    @ExcelProperty("Family")
    private String familyEn;

    /**
     * 标本中文所属的科名
     */
    @ExcelProperty("科名")
    private String familyCn;

    /**
     * 标本的采集地点
     */
    @ExcelProperty("采集地点")
    private String collectionPlace;

    /**
     * 英文采集地
     */
    @ExcelProperty("Place of Collection")
    private String placeEn;

    /**
     * 标本的采集日期
     */
    @ExcelProperty("采集日期/Collect Date")
    private String collectionTime;

    /**
     * 采集该标本的人员姓名
     */
    @ExcelProperty("采集人")
    private String collector;


	@ExcelProperty("Collector")
    private String collectorEn;

    /**
     * 该标本记录导入数据库的批次标识
     */
    @ExcelProperty("数据库导入批次")
    private String importBatch;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 语言类型zh中文en英文
     */
    @ExcelProperty("语言类型zh中文en英文")
    private String languageType;

}
