import { request } from '@/api/request'

/**
 * <PERSON>tch unique Dating Method values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getDatingMethods() {
    return request({
        url: '/admin/paleoecology/dating-methods',
        method: 'get'
    })
}

/**
 * Fetch unique Dating Quality values from the database
 * @returns {Promise} Promise object representing the API response
 */
export function getDatingQualities() {
    return request({
        url: '/admin/paleoecology/dating-qualities',
        method: 'get'
    })
}
