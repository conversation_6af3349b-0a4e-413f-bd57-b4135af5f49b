/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package com.tdkj.tdcloud.admin.api.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 标本信息表，存储标本的基本信息
 *
 * <AUTHOR> code generator
 * @date 2025-03-11 09:35:18
 */
@Data
@TableName("x_specimen")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "标本信息表，存储标本的基本信息")
public class XSpecimen extends Model<XSpecimen> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每条标本记录
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="自增主键，唯一标识每条标本记录")
    private Integer id;

    /**
     * 标本的编号，可能为字母和数字的组合
     */
    @Schema(description="标本的编号，可能为字母和数字的组合")
    private String specimenNo;

    /**
     * 标本对应的中文名称
     */
    @Schema(description="标本对应的中文名称")
    private String chineseName;

    /**
     * 标本的物种名称
     */
    @Schema(description="标本的物种名称")
    private String speciesEn;

    /**
     * 标本的物种中文名称
     */
    @Schema(description="标本的物种中文名称")
    private String speciesCn;

    /**
     * 标本所属的属名
     */
    @Schema(description="标本所属的属名")
    private String genusEn;

    /**
     * 标本中文所属的属名
     */
    @Schema(description="标本中文所属的属名")
    private String genusCn;

    /**
     * 标本所属的科名
     */
    @Schema(description="标本所属的科名")
    private String familyEn;

    /**
     * 标本中文所属的科名
     */
    @Schema(description="标本中文所属的科名")
    private String familyCn;

    /**
     * 标本的采集地点
     */
    @Schema(description="标本的采集地点")
    private String collectionPlace;

    /**
     * 英文采集地
     */
    @Schema(description="英文采集地")
    private String placeEn;

    /**
     * 标本的采集日期
     */
    @Schema(description="标本的采集日期")
    private String collectionTime;

    /**
     * 采集该标本的人员姓名
     */
    @Schema(description="采集该标本的人员姓名")
    private String collector;
    private String collectorEn;

    /**
     * 该标本记录导入数据库的批次标识
     */
    @Schema(description="该标本记录导入数据库的批次标识")
    private String importBatch;

    /**
     * 语言类型zh中文en英文
     */
    @Schema(description="语言类型zh中文en英文")
    private String languageType;

	private LocalDateTime createTime;

	@TableField(exist = false)
	private List<SysFile> upSysFileList = new ArrayList<>();

	@TableField(exist = false)
	private List<SysFile> lwSysFileList = new ArrayList<>();

}
