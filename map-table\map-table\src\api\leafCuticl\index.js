import {request} from '@/api/request'

//列表查询数据
export function getSpecimenList(query) {
    return request({
        url: '/admin/xspecimen/page',
        method: 'get',
        params: query
    })
}
//详情接口
export function getObj(id) {
    return request({
        url: '/admin/xspecimen/' + id,
        method: 'get'
    })
}

//左侧树
export function getXSpecimenTree(parentEn) {
    return request({
        url: '/admin/xspecimen/getXSpecimenTree',
        method: 'get',
        params: {
            parentEn
        }
    })
}