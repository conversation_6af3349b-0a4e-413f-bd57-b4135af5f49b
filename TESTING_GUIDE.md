# Paleoecology Data Migration Testing Guide

## Prerequisites

### Database Setup
Ensure MySQL database is running with:
- **Host**: `*************:3306` (or update in `application-dev.yml`)
- **Database**: `paleoecology_db`
- **Username**: `root`
- **Password**: `root`
- **Tables**: `Rupelian` and `Chattian` with data

### Redis Setup
Ensure Redis is running on:
- **Host**: `127.0.0.1:6379`
- **Database**: `9`

## Step 1: Start Java Spring Boot Backend

### Option A: Using Maven
```bash
cd paleoecologyDatabase/tdcloud-upms/tdcloud-upms-biz
mvn spring-boot:run
```

### Option B: Using JAR file
```bash
cd paleoecologyDatabase/tdcloud-upms/tdcloud-upms-biz
java -jar target/tdcloud-upms-biz.jar
```

### Expected Output
- Application should start on **port 8888**
- Context path: `/admin`
- Look for: "Started TdcloudAdminApplication"
- Swagger UI available at: `http://localhost:8888/admin/doc.html`

### Backend Health Check
Test these URLs in browser/Postman:
```
http://localhost:8888/admin/actuator/health
http://localhost:8888/admin/paleoecology/dating-methods
http://localhost:8888/admin/paleoecology/taxa/families
```

## Step 2: Start Vue.js Frontend

```bash
cd map-table/map-table
npm install  # if not already done
npm run serve
```

### Expected Output
- Frontend should start on **port 8181**
- Available at: `http://localhost:8181`
- Should show paleoecology database interface

## Step 3: Functional Testing

### 3.1 Search Panel Testing
**Test autocomplete suggestions:**
- [ ] Family field loads suggestions from `/taxa/families`
- [ ] Genus field loads suggestions from `/taxa/genera`
- [ ] Species field loads suggestions from `/taxa/species`
- [ ] Scientific Name field loads suggestions from `/taxa/scientific-names`
- [ ] Dating Method field loads suggestions from `/dating-methods`
- [ ] Dating Quality field loads suggestions from `/dating-qualities`
- [ ] Original Name field loads suggestions from `/taxa/original-names`
- [ ] Fossil Type field loads suggestions from `/fossil-types`
- [ ] Plant Organ field loads suggestions from `/plant-organs`

### 3.2 Basic Search Testing
**Test simple queries:**
- [ ] Search by Family (e.g., "Fagaceae")
- [ ] Search by Genus (e.g., "Quercus")
- [ ] Search by Country (e.g., "China")
- [ ] Search by Dating Method
- [ ] Verify results appear in both map and table views

### 3.3 Geographic Filtering Testing
**Test drawing tools:**
- [ ] Draw polygon on map
- [ ] Draw circle on map
- [ ] Draw rectangle on map
- [ ] Verify geographic filtering works
- [ ] Check that only points within shapes are returned

### 3.4 Detail Data Testing
**Test table expansion:**
- [ ] Click on table row to expand
- [ ] Verify detail data loads from `/detail` endpoint
- [ ] Check that Chattian table data displays correctly

### 3.5 Complex Query Testing
**Test combined filters:**
- [ ] Family + Geographic filter
- [ ] Time range + Taxa filter
- [ ] Multiple geographic shapes
- [ ] Age range filtering

## Step 4: Error Handling Testing

### 4.1 Network Error Testing
- [ ] Stop backend, verify frontend shows error messages
- [ ] Test with invalid search parameters
- [ ] Test with malformed geographic coordinates

### 4.2 Data Validation Testing
- [ ] Test empty search results
- [ ] Test invalid ID for detail endpoint
- [ ] Test malformed polygon coordinates

## Step 5: Performance Testing

### 5.1 Load Testing
- [ ] Test with large result sets
- [ ] Test complex geographic queries
- [ ] Monitor response times

### 5.2 Caching Testing
- [ ] Verify suggestion data is cached
- [ ] Test refresh functionality

## Common Issues and Solutions

### Backend Issues

**Issue**: Application won't start
**Solutions**:
- Check database connection in `application-dev.yml`
- Verify Redis is running
- Check port 8888 is not in use
- Review application logs for errors

**Issue**: Database connection failed
**Solutions**:
- Update database URL in `application-dev.yml`
- Verify MySQL is running and accessible
- Check username/password credentials

**Issue**: 404 errors on API endpoints
**Solutions**:
- Verify controller mapping: `/paleoecology/*`
- Check if `@RequestMapping` annotations are correct
- Ensure component scanning includes new controller

### Frontend Issues

**Issue**: API calls fail with CORS errors
**Solutions**:
- Add CORS configuration to Spring Boot
- Update `vue.config.js` proxy settings
- Check `config.js` API base URL

**Issue**: Autocomplete not working
**Solutions**:
- Check browser network tab for failed requests
- Verify API endpoints return data
- Check console for JavaScript errors

**Issue**: Geographic filtering not working
**Solutions**:
- Verify `GeographicUtils` class is working
- Check polygon/circle coordinate parsing
- Test point-in-polygon algorithms

## Debugging Tips

### Backend Debugging
1. Enable debug logging in `application-dev.yml`:
   ```yaml
   logging:
     level:
       com.tdkj.tdcloud.admin: debug
   ```

2. Check Swagger UI for API documentation:
   `http://localhost:8888/admin/doc.html`

3. Monitor database queries in logs

### Frontend Debugging
1. Open browser Developer Tools
2. Check Network tab for API calls
3. Check Console for JavaScript errors
4. Use Vue DevTools for component debugging

## Success Criteria

✅ **Backend Started Successfully**
- Application runs on port 8888
- All API endpoints respond correctly
- Database connections established

✅ **Frontend Started Successfully**
- Application runs on port 8181
- All components load without errors
- API calls succeed

✅ **Core Functionality Working**
- Search suggestions load
- Data queries return results
- Geographic filtering works
- Detail data displays correctly
- Map and table views function properly

✅ **Error Handling Working**
- Graceful error messages
- Retry mechanisms function
- Network failures handled properly

## Next Steps After Testing

1. **Fix any identified issues**
2. **Update configuration for production**
3. **Create deployment documentation**
4. **Set up monitoring and logging**
5. **Prepare user documentation**
