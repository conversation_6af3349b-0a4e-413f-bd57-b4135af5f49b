/*
 *    Copyright (c) 2018-2025, tdcloud All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: tdcloud
 */

package ${package}.${moduleName}.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tdkj.tdcloud.common.core.util.R;
import com.tdkj.tdcloud.common.log.annotation.SysLog;
import ${package}.${moduleName}.entity.${className};
import ${package}.${moduleName}.service.${className}Service;
import org.springframework.security.access.prepost.PreAuthorize;
import com.tdkj.tdcloud.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/${pathName}" )
@Tag(description = "${pathName}" , name = "${comments}管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ${className}Controller {

    private final  ${className}Service ${classname}Service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param ${classname} ${comments}
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_view')" )
    public R get${className}Page(Page page, ${className} ${classname}) {
        return R.ok(${classname}Service.page(page, Wrappers.query(${classname})));
    }


    /**
     * 通过id查询${comments}
     * @param ${pk.lowerAttrName} id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{${pk.lowerAttrName}}" )
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_view')" )
    public R getById(@PathVariable("${pk.lowerAttrName}" ) ${pk.attrType} ${pk.lowerAttrName}) {
        return R.ok(${classname}Service.getById(${pk.lowerAttrName}));
    }

    /**
     * 新增${comments}
     * @param ${classname} ${comments}
     * @return R
     */
    @Operation(summary = "新增${comments}" , description = "新增${comments}" )
    @SysLog("新增${comments}" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_add')" )
    public R save(@RequestBody ${className} ${classname}) {
        return R.ok(${classname}Service.save(${classname}));
    }

    /**
     * 修改${comments}
     * @param ${classname} ${comments}
     * @return R
     */
    @Operation(summary = "修改${comments}" , description = "修改${comments}" )
    @SysLog("修改${comments}" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_edit')" )
    public R updateById(@RequestBody ${className} ${classname}) {
        return R.ok(${classname}Service.updateById(${classname}));
    }

    /**
     * 通过id删除${comments}
     * @param ${pk.lowerAttrName} id
     * @return R
     */
    @Operation(summary = "通过id删除${comments}" , description = "通过id删除${comments}" )
    @SysLog("通过id删除${comments}" )
    @DeleteMapping("/{${pk.lowerAttrName}}" )
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_del')" )
    public R removeById(@PathVariable ${pk.attrType} ${pk.lowerAttrName}) {
        return R.ok(${classname}Service.removeById(${pk.lowerAttrName}));
    }


    /**
     * 导出excel 表格
     * @param ${classname} 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('${moduleName}_${pathName}_export')" )
    public List<${className}> export(${className} ${classname}) {
        return ${classname}Service.list(Wrappers.query(${classname}));
    }
}
