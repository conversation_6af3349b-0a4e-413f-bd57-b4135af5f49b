<template>
  <el-dialog width="70%"
          :title="!dataForm.id ? '新增' : '修改'"
          :close-on-click-modal="false"
          append-to-body
          :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="80px">
      <el-form-item label="语言类型" prop="languageType">
        <el-select v-model="dataForm.languageType" placeholder="语言类型" style="width: 100%;">
          <el-option label="中文" value="zh"></el-option>
          <el-option label="英文" value="en"></el-option>
        </el-select>
      </el-form-item>
    <el-form-item label="标题" prop="title">
      <el-input v-model="dataForm.title" placeholder="标题"></el-input>
    </el-form-item>
    <el-form-item label="内容" prop="content">
      <quill-editor ref="myQuillEditor" style="width: 100%;" :options="tl"
                    v-model="dataForm.content">
      </quill-editor>
    </el-form-item>
      <el-form-item label="视频封面" prop="sysFile">
        <singleFileUpload v-model="dataForm.sysFile" :file="fileList" @successUpload="successUpload"></singleFileUpload>
      </el-form-item>
      <el-form-item label="视频" prop="video">
        <videoUpload v-model="dataForm.video" :videoFile="fileVideoList" @successVideoUpload="successVideoUpload"></videoUpload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="canSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {getObj, addObj, putObj} from '@/api/xeducation/xeducation'
  import videoUpload from "@/components/upload/videoUpload";
  import singleFileUpload from "@/components/upload/singleFileUpload";
  import tl from '@/util/editor'
  export default {
    components: { videoUpload, singleFileUpload },
    data() {
      return {
        tl:tl,
        visible: false,
        canSubmit: false,
        fileList: [],
        fileVideoList: [],
        dataForm: {
          id: null,
          title: '',
          content: '',
          languageType: 'en',
          type: 'video',
          createTime: '',
          sysFile: {},
          video: {}
        },
        dataRule: {
          title: [
            {required: true, message: '标题不能为空', trigger: 'blur'}
          ],
          content: [
            {required: false, message: '内容不能为空', trigger: 'blur'}
          ],
          languageType: [
            {required: true, message: '语言类型不能为空', trigger: 'blur'}
          ],
          sysFile: [
            {required: true, message: '视频封面不能为空', trigger: 'change'}
          ],
          video: [
            {required: true, message: '视频不能为空', trigger: 'change'}
          ],
        }
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || null
        this.visible = true
        this.canSubmit = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            getObj(this.dataForm.id).then(response => {
              this.dataForm = response.data.data
              this.fileList = [response.data.data.sysFile]
              this.fileVideoList = [response.data.data.video]
            })
          }else {
            this.fileList = []
            this.fileVideoList = []
          }
        })
      },
      successVideoUpload(val) {
        this.dataForm.video = val
      },
      successUpload(val) {
        this.dataForm.sysFile = val ? val.data.data : {}
      },
      // 表单提交
      dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        let pic = !this.dataForm.sysFile || this.dataForm.sysFile === {} || this.dataForm.sysFile.url === undefined
        let video = !this.dataForm.video || this.dataForm.video === {} || this.dataForm.video.url === undefined
        if(pic) {
          this.$message.error("请上传封面图片！")
        }
        if(video) {
          this.$message.error("请上传视频！")
        }
          if (valid && !pic && !video) {
            this.canSubmit = false
            if (this.dataForm.id) {
              putObj(this.dataForm).then(data => {
                this.$notify.success('修改成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            } else {
              addObj(this.dataForm).then(data => {
                this.$notify.success('添加成功')
                this.visible = false
                this.$emit('refreshDataList')
              }).catch(() => {
                this.canSubmit = true
              })
            }
          }
        })
      }
    }
  }
</script>
<style>

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='10px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='10px']::before {
  content: '字号';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
  content: '12px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='18px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='18px']::before {
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  content: '20px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='22px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='22px']::before {
  content: '22px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='24px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='24px']::before {
  content: '24px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='36px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='36px']::before {
  content: '36px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='48px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='48px']::before {
  content: '48px';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='1.0']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='1.0']::before {
  content: '行高';
  font-size: 12px;
  width: 60px;
}
/*.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='1.0']::before ,*/
/*.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='1.0']::before {*/
/*  content: '1.0';*/
/*  font-size: 12px;*/
/*  width: 60px;*/
/*}*/
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='1.5']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='1.5']::before {
  content: '1.5';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='1.75']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='1.75']::before {
  content: '1.75';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='1.8']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='1.8']::before {
  content: '1.8';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='2.0']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='2.0']::before {
  content: '2.0';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='2.5']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='2.5']::before {
  content: '2.5';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='2.75']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='2.75']::before {
  content: '2.75';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='2.8']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='2.8']::before {
  content: '2.8';
  font-size: 12px;
  width: 60px;
}
.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value='3.0']::before ,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value='3.0']::before {
  content: '3.0';
  font-size: 12px;
  width: 60px;}

.ql-indentCustom .ql-picker-label[data-value="0em"]::before ,
.ql-indentCustom .ql-picker-item[data-value="0em"]::before {
  content: '首行缩进';
  font-size: 12px;
  width: 80px;
}
/*.ql-indentCustom .ql-picker-item[data-value="0em"]::before {*/
/*  content: '0em';*/
/*}*/
.ql-indentCustom .ql-picker-label[data-value="1em"]::before ,
.ql-indentCustom .ql-picker-item[data-value="1em"]::before {
  content: '1em';
  font-size: 12px;
  width: 80px;
}
.ql-indentCustom .ql-picker-label[data-value="2em"]::before ,
.ql-indentCustom .ql-picker-item[data-value="2em"]::before {
  content: '2em';
  font-size: 12px;
  width: 80px;
}

</style>
