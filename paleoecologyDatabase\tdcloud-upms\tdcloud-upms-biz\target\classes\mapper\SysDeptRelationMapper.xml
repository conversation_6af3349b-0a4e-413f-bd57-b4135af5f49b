<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, tdcloud All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: tdcloud
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tdkj.tdcloud.admin.mapper.SysDeptRelationMapper">

	<!-- 删除节点关系	-->
	<delete id="deleteDeptRelations">
		DELETE FROM sys_dept_relation
		WHERE
		descendant IN ( SELECT temp.descendant FROM
		( SELECT descendant FROM sys_dept_relation WHERE ancestor = #{descendant} ) temp )
		AND ancestor IN ( SELECT temp.ancestor FROM ( SELECT ancestor FROM
		sys_dept_relation WHERE descendant = #{descendant} AND ancestor != descendant ) temp )
	</delete>

	<!-- 新增节点关系	-->
	<insert id="insertDeptRelations">
		INSERT INTO sys_dept_relation (ancestor, descendant)
		SELECT a.ancestor, b.descendant
		FROM sys_dept_relation a
		CROSS JOIN sys_dept_relation b
		WHERE a.descendant = #{ancestor}
		AND b.ancestor = #{descendant}
	</insert>

	<!--删除部门 > 删除所有关联此部门子节点的闭包关系-->
	<delete id="deleteDeptRelationsByDeptId">
		DELETE
		FROM
		sys_dept_relation
		WHERE
		descendant IN (
		SELECT
		temp.descendant
		FROM
		(
		SELECT
		descendant
		FROM
		sys_dept_relation
		WHERE
		ancestor = #{id}
		) temp
		)
	</delete>
</mapper>
