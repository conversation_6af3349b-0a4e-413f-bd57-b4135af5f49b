<template>
  <div class="login-panle">
    <div class="login-panle-title">{{$t('nav.paleoecologyNav')}}</div>
    <el-form ref="loginForm" class="loginForm" :model="loginForm" :rules="loginRules">
      <div class="form_item">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" :placeholder="$t('login.emailPlaceholder')"></el-input>
        </el-form-item>
      </div>
      <div class="form_item">
        <el-form-item prop="password">
          <el-input
              auto-complete="off"
              v-model="loginForm.password"
              :type="passwordType"
              :placeholder="$t('login.passPlaceholder')">
            <i slot="suffix" class="el-icon-view el-input__icon icon" @click="showPassword"/>
            <i slot="prefix" class="icon-mima icon"></i>
          </el-input>
        </el-form-item>
      </div>
      <div class="forgot_password" @click="goToForgotPage">
        <div >{{ $t('login.forgotText')}}</div>
      </div>
      <div class="login-btns">
        <div class="btn cancel" @click="cancelHandle">{{ $t('login.cancelBtn')}}</div>
        <div class="btn login" @click="handleLogin">{{ $t('login.loginBoxBtn')}}</div>
      </div>
      <div class="register_link">
        <span>{{ $t('login.loginNote')}}</span>
        <div  @click="goToRegister">{{ $t('login.signIn')}}</div>
      </div>

    </el-form>
  </div>
</template>

<script>
import {rule} from "@/util/validateRules";
import {mapMutations} from "vuex";
export default {
  name: "LoginPanle",
  data() {
    return {
      passwordType: "password",
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: this.$t('login.emailPlaceholder'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {validator: rule.validatorPassword,trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    ...mapMutations(['SET_OPEN_LOGIN_BOX']),
    ...mapMutations(['SET_OPEN_REGISTER_BOX']),
    ...mapMutations(['SET_OPEN_FORGOT_BOX']),
    //取消按钮
    cancelHandle() {
      this.$refs.loginForm.resetFields();
      this.SET_OPEN_LOGIN_BOX(false);
    },
    showPassword() {
      this.passwordType === ""
          ? (this.passwordType = "password")
          : (this.passwordType = "");
    },
    //立即登录功能
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          // TODO 登录接口调用
          this.$store.dispatch('LoginByUsername', Object.assign({}, this.loginForm)).then(() => {
            // 2. 登录成功后获取用户信息
            return this.$store.dispatch('GetUserInfo');
          }) .then(() => {
            // 3. 跳转到首页
            // this.$router.push('/');
            location.reload()
            this.SET_OPEN_LOGIN_BOX(false)
          }).catch(error => {
            // console.log('000',error)
            // this.$message.error(error.message || '用户名或密码错误');
          })
        } else {

          return false
        }
      })
    },
    //跳转注册弹框
    goToRegister() {
      this.SET_OPEN_REGISTER_BOX(true)
      this.SET_OPEN_LOGIN_BOX(false)
    },
    //跳转忘记密码页面
    goToForgotPage() {
      this.SET_OPEN_LOGIN_BOX(false)
      this.SET_OPEN_FORGOT_BOX(true)

    },

  }
}
</script>

<style scoped lang="scss">
.login-panle {
  position: fixed;
  top: 200px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  width: 504px;
  height: 443px;
  background: #FFFFFF;
  border-radius: 6px ;
  .login-panle-title {
    margin-top: 40px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 32px;
    color: var(--primary-text-color);
    margin-bottom: 40px;
  }
  .loginForm {
    padding: 0 34px;
  }
  .form_item {
    margin-bottom: 20px;
  }

  .forgot_password {
    width: 150px;
    float: right;
    text-align: right;
    margin-bottom: 20px;
    margin-top: 20px;
    font-size: 15px;
    cursor: pointer;
    div {
      color: var(--primary-text-color);
      font-size: 15px;
      text-decoration: none;
    }
  }
  .login-btns {
    clear: both;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .btn {
      width: calc((100% - 10px) / 2);
      height: 50px;
      line-height: 50px;
      text-align: center;
      background: #D5E2E5;
      border-radius: 4px;
      color: var(--primary-text-color);
      cursor: pointer;
    }
    .login {
      background-color: var(--primary-text-color);
      color: #ffffff;
    }
  }

  .register_link {
    text-align: center;
    font-size: 15px;
     color: #86909C;

    div {
      display: inline-block;
      color: var(--primary-text-color);
      text-decoration: none;
      margin-left: 5px;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
::v-deep .el-input__inner {
  padding: 0 15px !important;
}
</style>