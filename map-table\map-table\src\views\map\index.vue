<template>
  <div class="content">
    <!--    <div class="cover">Paleoecology Database</div>-->
    <SearchPanel @search="updateSearchCriteria" @search-result-selected="loadSavedSearch"
      @clear-map-selections="clearMapSelections" @clear="handleClear" :boundingBoxes="boundingBoxes"
      :polygons="polygons" :circleCenters="circleCenters" :circleRadii="circleRadii" :searchHistory="searchHistory" />
    <div class="separator"></div>
    <main>
      <!--      <div class="search-results">-->
      <!--        Search Occurrences | 2309 Results-->
      <!--      </div>-->
      <div class="toggle-buttons">
        <button :class="[showMap ? 'active-line' : '']" @click="toggleView(true)">MAP</button>
        <button :class="[!showMap ? 'active-line' : '']" @click="toggleView(false)">TABLE</button>
      </div>
      <div v-if="showMap">
        <MapView ref="mapView" :searchCriteria="searchCriteria" :key="mapKey" @updateTableData="updateTableData"
          @shapes-updated="updateShapeData" />
      </div>
      <div v-else>
        <TableView :searchCriteria="searchCriteria" :tableData="tableData" :searchHistory="searchHistory"
          :key="tableKey" @search-result-selected="loadSavedSearch" />
      </div>
    </main>
  </div>
</template>
<script>
import MapView from '../../components/MapView.vue';
import SearchPanel from '../../components/SearchPanel.vue';
import TableView from '../../components/TableView.vue';
import 'leaflet/dist/leaflet.css';
import Vue from 'vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import { getPaleoecologyData } from '@/api/paleoecology';
Vue.use(ElementUI);

export default {
  name: 'MapIndex',
  components: {
    MapView,
    SearchPanel,
    TableView
  },
  data() {
    return {
      showMap: true,
      searchCriteria: {},
      mapKey: 0,
      tableKey: 0,
      boundingBoxes: [],
      polygons: [],
      circleCenters: [],
      circleRadii: [],
      tableData: [],
      searchHistory: [],
      savedDrawnShapes: null // New property to store drawn shapes
    };
  },
  methods: {
    updateSearchCriteria(criteria) {
      console.log('Received search criteria:', criteria);

      // Deep copy the criteria to avoid reference issues
      this.searchCriteria = JSON.parse(JSON.stringify(criteria));

      // Store the shape data from the criteria
      if (criteria.bboxes) {
        this.boundingBoxes = criteria.bboxes.split('|').filter(bbox => bbox.trim() !== '');
      }
      if (criteria.polygons) {
        this.polygons = criteria.polygons.split('|').filter(poly => poly.trim() !== '');
      }
      if (criteria.circle_centers) {
        this.circleCenters = criteria.circle_centers.split('|').filter(center => center.trim() !== '');
      }
      if (criteria.circle_radii) {
        this.circleRadii = criteria.circle_radii.split('|').filter(radius => radius.trim() !== '');
      }

      console.log('Updated searchCriteria:', this.searchCriteria);

      // Add search to history with a more descriptive name
      let searchName = `Search ${this.searchHistory.length + 1}`;

      // Check if this search includes map selection
      const hasMapSelection =
        (this.boundingBoxes && this.boundingBoxes.length > 0) ||
        (this.polygons && this.polygons.length > 0) ||
        (this.circleCenters && this.circleCenters.length > 0);

      if (hasMapSelection) {
        searchName = `Map Selection ${this.searchHistory.length + 1}`;
      }

      // Store the actual drawn shapes data if available
      let drawnShapesData = null;
      if (this.$refs.mapView) {
        drawnShapesData = this.$refs.mapView.getDrawnShapes();
      }

      // Create the search history item with full criteria and shape data
      const searchHistoryItem = {
        id: `search-${Date.now()}`,
        name: searchName,
        criteria: {
          ...this.searchCriteria,
          // Ensure shape data is included in criteria
          bboxes: this.boundingBoxes.join('|'),
          polygons: this.polygons.join('|'),
          circle_centers: this.circleCenters.join('|'),
          circle_radii: this.circleRadii.join('|')
        },
        drawnShapesData: drawnShapesData
      };

      // Add to search history
      this.searchHistory.push(searchHistoryItem);
      console.log('Added to search history:', searchHistoryItem);

      // Update map component key to force it to re-render with new criteria
      this.mapKey++;

      // If we're in TABLE view, fetch data directly
      if (!this.showMap) {
        this.fetchDataForTable();
      }
    },
    fetchDataForTable() {
      const requestParams = { ...this.searchCriteria };
      getPaleoecologyData(requestParams)
        .then(response => {
          // console.log('API response for TABLE view:', response.data);
          // Handle the R response wrapper structure
          let data = [];
          if (response.data && response.data.data && response.data.data.table1) {
            // Java Spring Boot R.ok(response) format
            data = response.data.data.table1;
          } else if (response.data && response.data.table1) {
            // Direct response format
            data = response.data.table1;
          } else if (Array.isArray(response.data)) {
            // Direct array format
            data = response.data;
          }
          // console.log('Processed data for table:', data);
          this.tableData = data;
          this.tableKey++; // Force TableView to re-render
        })
        .catch(error => {
          console.error('Error fetching data for TABLE view:', error);
        });
    },
    setBoundingBox(bbox) {
      this.searchCriteria.boundingBox = bbox;
    },
    updateTableData(data) {
      // console.log('Updating table data from MapView:', data);
      this.tableData = data;
      // Only increment tableKey if currently in table view to avoid re-rendering when on map 
      if (!this.showMap) {
        this.tableKey++;
      }
    },
    updateShapeData(shapeData) {
      console.log('Shapes updated:', shapeData);
      // Update the local shape data
      this.boundingBoxes = shapeData.boundingBoxes || [];
      this.polygons = shapeData.polygons || [];
      this.circleCenters = shapeData.circleCenters || [];
      this.circleRadii = shapeData.circleRadii || [];
    },
    toggleView(showMap) {
      if (this.showMap !== showMap) {
        // If switching from map to table, save the drawn shapes
        if (this.showMap && !showMap && this.$refs.mapView) {
          this.savedDrawnShapes = this.$refs.mapView.getDrawnShapes();
          // console.log('Saved drawn shapes:', this.savedDrawnShapes);
        }

        this.showMap = showMap;

        // If switching back to map and we have saved shapes, restore them after the component is mounted
        if (showMap && this.savedDrawnShapes) {
          // We need to use nextTick to ensure the map component is fully mounted
          this.$nextTick(() => {
            if (this.$refs.mapView) {
              console.log('Restoring drawn shapes:', this.savedDrawnShapes);
              this.$refs.mapView.restoreDrawnShapes(this.savedDrawnShapes);
            }
          });
        }
      }
    },
    loadSavedSearch(criteria) {
      // console.log('Loading saved search with criteria:', criteria);
      if (!criteria) return;

      // Find the search history item
      const historyItem = this.searchHistory.find(item =>
        item.criteria && JSON.stringify(item.criteria) === JSON.stringify(criteria));

      // Create a deep copy of the criteria to avoid reference issues
      this.searchCriteria = JSON.parse(JSON.stringify(criteria));

      // Update the shape data arrays
      if (criteria.bboxes) {
        this.boundingBoxes = criteria.bboxes.split('|').filter(bbox => bbox.trim() !== '');
      } else {
        this.boundingBoxes = [];
      }

      if (criteria.polygons) {
        this.polygons = criteria.polygons.split('|').filter(poly => poly.trim() !== '');
      } else {
        this.polygons = [];
      }

      if (criteria.circle_centers) {
        this.circleCenters = criteria.circle_centers.split('|').filter(center => center.trim() !== '');
      } else {
        this.circleCenters = [];
      }

      if (criteria.circle_radii) {
        this.circleRadii = criteria.circle_radii.split('|').filter(radius => radius.trim() !== '');
      } else {
        this.circleRadii = [];
      }

      // If we're switching to map view and have stored shapes, restore them
      if (this.showMap) {
        // Force map to re-render
        this.mapKey++;

        this.$nextTick(() => {
          if (this.$refs.mapView) {
            // The map will automatically restore shapes from criteria via the watch handler
            console.log('Map should restore shapes from criteria');
          }
        });
      }

      // Fetch data for the table view
      if (!this.showMap) {
        this.fetchDataForTable();
      }
    },
    clearMapSelections() {
      console.log('Clearing map selections');

      // Clear local shape data
      this.boundingBoxes = [];
      this.polygons = [];
      this.circleCenters = [];
      this.circleRadii = [];

      // Force map to re-render to clear selections
      this.mapKey++;

      // If we have a reference to the map view, clear its drawn items
      this.$nextTick(() => {
        if (this.$refs.mapView) {
          this.$refs.mapView.clearDrawnItems();
        }
      });
    },
    handleClear(criteria) {
      console.log('Handling clear event with criteria:', criteria);

      // Update the search criteria without adding to history
      this.searchCriteria = JSON.parse(JSON.stringify(criteria));

      // Clear local shape data
      this.boundingBoxes = [];
      this.polygons = [];
      this.circleCenters = [];
      this.circleRadii = [];

      // Force map to re-render
      this.mapKey++;

      // If we're in TABLE view, fetch data directly
      if (!this.showMap) {
        this.fetchDataForTable();
      }
    },
  }
}
</script>

<style scoped>
.content {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.search-results {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: #f4f4f2;
  text-align: center;
  color: #344952;
  height: 50px;
  font-size: 18px;
}

.toggle-buttons {
  display: flex;
  border-bottom: 1px solid #ccc;

}

button {
  padding: 5px 20px;
  border: none;
  cursor: pointer;
  color: #344952;
  background-color: #f5f5f5;
  font-size: 16px;
  border-radius: 0;
  height: 37px;
}

button:hover {
  color: #344952;
  background-color: #f5f5f5;
}

.active-line {
  border-bottom: 1px solid #344952;
}

.content {
  display: flex;

}

.cover {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 90;
  width: 100%;
  height: 380px;
  /*background-color: #fff;*/
  line-height: 380px;
  font-size: 36px;
  color: var(--light-text-color);
  background: url("../../assets/top-banner.png") no-repeat;
  background-position: center;
  background-size: cover;
}

main {
  margin-left: 275px;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  background-color: #f4f4f2;
  /*margin-top: 280px;*/
  overflow: hidden;
}

.separator {
  height: 1px;
  background-color: #ccc;
  margin: 10px 0;
}
</style>