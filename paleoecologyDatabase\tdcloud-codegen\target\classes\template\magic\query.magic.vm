import request;
import cn.hutool.json.JSONUtil;
import response;

// 基础查询语句
var queryDsl = db.${dsName}.table("${tableName}")

// 判断是否逻辑删除
#foreach($column in $columns)
    #if($column.columnName == "del_flag")
        queryDsl = queryDsl.where().eq("del_flag", 0)
    #end
#end

// 模糊查询
if (query != null) {
    var jsonObj = JSONUtil.parseObj(query)
    for (key in jsonObj.keySet()) {
        queryDsl.like(key, "%" + jsonObj.get(key) + "%")
    }
}

// 执行DSL 分页输出结果
return response.json(queryDsl.page())
